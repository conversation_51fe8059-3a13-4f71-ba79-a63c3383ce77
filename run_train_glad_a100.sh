# Optimized Training Script for Water Body Reconstruction
# Initialize Conda
source /mnt/storage/xiaozhen/miniconda3/bin/activate
# Activate the geospatial environment
conda activate geoai

#%Module load cuda/11.8

nvidia-smi

# Data paths (modify as needed)
INDEX_FILE="/mnt/storage/xiaozhen/Water_glad/Sample/samples.json"
MISSING_DB="/mnt/storage/xiaozhen/Water_glad/Sample/missing_db.npz"

# Configuration file
CONFIG="configs/config_v20.yaml"

# Launch distributed training using torchrun
echo "Config: ${CONFIG}"

python model_glad/train_glad.py \
    --config ${CONFIG} \
    --index_file ${INDEX_FILE} \
    --missing_db ${MISSING_DB}\
    --resume /mnt/storage/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_2/best.pt

echo "Training completed!"