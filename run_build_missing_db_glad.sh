#!/bin/bash
#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=16
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_amd
#SBATCH --qos=normal
#SBATCH --exclude=GPA-4-18,GPA-4-17
#SBATCH --time=1:00:00
#SBATCH --output=logs/missing_db.out
#SBATCH --mem=100G

# Usage: sbatch run_build_missing_db.sh <index_file.json> <output_db.npz> [workers]
# Example: sbatch run_build_missing_db.sh dataset_index.json missing_db.npz 16

set -euo pipefail

INDEX_FILE="/fossfs/xiaozhen/Sample/GLAD/samples.json"
OUTPUT_DB="/fossfs/xiaozhen/Sample/GLAD/missing_db.npz"

# Activate environment if needed (modify accordingly)
source /home/<USER>/miniconda/bin/activate
conda activate water

# Ensure log directory exists
mkdir -p logs

python data/export_missing_patterns.py "$INDEX_FILE" "$OUTPUT_DB" --min-ratio 0.05 --workers 32 --visualize