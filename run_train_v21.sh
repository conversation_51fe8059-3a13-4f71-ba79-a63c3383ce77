#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=l40s
#SBATCH --qos=gpu
#SBATCH --mem=80G
#SBATCH --gres=gpu:2
#SBATCH --time=100:00:00
#SBATCH --output=logs/train_v21.out
#SBATCH --mail-type=END
#SBATCH --mail-user=<EMAIL>

# Optimized Training Script for Water Body Reconstruction
# Initialize Conda
source /home/<USER>/miniconda/bin/activate
# Activate the geospatial environment
conda activate water

#%Module load cuda/11.8

nvidia-smi

# Detect available GPUs and set NUM_GPUS accordingly
AVAILABLE_GPUS=$(nvidia-smi --list-gpus | wc -l)
echo "Available GPUs: ${AVAILABLE_GPUS}"

# Set NumExpr threads
export NUMEXPR_MAX_THREADS=16

# Data paths (modify as needed)
# INDEX_FILE="/fossfs/xiaozhen/Sample/Sample4/samples.json"
# MISSING_DB="/fossfs/xiaozhen/Sample/Sample4/missing_db.npz"

INDEX_FILE="/lustre1/g/geog_geors/xiaozhen/Water/Sample/Sample4/samples.json"
MISSING_DB="/lustre1/g/geog_geors/xiaozhen/Water/Sample/Sample4/missing_db.npz"
# Configuration file
CONFIG="configs/config_v21.yaml"

# Launch distributed training using torchrun
echo "Config: ${CONFIG}"

# # PyTorch 2.0+ distributed launch (multi-GPU)
NUM_GPUS=${NUM_GPUS:-${AVAILABLE_GPUS}}  # Use available GPUs if not set
torchrun \
    --nproc_per_node=${NUM_GPUS} \
    --nnodes=1 \
    --node_rank=0 \
    --master_addr=localhost \
    --master_port=29500 \
    model/train_v21.py \
    --config ${CONFIG} \
    --index_file ${INDEX_FILE} \
    --missing_db ${MISSING_DB} \
    # --resume /lustre1/g/geog_geors/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_2/last.pt

echo "Training completed!"