{"cells": [{"cell_type": "code", "execution_count": 10, "id": "8532f465", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:evaluation.utils:Analyzing distributions for 1503 samples\n", "INFO:evaluation.utils:Loaded bin specifications from configs/config_v20.yaml\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:evaluation.utils:Histogram analysis saved to validation_samples/validation_histogram_analysis.png\n"]}, {"data": {"text/plain": ["'validation_samples/validation_histogram_analysis.png'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from evaluation.utils import analyze_sample_distributions\n", "import json\n", "\n", "# Load samples\n", "samples_file = \"/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/divide/validation_indices.json\"\n", "# samples_file = \"/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/train_indices.json\"\n", "# samples_file = \"/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/val_indices.json\"\n", "with open(samples_file, 'r') as f:\n", "    data = json.load(f)\n", "\n", "if 'no_missing_samples' in data:\n", "    samples = data['no_missing_samples']\n", "else:\n", "    samples = data\n", "\n", "analyze_sample_distributions(samples, \"./validation_samples\")"]}, {"cell_type": "code", "execution_count": 9, "id": "8af02b20", "metadata": {}, "outputs": [{"data": {"text/plain": ["6490"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["len(samples)"]}, {"cell_type": "code", "execution_count": 7, "id": "c02e3720", "metadata": {}, "outputs": [{"data": {"text/plain": ["6490"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(samples)"]}, {"cell_type": "code", "execution_count": 4, "id": "ab3fa6cc", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "string indices must be integers", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43manalyze_sample_distributions\u001b[49m\u001b[43m(\u001b[49m\u001b[43msamples\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m./validation_samples\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Water/evaluation/utils.py:60\u001b[0m, in \u001b[0;36manalyze_sample_distributions\u001b[0;34m(samples, output_dir, title_prefix)\u001b[0m\n\u001b[1;32m     57\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnalyzing distributions for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(samples)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m samples\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     59\u001b[0m \u001b[38;5;66;03m# Extract variables\u001b[39;00m\n\u001b[0;32m---> 60\u001b[0m water_frequencies \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmean_water_frequency\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     61\u001b[0m water_ratios \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwater_proportion\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     62\u001b[0m years \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124myear\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n", "File \u001b[0;32m~/Water/evaluation/utils.py:60\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m     57\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnalyzing distributions for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(samples)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m samples\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     59\u001b[0m \u001b[38;5;66;03m# Extract variables\u001b[39;00m\n\u001b[0;32m---> 60\u001b[0m water_frequencies \u001b[38;5;241m=\u001b[39m [\u001b[43ms\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mmean_water_frequency\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     61\u001b[0m water_ratios \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwater_proportion\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     62\u001b[0m years \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124myear\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n", "\u001b[0;31mTypeError\u001b[0m: string indices must be integers"]}], "source": ["analyze_sample_distributions(samples, \"./validation_samples\")"]}, {"cell_type": "code", "execution_count": null, "id": "220c64e8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "geoai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}