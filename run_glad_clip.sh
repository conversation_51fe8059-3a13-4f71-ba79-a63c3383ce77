#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=64
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_amd
#SBATCH --qos=normal
#SBATCH --time=100:00:00
#SBATCH --output=logs/clip_glad.out
#SBATCH --mem=700G
#SBATCH --mail-type=END
#SBATCH --mail-user=<EMAIL>

pwd
# Initialize Conda
source /home/<USER>/miniconda/bin/activate
# Activate the geospatial environment
conda activate geospatial

# 设置环境变量以优化性能
# 控制各种数值计算库的线程数，避免线程竞争
export OMP_NUM_THREADS=1        # OpenMP线程数
export OPENBLAS_NUM_THREADS=1   # OpenBLAS线程数
export MKL_NUM_THREADS=1        # Intel MKL线程数  
export NUMEXPR_NUM_THREADS=1    # NumExpr线程数

# 设置GDAL/GEOS优化参数（已在代码中设置，这里作为备份）
export GDAL_DISABLE_READDIR_ON_OPEN=YES
export GDAL_NUM_THREADS=64
export GDAL_CACHEMAX=32768      # 32GB缓存
export GDAL_SWATH_SIZE=512M

# 创建必要的目录
mkdir -p logs

# 运行GLAD数据裁剪
echo "开始处理GLAD数据..."
python data/clip_glad.py \
    --glad_dir /fossfs/xiaozhen/GLAD \
    --output_dir /fossfs/xiaozhen/Clip/GLAD \
    --processes 64

echo "GLAD数据处理完成！" 