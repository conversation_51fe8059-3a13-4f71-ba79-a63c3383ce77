我来分析这个模型的整体思路和维度流动。首先让我查看一下完整的模型结构。

Read file: model/swin_water_net_v6.py
现在让我查看模型的主要前向传播部分：

Read file: model/swin_water_net_v6.py
现在让我查看temporal sampling部分：

Ran tool
Ran tool
Read file: model/sampler.py
现在让我查看sampler的forward方法：

Read file: model/sampler.py
现在我已经对整个模型有了全面的了解。让我详细解释这个模型的整体思路和维度流动：

## 模型整体思路

这是一个基于Swin Transformer的视频水体修复网络（SwinWaterNet），主要用于从时间序列卫星图像中修复水体缺失区域。模型的核心思想是：

### 1. **多尺度时间建模**
- 输入：120帧的时间序列图像 `(B, 120, 2, 256, 256)`
- 通过注意力引导的金字塔采样，提取3个不同时间尺度的特征：
  - **短期**（24帧）：关注目标帧附近的局部细节
  - **中期**（16帧）：平衡局部和全局信息
  - **长期**（8帧）：捕获整个序列的全局趋势

### 2. **地理-时间条件注入**
- 结合地理信息（经纬度）和时间信息（年、月）
- 通过可学习的嵌入层将地理-时间信息注入到特征中

### 3. **多尺度编码-解码架构**
- **编码器**：基于Swin Transformer的多尺度特征提取
- **解码器**：渐进式上采样，输出分类logits（背景和水体）

## 维度流动详解

### **输入阶段**
```python
# 输入数据
batch = {
    'input_sequence': (B, 120, 2, 256, 256),  # 120帧时间序列
    'center_frame_idx': (B,),                 # 目标帧索引
    'tile_lon': (B,),                        # 经度
    'tile_lat': (B,),                        # 纬度  
    'year': (B,),                            # 年份
    'month': (B,),                           # 月份
    'occurrence': (B, 256, 256)              # 水体频率图
}
```

### **1. 时间采样阶段 (sampler.py)**

```python
# 输入: (B, 120, 2, 256, 256)
multi_scale_x = self.temporal_sampling(x, center_frame_idx, water_frequency)
# 输出: [(B, 24, 2, 256, 256), (B, 16, 2, 256, 256), (B, 8, 2, 256, 256)]
```

**采样策略**：
- 计算重要性分数：时间注意力 + 空间边缘检测 + 水体动态度
- 通过可学习的仿射变换层融合三种分数
- 为每个尺度应用不同的时间窗口策略

### **2. Patch Embedding阶段**

```python
# 对每个尺度进行patch embedding
for scale_x in multi_scale_x:  # (B, T, 2, 256, 256)
    x_embed = self.patch_embed(scale_x)
    # 输出: (B, T, N, D) 其中 N = (256/8)² = 1024, D = embed_dim
```

**维度变换**：
- `(B, T, 2, 256, 256)` → `(B, T, 1024, 96)`
- 每个8×8的patch被映射为96维特征

### **3. 多尺度时间注意力阶段**

```python
# 输入: [(B, 24, 1024, 96), (B, 16, 1024, 96), (B, 8, 1024, 96)]
weighted_embeddings = self.temporal_attention(multi_scale_embeddings, target_features, water_frequency)
# 输出: 加权后的多尺度特征
```

### **4. 编码器阶段 (swin_water_net_v6.py)**

```python
# 转换为编码器格式
for weighted_embed in weighted_embeddings:  # (B, T, 1024, 96)
    weighted_flat = weighted_embed.reshape(B, T*1024, 96)
    # 输出: (B, T*1024, 96)

# 多尺度编码
encoder_features, real_T_values = self.encoder(multi_scale_features, multi_scale_T, H_p, W_p)
# 输出: 多层特征，每层形状为 (B, L, D) 其中L和D随深度变化
```

### **5. 解码器阶段 (decoder.py)**

```python
# 输入: decoder_input (B, 96, 32, 32) - 最终编码特征
# 输入: decoder_features[:-1] - 跳跃连接特征
logits, confidence = self.decoder(decoder_input, decoder_features[:-1], None, water_frequency)
```

**解码器维度流动**：

```python
# 初始输入: (B, 96, 32, 32)

# 第一个上采样块
x = upsample_blocks[0](x)  # (B, 48, 64, 64)
# 跳跃连接处理
skip = features[-(i+1)]  # (B, L, C) 从编码器特征
skip_reshaped = skip.reshape(B, T, H_p, W_p, C)  # (B, T, 32, 32, C)
skip_spatial = skip_reshaped[:, t_idx]  # (B, 32, 32, C)
skip_spatial = skip_spatial.permute(0, 3, 1, 2)  # (B, C, 32, 32)
skip_upsampled = F.interpolate(skip_spatial, size=(64, 64))  # (B, C, 64, 64)
x = x + skip_gate * skip_upsampled

# 第二个上采样块
x = upsample_blocks[1](x)  # (B, 24, 128, 128)

# 第三个上采样块  
x = upsample_blocks[2](x)  # (B, 12, 256, 256)

# 最终输出
logits = self.final_conv(x)  # (B, 2, 256, 256) - 分类logits
confidence = self.confidence(x)  # (B, 1, 256, 256) - 置信度
```

### **6. 输出阶段**

```python
# 最终输出
return {
    'inpaint': {
        'logits': (B, 2, 256, 256),      # 背景和水体的分类logits
        'confidence': (B, 1, 256, 256)   # 预测置信度
    }
}
```

## 关键设计特点

### **1. 内存效率优化**
- 使用梯度检查点（gradient checkpointing）
- 可分离的时空注意力机制
- 渐进式上采样减少内存占用

### **2. 动态跳跃连接**
- 基于水体频率信息的动态门控机制
- 自适应调整跳跃连接的权重

### **3. 多尺度时间建模**
- 不同时间尺度捕获不同层次的时间依赖
- 注意力引导的帧选择策略

### **4. 地理-时间条件**
- 将地理和时间信息作为条件注入
- 增强模型对不同时空场景的适应性

这个模型通过巧妙的多尺度时间建模和条件注入，能够有效地从时间序列卫星图像中修复水体缺失区域，同时保持较高的内存效率和计算效率。