#!/bin/bash

# Activate the correct conda environment
source /mnt/storage/xiaozhen/miniconda3/bin/activate
conda activate geoai

# Check GPU availability
echo "Checking GPU availability..."
nvidia-smi

# Set data paths (modify these according to your setup)
INDEX_FILE="/mnt/storage/xiaozhen/Water/Sample/Sample4/samples.json"
MISSING_DB="/mnt/storage/xiaozhen/Water/Sample/Sample4/missing_db.npz"

# Configuration file
CONFIG="configs/config_v5.yaml"

echo "Config: ${CONFIG}"
echo "Data: ${INDEX_FILE}"
echo "Missing DB: ${MISSING_DB}"

# Single GPU training only
echo "Running single GPU training..."

# Get additional arguments (everything after the first argument)
EXTRA_ARGS="$@"

if [ ! -z "$EXTRA_ARGS" ]; then
    echo "Additional arguments: $EXTRA_ARGS"
fi

cd /home/<USER>/Water
python train_v5_1.py \
    --config ${CONFIG} \
    --index_file ${INDEX_FILE} \
    --missing_db ${MISSING_DB} \
    $EXTRA_ARGS \

echo "Training completed!" 