#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_gpu
#SBATCH --qos=gpu
#SBATCH --mem=300G
#SBATCH --gres=gpu:1
#SBATCH --time=100:00:00
#SBATCH --output=logs/predict.out
#SBATCH --mail-type=END
#SBATCH --mail-user=<EMAIL>

source /home/<USER>/miniconda/bin/activate
conda activate water

python prediction/workflow.py \
     /fossfs/xiaozhen/Clip/JRC4 \
    --config /home/<USER>/Water/configs/config_v20.yaml \
    --model /fossfs/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_3/epoch_45.pt \
    --output /fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_3 \
    --occurrence_raster /fossfs/xiaozhen/JRC_GSW/occurrence \
    --region 115 28 118 30 \
    --device cuda:0 \
    --batch_size 36 \
    --num_workers 4 \
    --max_memory_gb 4 \
    --time_range 1984-01-01 2022-01-01 \

