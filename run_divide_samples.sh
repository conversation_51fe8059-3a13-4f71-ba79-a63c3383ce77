#!/bin/bash

# 样本分割脚本
# 从原始样本中提取50%训练集和10%验证集，保持各特征均匀分布

echo "开始样本分割..."

# 激活conda环境
source /mnt/storage/xiaozhen/miniconda3/etc/profile.d/conda.sh
conda activate geoai

# 运行样本分割脚本
python evaluation/divide_samples.py \
    --input /mnt/storage/xiaozhen/Water/Sample/Sample4/samples.json \
    --output_dir /mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/divide \
    --config /home/<USER>/Water/configs/config_v20.yaml \
    --train_ratio 0.5 \
    --val_ratio 0.1

echo "样本分割完成！"
