# Optimized Training Script for Water Body Reconstruction
# Initialize Conda
source /mnt/storage/xiaozhen/miniconda3/bin/activate
# Activate the geospatial environment
conda activate geoai

#%Module load cuda/11.8

nvidia-smi

# Detect available GPUs and set NUM_GPUS accordingly
AVAILABLE_GPUS=$(nvidia-smi --list-gpus | wc -l)
echo "Available GPUs: ${AVAILABLE_GPUS}"

# Set NumExpr threads
export NUMEXPR_MAX_THREADS=16
export CUDA_VISIBLE_DEVICES=0  # Use GPU 0, change to 1 if you want to use GPU 1

# Data paths (modify as needed)
INDEX_FILE="/mnt/storage/xiaozhen/Water/Sample/Sample4/samples.json"
MISSING_DB="/mnt/storage/xiaozhen/Water/Sample/Sample4/missing_db.npz"

# Configuration file
CONFIG="configs/config_v21.yaml"

# Launch distributed training using torchrun
echo "Config: ${CONFIG}"

python model/train_v21.py \
    --config ${CONFIG} \
    --index_file ${INDEX_FILE} \
    --missing_db ${MISSING_DB}\
    # --resume /lustre1/g/geog_geors/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_2/last.pt

echo "Training completed!"