"""
Enhanced Spatial Mosaicking and TIF Restoration Module
====================================================

This module provides comprehensive functionality for:
1. Spatial mosaicking of NetCDF inference results
2. Restoration of complete geospatial TIF images for each time step
3. Handling of overlapping regions with intelligent merging strategies
4. Efficient parallel processing for large datasets
5. Quality validation and metadata preservation

Data Processing Flow:
- Input: NetCDF files with 0-100 probability values from inference
- Processing: Average overlapping probability values
- Output: TIF files with JRC encoding (1=land, 2=water, 255=nodata)

Author: Assistant
Date: 2025-08-16
"""

import os
import sys
import logging
import json
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from dataclasses import dataclass

import numpy as np
import pandas as pd
from tqdm import tqdm

# JRC data value definitions (fallback if data.dataset not available)
JRC_VALUES = {
    'no_data': 255,
    'land': 1,
    'water': 2,
    'no_observation': 0
}

# Geospatial imports
try:
    import rasterio
    from rasterio.transform import Affine
    from rasterio.windows import Window
    from rasterio.warp import calculate_default_transform, reproject, Resampling
    from rasterio.merge import merge
    import xarray as xr

    # Try to import JRC_VALUES from data.dataset, use fallback if not available
    try:
        from data.dataset import JRC_VALUES as _JRC_VALUES
        JRC_VALUES = _JRC_VALUES
    except ImportError:
        pass  # Use fallback definition above

    GEOSPATIAL_AVAILABLE = True
except ImportError as e:
    GEOSPATIAL_AVAILABLE = False
    # Delay logger usage until after it's defined
    _GEOSPATIAL_IMPORT_ERROR = str(e)

warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logger = logging.getLogger(__name__)

if not GEOSPATIAL_AVAILABLE:
    logger.warning(f"Geospatial libraries not available: {_GEOSPATIAL_IMPORT_ERROR}")

def convert_probability_to_jrc(prob_data: np.ndarray, threshold: float = 50.0) -> np.ndarray:
    """
    Convert 0-100 probability values to JRC encoding

    Args:
        prob_data: Array with 0-100 probability values (can contain NaN for nodata)
        threshold: Threshold for water classification (default: 50.0)

    Returns:
        Array with JRC encoding (1=land, 2=water, 255=nodata)
    """
    result = np.full(prob_data.shape, JRC_VALUES['no_data'], dtype=np.uint8)

    # Create mask for valid data (not NaN and not 255)
    valid_mask = ~np.isnan(prob_data) & (prob_data != 255)
    
    if np.any(valid_mask):
        # Convert probabilities to JRC encoding
        valid_data = prob_data[valid_mask]
        water_mask = valid_data >= threshold

        # Apply classifications
        result[valid_mask] = np.where(water_mask, JRC_VALUES['water'], JRC_VALUES['land'])
    return result

def convert_jrc_to_probability(jrc_data: np.ndarray) -> np.ndarray:
    water_mask = jrc_data == JRC_VALUES['water']
    land_mask = jrc_data == JRC_VALUES['land']
    nodata_mask = jrc_data == JRC_VALUES['no_data']

    prob_data = np.full(jrc_data.shape, np.nan, dtype=np.float32)
    prob_data[water_mask] = 100.0
    prob_data[land_mask] = 0.0
    prob_data[nodata_mask] = np.nan
    return prob_data

@dataclass
class MosaickingConfig:
    """Configuration for spatial mosaicking"""
    output_dir: str = "./restored_tifs"
    merge_method: str = "average"  # "average", "first", "last", "max"
    target_crs: str = "EPSG:4326"
    target_resolution: Optional[Tuple[float, float]] = None  # (x_res, y_res) in target CRS units
    compression: str = "lzw"
    nodata_value: int = 255  # JRC nodata value as int for uint8 compatibility
    tile_size: int = 256
    num_workers: int = 4
    include_occurrence: bool = True
    include_metadata: bool = True
    quality_validation: bool = True
    # Optional global occurrence raster aligned to target grid (no resampling expected)
    occurrence_raster: Optional[str] = None
    missing_threshold: float = 0.0


class SpatialMosaicker:
    """Enhanced spatial mosaicking engine for NetCDF results"""

    def __init__(self, config: MosaickingConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        if not GEOSPATIAL_AVAILABLE:
            raise ImportError("Geospatial libraries (rasterio, xarray) are required but not available")

        logger.info(f"Initialized SpatialMosaicker with output directory: {self.output_dir}")

    def extract_spatial_bounds_from_netcdf(self, nc_file: Path) -> Dict[str, Any]:
        """Extract spatial bounds and metadata from NetCDF file"""
        try:
            with xr.open_dataset(nc_file) as ds:
                # Get spatial dimensions
                spatial_info = {
                    'file_path': str(nc_file),
                    'idx_x_count': ds.dims.get('idx_x', 0),
                    'idx_y_count': ds.dims.get('idx_y', 0),
                    'time_count': ds.dims.get('time', 0)
                }

                # Extract coordinate information
                if 'window_transform' in ds.attrs:
                    transform = Affine(*ds.attrs['window_transform'])
                    spatial_info['transform'] = transform

                    # Calculate bounds
                    height = ds.dims['y'] if 'y' in ds.dims else self.config.tile_size
                    width = ds.dims['x'] if 'x' in ds.dims else self.config.tile_size

                    # Transform corner coordinates
                    left, top = transform * (0, 0)
                    right, bottom = transform * (width, height)

                    spatial_info['bounds'] = (left, bottom, right, top)  # (min_x, min_y, max_x, max_y)
                    spatial_info['crs'] = ds.attrs.get('crs', 'EPSG:4326')

                # Extract window coordinates (if available)
                if 'window_lon' in ds.attrs and 'window_lat' in ds.attrs:
                    spatial_info['window_lon'] = float(ds.attrs['window_lon'])
                    spatial_info['window_lat'] = float(ds.attrs['window_lat'])

                # Extract time information
                if 'time' in ds.coords:
                    spatial_info['time_coords'] = pd.to_datetime(ds.time.values)
                    spatial_info['time_range'] = (
                        spatial_info['time_coords'].min(),
                        spatial_info['time_coords'].max()
                    )

                return spatial_info

        except Exception as e:
            logger.error(f"Error extracting spatial bounds from {nc_file}: {e}")
            return {}

    def calculate_mosaic_bounds(self, nc_files: List[Path]) -> Dict[str, Any]:
        """Calculate overall bounds for the mosaic from all NetCDF files"""
        logger.info("Calculating mosaic bounds from NetCDF files...")

        all_bounds = []
        all_transforms = []
        time_ranges = []

        for nc_file in tqdm(nc_files, desc="Analyzing files"):
            spatial_info = self.extract_spatial_bounds_from_netcdf(nc_file)
            if spatial_info and 'bounds' in spatial_info:
                all_bounds.append(spatial_info['bounds'])
                all_transforms.append(spatial_info.get('transform'))
                if 'time_range' in spatial_info:
                    time_ranges.append(spatial_info['time_range'])

        if not all_bounds:
            raise ValueError("No valid spatial bounds found in NetCDF files")

        # Calculate overall bounds
        all_bounds = np.array(all_bounds)
        overall_bounds = (
            np.min(all_bounds[:, 0]),  # min_x
            np.min(all_bounds[:, 1]),  # min_y
            np.max(all_bounds[:, 2]),  # max_x
            np.max(all_bounds[:, 3])   # max_y
        )

        # Calculate time range
        overall_time_range = None
        if time_ranges:
            all_start_times = [tr[0] for tr in time_ranges]
            all_end_times = [tr[1] for tr in time_ranges]
            overall_time_range = (min(all_start_times), max(all_end_times))

        # Determine resolution from first valid transform
        resolution = None
        for transform in all_transforms:
            if transform is not None:
                resolution = (abs(transform.a), abs(transform.e))
                break

        mosaic_info = {
            'bounds': overall_bounds,
            'resolution': resolution or self.config.target_resolution,
            'time_range': overall_time_range,
            'file_count': len(nc_files),
            'target_crs': self.config.target_crs
        }

        logger.info(f"Mosaic bounds: {overall_bounds}")
        logger.info(f"Resolution: {resolution}")
        if overall_time_range:
            logger.info(f"Time range: {overall_time_range[0]} to {overall_time_range[1]}")

        return mosaic_info

    def create_mosaic_grid(self, bounds: Tuple[float, float, float, float],
                          resolution: Tuple[float, float]) -> Tuple[Affine, int, int]:
        """Create the target mosaic grid"""
        min_x, min_y, max_x, max_y = bounds
        x_res, y_res = resolution

        # Calculate dimensions
        width = int(np.ceil((max_x - min_x) / x_res))
        height = int(np.ceil((max_y - min_y) / y_res))

        # Create transform
        transform = Affine.translation(min_x, max_y) * Affine.scale(x_res, -y_res)

        logger.info(f"Created mosaic grid: {width} x {height} pixels")
        return transform, width, height

    def process_single_timestep(self, nc_files: List[Path], time_index: int,
                              mosaic_transform: Affine, mosaic_width: int, mosaic_height: int,
                              output_path: Path) -> Dict[str, Any]:
        """Process a single time step to create a mosaicked TIF"""
        try:
            # Initialize mosaic arrays - use consistent data types
            mosaic_data = np.full((mosaic_height, mosaic_width), np.nan, dtype=np.float32)
            weight_array = np.zeros((mosaic_height, mosaic_width), dtype=np.float32)

            # Inverse transform for coordinate conversion
            inv_mosaic_transform = ~mosaic_transform

            # Process each NetCDF file
            tiles_processed = 0
            for nc_file in nc_files:
                try:
                    with xr.open_dataset(nc_file) as ds:
                        # Check if time index is valid
                        if time_index >= ds.dims.get('time', 0):
                            continue

                        # Get file spatial information
                        spatial_info = self.extract_spatial_bounds_from_netcdf(nc_file)
                        if not spatial_info or 'transform' not in spatial_info:
                            continue

                        window_transform = spatial_info['transform']

                        # Process each spatial tile in the file
                        for idx_x in range(ds.dims.get('idx_x', 0)):
                            for idx_y in range(ds.dims.get('idx_y', 0)):
                                # Check if tile is valid first (using mean_water_frequency as indicator)
                                if 'mean_water_frequency' in ds.data_vars:
                                    mean_freq = ds.mean_water_frequency.isel(idx_x=idx_x, idx_y=idx_y).values
                                    if np.isnan(mean_freq):
                                        continue

                                # Read tile data for this time step using correct xarray indexing
                                try:
                                    tile_data = ds.data.isel(idx_x=idx_x, idx_y=idx_y, time=time_index).values
                                    missing_proportion = ds.missing_proportion.isel(idx_x=idx_x, idx_y=idx_y, time=time_index).values
                                except (IndexError, KeyError) as e:
                                    logger.debug(f"Failed to read tile data for idx_x={idx_x}, idx_y={idx_y}, time={time_index}: {e}")
                                    continue

                                if missing_proportion <= self.config.missing_threshold:
                                    tile_data = convert_jrc_to_probability(tile_data)
                                    
                                # Skip if all nodata
                                if np.all(np.isnan(tile_data)) or np.all(tile_data == 255):
                                    continue

                                # Get tile position information using correct xarray indexing
                                col_offset = ds.tile_col_offset.isel(idx_x=idx_x, idx_y=idx_y).values
                                row_offset = ds.tile_row_offset.isel(idx_x=idx_x, idx_y=idx_y).values

                                # Validate offsets
                                if np.isnan(col_offset) or np.isnan(row_offset):
                                    continue

                                # Coordinate transformation: tile offset -> geographic -> mosaic pixels
                                tile_height, tile_width = tile_data.shape

                                # Calculate tile position in geographic coordinates
                                geo_x, geo_y = window_transform * (col_offset, row_offset)

                                # Convert to mosaic pixel coordinates
                                out_col, out_row = inv_mosaic_transform * (geo_x, geo_y)
                                out_col, out_row = int(round(out_col)), int(round(out_row))

                                # Quick bounds check - allow partial overlap
                                if (out_col >= mosaic_width or out_row >= mosaic_height or
                                    out_col + tile_width <= 0 or out_row + tile_height <= 0):
                                    continue

                                # Calculate valid overlap region
                                start_col = max(0, out_col)
                                start_row = max(0, out_row)
                                end_col = min(mosaic_width, out_col + tile_width)
                                end_row = min(mosaic_height, out_row + tile_height)

                                # Skip if no valid overlap
                                if start_col >= end_col or start_row >= end_row:
                                    continue

                                # Calculate corresponding tile region
                                tile_start_col = start_col - out_col
                                tile_start_row = start_row - out_row
                                tile_end_col = tile_start_col + (end_col - start_col)
                                tile_end_row = tile_start_row + (end_row - start_row)

                                # Prepare tile data - handle 0-100 probability values
                                tile_region = tile_data[tile_start_row:tile_end_row, tile_start_col:tile_end_col].astype(np.float32, copy=True)
                                # Convert JRC nodata (255) to NaN for processing
                                tile_region[tile_region == 255] = np.nan

                                # Create valid data mask
                                valid_mask = ~np.isnan(tile_region)
                                if not np.any(valid_mask):
                                    continue

                                # Get mosaic and weight slices
                                mosaic_slice = mosaic_data[start_row:end_row, start_col:end_col]
                                weight_slice = weight_array[start_row:end_row, start_col:end_col]

                                # Apply merging strategy - optimized for performance
                                if self.config.merge_method == "average":
                                    # Optimized weighted average
                                    first_time_mask = (weight_slice == 0) & valid_mask
                                    overlap_mask = (weight_slice > 0) & valid_mask

                                    # Handle first-time pixels
                                    if np.any(first_time_mask):
                                        mosaic_slice[first_time_mask] = tile_region[first_time_mask]
                                        weight_slice[first_time_mask] = 1.0

                                    # Handle overlapping pixels
                                    if np.any(overlap_mask):
                                        mosaic_slice[overlap_mask] = (
                                            (mosaic_slice[overlap_mask] * weight_slice[overlap_mask] +
                                             tile_region[overlap_mask]) / (weight_slice[overlap_mask] + 1.0)
                                        )
                                        weight_slice[overlap_mask] += 1.0

                                elif self.config.merge_method == "first":
                                    # First valid value wins
                                    first_time_mask = np.isnan(mosaic_slice) & valid_mask
                                    mosaic_slice[first_time_mask] = tile_region[first_time_mask]

                                elif self.config.merge_method == "last":
                                    # Last valid value wins
                                    mosaic_slice[valid_mask] = tile_region[valid_mask]

                                elif self.config.merge_method == "max":
                                    # Maximum value wins
                                    update_mask = valid_mask & (np.isnan(mosaic_slice) | (tile_region > mosaic_slice))
                                    mosaic_slice[update_mask] = tile_region[update_mask]

                                tiles_processed += 1

                except Exception as e:
                    logger.warning(f"Error processing {nc_file} at time {time_index}: {e}")
                    continue

            # Quick coverage check
            valid_pixels = np.sum(~np.isnan(mosaic_data))
            total_pixels = mosaic_data.size
            coverage_percentage = (valid_pixels / total_pixels) * 100

            logger.info(f"Time {time_index}: processed {tiles_processed} tiles, "
                       f"coverage: {coverage_percentage:.2f}% ({valid_pixels}/{total_pixels} pixels)")

            # Convert 0-100 probability values to JRC encoding
            final_data = convert_probability_to_jrc(mosaic_data, threshold=50.0)

            # Simplified occurrence override
            if self.config.include_occurrence and self.config.occurrence_raster:
                try:
                    final_data = self._apply_occurrence_override(
                        final_data, mosaic_transform, mosaic_width, mosaic_height, self.config.occurrence_raster
                    )
                    logger.debug(f"Applied occurrence override for time {time_index}")
                except Exception as e:
                    logger.warning(f"Occurrence override failed for time {time_index}: {e}")

            # Update profile for uint8 output
            profile = {
                'driver': 'GTiff',
                'height': mosaic_height,
                'width': mosaic_width,
                'count': 1,
                'dtype': 'uint8',  # Ensure uint8 for JRC encoding
                'crs': self.config.target_crs,
                'transform': mosaic_transform,
                'nodata': int(JRC_VALUES['no_data']),  # Ensure nodata is int for uint8
                'compress': self.config.compression,
                'tiled': True,
                'blockxsize': min(self.config.tile_size, mosaic_width),
                'blockysize': min(self.config.tile_size, mosaic_height)
            }

            with rasterio.open(output_path, 'w', **profile) as dst:
                dst.write(final_data, 1)

                # Add metadata
                if self.config.include_metadata:
                    dst.update_tags(
                        created_by='SpatialMosaicker',
                        time_index=str(time_index),
                        tiles_processed=str(tiles_processed),
                        merge_method=self.config.merge_method,
                        source_files=str(len(nc_files)),
                        occurrence_override='applied' if (self.config.include_occurrence and self.config.occurrence_raster) else 'none'
                    )

            logger.info(f"Created mosaic for time {time_index}: {output_path}")

            return {
                'time_index': time_index,
                'output_path': str(output_path),
                'tiles_processed': tiles_processed,
                'valid_pixels': np.sum(final_data != JRC_VALUES['no_data']),  # Use final_data for accurate count
                'water_pixels': np.sum(final_data == JRC_VALUES['water']),
                'land_pixels': np.sum(final_data == JRC_VALUES['land']),
                'file_size_mb': output_path.stat().st_size / (1024*1024) if output_path.exists() else 0
            }

        except Exception as e:
            logger.error(f"Error processing time step {time_index}: {e}")
            return {'time_index': time_index, 'error': str(e)}

    def create_temporal_mosaic_series(self, nc_files: List[Path],
                                    time_range: Optional[Tuple[str, str]] = None,
                                    region_bounds: Optional[Tuple[float, float, float, float]] = None) -> Dict[str, Any]:
        """Create a complete time series of mosaicked TIF images"""
        logger.info("Creating temporal mosaic series...")

        # Calculate mosaic bounds and grid
        mosaic_info = self.calculate_mosaic_bounds(nc_files)

        # If region bounds provided, clip mosaic bounds to region intersection
        if region_bounds is not None and 'bounds' in mosaic_info:
            minx, miny, maxx, maxy = mosaic_info['bounds']
            rminx, rminy, rmaxx, rmaxy = region_bounds
            imin = max(minx, rminx)
            jmin = max(miny, rminy)
            imax = min(maxx, rmaxx)
            jmax = min(maxy, rmaxy)
            if imin < imax and jmin < jmax:
                logger.info(f"Clipping mosaic bounds {mosaic_info['bounds']} to region {region_bounds}")
                mosaic_info['bounds'] = (imin, jmin, imax, jmax)
            else:
                logger.warning(f"Region {region_bounds} does not intersect computed bounds {mosaic_info['bounds']}; using computed bounds")

        transform, width, height = self.create_mosaic_grid(
            mosaic_info['bounds'],
            mosaic_info['resolution']
        )

        # Determine time steps to process
        time_indices = []
        time_coords = None

        # Get time information from first valid file
        for nc_file in nc_files:
            try:
                with xr.open_dataset(nc_file) as ds:
                    if 'time' in ds.coords:
                        time_coords = pd.to_datetime(ds.time.values)
                        break
            except:
                continue

        if time_coords is None:
            logger.error("No valid time coordinates found")
            return {}

        # Filter by time range if specified
        if time_range:
            start_time, end_time = pd.to_datetime(time_range[0]), pd.to_datetime(time_range[1])
            valid_times = (time_coords >= start_time) & (time_coords <= end_time)
            time_indices = np.where(valid_times)[0].tolist()
            filtered_times = time_coords[valid_times]
        else:
            time_indices = list(range(len(time_coords)))
            filtered_times = time_coords

        logger.info(f"Processing {len(time_indices)} time steps")

        # Process each time step
        results = []

        if self.config.num_workers > 1:
            # Parallel processing
            with ProcessPoolExecutor(max_workers=self.config.num_workers) as executor:
                futures = []

                for i, time_idx in enumerate(time_indices):
                    timestamp = filtered_times[i] if i < len(filtered_times) else time_coords[time_idx]
                    output_filename = f"mosaic_{timestamp.strftime('%Y%m%d_%H%M')}.tif"
                    output_path = self.output_dir / output_filename

                    future = executor.submit(
                        self.process_single_timestep,
                        nc_files, time_idx, transform, width, height, output_path
                    )
                    futures.append(future)

                # Collect results
                for future in tqdm(as_completed(futures), total=len(futures), desc="Processing time steps"):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Error in parallel processing: {e}")
        else:
            # Sequential processing
            for i, time_idx in enumerate(tqdm(time_indices, desc="Processing time steps")):
                timestamp = filtered_times[i] if i < len(filtered_times) else time_coords[time_idx]
                output_filename = f"mosaic_{timestamp.strftime('%Y%m%d_%H%M')}.tif"
                output_path = self.output_dir / output_filename

                result = self.process_single_timestep(
                    nc_files, time_idx, transform, width, height, output_path
                )
                results.append(result)

        # Generate summary
        successful_results = [r for r in results if 'error' not in r]
        total_tiles = sum(r['tiles_processed'] for r in successful_results)
        total_pixels = sum(r['valid_pixels'] for r in successful_results)
        total_size_mb = sum(r['file_size_mb'] for r in successful_results)

        summary = {
            'mosaic_info': mosaic_info,
            'time_steps_processed': len(successful_results),
            'time_steps_failed': len(results) - len(successful_results),
            'total_tiles_processed': total_tiles,
            'total_valid_pixels': total_pixels,
            'total_output_size_mb': total_size_mb,
            'output_directory': str(self.output_dir),
            'merge_method': self.config.merge_method,
            'processing_results': results
        }

        # Save summary
        summary_path = self.output_dir / "mosaic_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        logger.info(f"Temporal mosaic series completed:")
        logger.info(f"  Processed: {len(successful_results)}/{len(results)} time steps")
        logger.info(f"  Total tiles: {total_tiles}")
        logger.info(f"  Total size: {total_size_mb:.1f} MB")
        logger.info(f"  Output directory: {self.output_dir}")

        return summary

    # def _apply_occurrence_override(self, final_data: np.ndarray, mosaic_transform: Affine,
    #                              mosaic_width: int, mosaic_height: int) -> np.ndarray:
    #     """
    #     Deprecated:
    #     Apply occurrence override using vectorized operations for better performance
    #     """
    #     try:
    #         import rasterio
    #         from rasterio.warp import reproject, Resampling
    #         import glob
    #         import os

    #         occ_path = self.config.occurrence_raster
    #         if not occ_path:
    #             return final_data

    #         # Get mosaic bounds
    #         minx = mosaic_transform.c
    #         maxy = mosaic_transform.f
    #         maxx = minx + mosaic_width * mosaic_transform.a
    #         miny = maxy + mosaic_height * mosaic_transform.e  # e is negative

    #         # Find overlapping occurrence files
    #         occ_files = []
    #         if os.path.isdir(occ_path):
    #             occ_files = glob.glob(os.path.join(occ_path, '*.tif'))
    #         elif os.path.isfile(occ_path):
    #             occ_files = [occ_path]
    #         else:
    #             logger.warning(f"Occurrence path not found: {occ_path}")
    #             return final_data

    #         # Use vectorized approach with reproject for better performance
    #         for occ_file in occ_files:
    #             try:
    #                 with rasterio.open(occ_file) as src:
    #                     # Check if files intersect
    #                     if (src.bounds.right <= minx or src.bounds.left >= maxx or
    #                         src.bounds.top <= miny or src.bounds.bottom >= maxy):
    #                         continue

    #                     # Create aligned occurrence array using reproject
    #                     aligned_occ = np.full((mosaic_height, mosaic_width), 255, dtype=np.uint8)

    #                     reproject(
    #                         source=rasterio.band(src, 1),
    #                         destination=aligned_occ,
    #                         src_transform=src.transform,
    #                         src_crs=src.crs,
    #                         dst_transform=mosaic_transform,
    #                         dst_crs=self.config.target_crs,
    #                         resampling=Resampling.nearest,
    #                         src_nodata=255,
    #                         dst_nodata=255
    #                     )

    #                     # Apply occurrence rules using vectorized operations
    #                     valid_mosaic = final_data != JRC_VALUES['no_data']
    #                     water_override = (aligned_occ == 100) & valid_mosaic
    #                     land_override = (aligned_occ == 0) & valid_mosaic

    #                     final_data[water_override] = JRC_VALUES['water']
    #                     final_data[land_override] = JRC_VALUES['land']

    #                     logger.debug(f"Applied occurrence override from {occ_file}: "
    #                                f"{np.sum(water_override)} water pixels, {np.sum(land_override)} land pixels")

    #             except Exception as e:
    #                 logger.debug(f"Error processing occurrence file {occ_file}: {e}")
    #                 continue

    #         return final_data

    #     except Exception as e:
    #         logger.error(f"Error in occurrence override: {e}")
    #         return final_data


    def _apply_occurrence_override(self, mosaic_data, mosaic_transform, mosaic_width, mosaic_height, tol=1e-9):
        import rasterio
        from rasterio.windows import from_bounds, Window
        import glob
        import os

        occ_path = self.config.occurrence_raster
        if not occ_path:
            return mosaic_data

        # Get mosaic bounds (只计算一次)
        xres_m = mosaic_transform.a
        yres_m = mosaic_transform.e  # 注意：e 通常是负数，表示从上到下 y 坐标递减
        minx = mosaic_transform.c
        maxy = mosaic_transform.f
        maxx = minx + mosaic_width * xres_m
        miny = maxy + mosaic_height * yres_m  # yres_m 是负数，所以这样计算是正确的

        # Find overlapping occurrence files
        occ_files = []
        if os.path.isdir(occ_path):
            occ_files = glob.glob(os.path.join(occ_path, '*.tif'))
        elif os.path.isfile(occ_path):
            occ_files = [occ_path]
        else:
            logger.warning(f"Occurrence path not found: {occ_path}")
            return mosaic_data

        # Debug: print mosaic bounds
        logger.debug(f"Mosaic bounds: minx={minx}, miny={miny}, maxx={maxx}, maxy={maxy}")

        # Process all overlapping files
        for occ_file in occ_files:
            with rasterio.open(occ_file) as src:
                logger.debug(f"Checking occurrence file {occ_file}")
                logger.debug(f"  Occurrence bounds: left={src.bounds.left}, bottom={src.bounds.bottom}, "
                           f"right={src.bounds.right}, top={src.bounds.top}")

                if (src.bounds.right <= minx or src.bounds.left >= maxx or
                    src.bounds.top <= miny or src.bounds.bottom >= maxy):
                    logger.debug(f"  Skipping {occ_file}: no overlap with mosaic")
                    continue

                logger.info(f"Processing occurrence file {occ_file}")
                # 校验 CRS/分辨率
                # if str(src.crs) != self.config.target_crs:
                #     raise ValueError(f"Occurrence CRS {src.crs} != target CRS {self.config.target_crs}")
                # xres_occ, yres_occ = src.transform.a, src.transform.e
                # if abs(xres_occ - xres_m) > tol or abs(yres_occ - yres_m) > tol:
                #     raise ValueError(f"Occurrence resolution {xres_occ},{yres_occ} != mosaic {xres_m},{yres_m}")
                
                # 计算交集
                ixmin = max(minx, src.bounds.left)
                iymin = max(miny, src.bounds.bottom)
                ixmax = min(maxx, src.bounds.right)
                iymax = min(maxy, src.bounds.top)
                if ixmin >= ixmax or iymin >= iymax:
                    continue  # 改为 continue，不是 return False
                
                # 计算窗口
                win_occ = from_bounds(ixmin, iymin, ixmax, iymax, transform=src.transform)
                col0 = (ixmin - minx) / xres_m
                row0 = (maxy - iymax) / yres_m
                w = (ixmax - ixmin) / xres_m
                h = (iymax - iymin) / abs(yres_m)
                
                # 像元对齐检查
                # if any(abs(v - round(v)) > tol for v in [win_occ.col_off, win_occ.row_off, win_occ.width, win_occ.height, col0, row0, w, h]):
                #     raise ValueError("Occurrence window not pixel-aligned; would require resampling")
                
                win_occ_i = Window(int(round(win_occ.col_off)), int(round(win_occ.row_off)), int(round(win_occ.width)), int(round(win_occ.height)))
                col0_i, row0_i, w_i, h_i = int(round(col0)), int(round(row0)), int(round(w)), int(round(h))
                occ_arr = src.read(1, window=win_occ_i)
                
                # 应用覆盖
                '''
                occ_data的阈值与data/clip.py保持统一，将clip.py没有处理的tile（以空值表示）进行赋值
                适当扩大occ_data的阈值，从而让部分推理问题得到修正
                '''
                water_mask = (occ_arr >= 95)
                land_mask = (occ_arr <= 5)
                mosaic_data[row0_i:row0_i+h_i, col0_i:col0_i+w_i][water_mask] = JRC_VALUES['water']  # 去掉 float()
                mosaic_data[row0_i:row0_i+h_i, col0_i:col0_i+w_i][land_mask] = JRC_VALUES['land']   # 去掉 float()
                
        return mosaic_data  # 在循环外返回

class EnhancedNetCDFToTIFRestorer:
    """Enhanced restoration engine that combines the original restorer with spatial mosaicking"""

    def __init__(self, netcdf_dir: str, output_dir: str = "restored_tifs"):
        self.netcdf_dir = Path(netcdf_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Initialized EnhancedNetCDFToTIFRestorer")
        logger.info(f"  Input directory: {self.netcdf_dir}")
        logger.info(f"  Output directory: {self.output_dir}")

    def restore_all(self, regions: Optional[Dict[str, Tuple[float, float, float, float]]] = None,
                   time_range: Optional[Tuple[str, str]] = None,
                   include_occurrence: bool = True,
                   num_workers: int = 4,
                   occurrence_raster: Optional[str] = None,
                   **mosaicking_kwargs) -> Dict[str, Any]:
        """
        Complete restoration workflow with spatial mosaicking

        Args:
            regions: Dictionary of region names to bounds (min_lon, min_lat, max_lon, max_lat)
            time_range: Time range to process (start_date, end_date)
            include_occurrence: Whether to include occurrence data in output
            num_workers: Number of parallel workers
            **mosaicking_kwargs: Additional arguments for MosaickingConfig

        Returns:
            Restoration results summary
        """
        logger.info("Starting enhanced NetCDF to TIF restoration workflow")

        # Find all NetCDF files
        nc_files = list(self.netcdf_dir.glob("*.nc"))
        if not nc_files:
            raise ValueError(f"No NetCDF files found in {self.netcdf_dir}")

        logger.info(f"Found {len(nc_files)} NetCDF files")

        # Process each region or all files if no regions specified
        if regions is None:
            regions = {"global": None}

        all_results = {} 

        for region_name, region_bounds in regions.items():
            logger.info(f"Processing region: {region_name}")

            # Filter files for this region
            if region_bounds:
                # Lazy import to decouple from geospatial imports and ensure availability
                try:
                    from prediction.inference import find_nc_files as _find_nc_files
                except ImportError as e:
                    raise ImportError(f"Unable to import find_nc_files from prediction.inference: {e}")
                region_files = _find_nc_files(str(self.netcdf_dir), region_bounds)
            else:
                region_files = nc_files

            if not region_files:
                logger.warning(f"No files found for region {region_name}")
                continue

            # Create region-specific output directory
            region_output_dir = self.output_dir / region_name
            region_output_dir.mkdir(parents=True, exist_ok=True)

            # Configure spatial mosaicking
            mosaic_config = MosaickingConfig(
                output_dir=region_output_dir,
                num_workers=num_workers,
                include_occurrence=include_occurrence,
                occurrence_raster=occurrence_raster,
                **mosaicking_kwargs
            )

            # Create mosaicker and process
            mosaicker = SpatialMosaicker(mosaic_config)
            region_results = mosaicker.create_temporal_mosaic_series(region_files, time_range, region_bounds)

            all_results[region_name] = region_results

        # Create overall summary
        total_time_steps = sum(r.get('time_steps_processed', 0) for r in all_results.values())
        total_size_mb = sum(r.get('total_output_size_mb', 0) for r in all_results.values())

        overall_summary = {
            'regions_processed': len(all_results),
            'total_time_steps': total_time_steps,
            'total_output_size_mb': total_size_mb,
            'output_directory': str(self.output_dir),
            'time_range': time_range,
            'region_results': all_results
        }

        # Save overall summary
        summary_path = self.output_dir / "restoration_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(overall_summary, f, indent=2, default=str)

        logger.info("Enhanced restoration workflow completed")
        logger.info(f"  Regions processed: {len(all_results)}")
        logger.info(f"  Total time steps: {total_time_steps}")
        logger.info(f"  Total output size: {total_size_mb:.1f} MB")

        return overall_summary


def main():
    """Main entry point for spatial mosaicking and TIF restoration"""

    # Configuration parameters (previously command line arguments)
    netcdf_dir = "/mnt/storage/xiaozhen/Water/Predictions/swin_waternet_v20_2/inpainted_netcdf"
    output_dir = "/mnt/storage/xiaozhen/Water/Predictions/swin_waternet_v20_2"
    occurrence_raster = "/mnt/storage/xiaozhen/Water/Clip/occurrence"
    regions_file = None  # Set to JSON file path if needed
    time_range = ("2020-09-01", "2020-09-01")
    merge_method = "average"
    num_workers = 1
    compression = "lzw"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('spatial_mosaicking.log'),
            logging.StreamHandler()
        ]
    )

    # Load regions if specified
    regions = None
    if regions_file:
        with open(regions_file, 'r') as f:
            regions = json.load(f)

    # Create restorer and run workflow
    restorer = EnhancedNetCDFToTIFRestorer(netcdf_dir, output_dir)

    results = restorer.restore_all(
        regions=regions,
        time_range=time_range,
        occurrence_raster=occurrence_raster,
        num_workers=num_workers,
        merge_method=merge_method,
        compression=compression
    )

    print("\nRestoration Results:")
    print(json.dumps(results, indent=2, default=str))


if __name__ == "__main__":
    main()