"""
NetCDF数据集推理模块 - 优化版本

主要功能：
1. 高效读取NetCDF数据集
2. 批量并行推理缺失区域
3. 保存推理结果到NetCDF

优化特性：
- 异步IO读取
- 批量处理tiles
- GPU并行推理
- 内存优化管理
- 进度监控
"""

import os
import shutil
import numpy as np
import pandas as pd
import xarray as xr
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from collections import OrderedDict

from tqdm import tqdm
import logging
from netCDF4 import Dataset as NcDataset

import time
import gc
import warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count
import re
import random
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
warnings.filterwarnings('ignore')

# Add project root to path
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from model.model_v20 import create_swin_water_net
from data.dataset import DataProcessor, MODEL_VALUES, JRC_VALUES
from configs import get_config, Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def _scan_single_file(nc_file: Path, missing_threshold: float, time_range: Optional[Tuple[str, str]] = None) -> List[Dict[str, Any]]:
    """
    扫描单个NetCDF文件的辅助函数 - 用于多进程处理

    Args:
        nc_file: NetCDF文件路径
        missing_threshold: 缺失数据阈值
        time_range: 时间范围过滤 (start_date, end_date)

    Returns:
        该文件中所有需要推理的tile样本列表
    """
    file_samples = []

    try:
        with xr.open_dataset(nc_file, decode_times=True, mask_and_scale=False, cache=False) as ds:
            # 快速预检查：如果没有mean_water_frequency或全为NaN，跳过
            if 'mean_water_frequency' not in ds.data_vars:
                return file_samples

            mean_freq_array = ds.mean_water_frequency.values
            if np.all(np.isnan(mean_freq_array)) or np.all(mean_freq_array == 0) or np.all(mean_freq_array == 1):
                return file_samples

            # 找到所有非NaN的位置
            valid_mask = ~np.isnan(mean_freq_array)
            valid_indices = np.where(valid_mask)

            if len(valid_indices[0]) == 0:
                return file_samples

            # 时间范围过滤
            time_indices_to_process = None
            if time_range is not None:
                try:
                    # 获取时间坐标
                    time_coords = pd.to_datetime(ds.time.values)
                    start_time, end_time = pd.to_datetime(time_range[0]), pd.to_datetime(time_range[1])

                    # 找到在时间范围内的时间索引
                    valid_time_mask = (time_coords >= start_time) & (time_coords <= end_time)
                    time_indices_to_process = np.where(valid_time_mask)[0]

                    if len(time_indices_to_process) == 0:
                        logger.debug(f"No time points in range {time_range} for file {nc_file.name}")
                        return file_samples

                    logger.debug(f"File {nc_file.name}: filtering to {len(time_indices_to_process)}/{len(time_coords)} time points")
                except Exception as e:
                    logger.warning(f"Error applying time filter to {nc_file}: {e}, processing all time points")
                    time_indices_to_process = None

            # 批量读取missing_proportion数据
            missing_proportion_data = ds.missing_proportion.values

            # 向量化处理所有有效tiles
            for idx_x, idx_y in zip(valid_indices[0], valid_indices[1]):
                missing_ratios = missing_proportion_data[idx_x, idx_y, :]

                # 找到所有满足条件的时间步
                valid_time_mask = missing_ratios > missing_threshold
                valid_time_indices = np.where(valid_time_mask)[0]

                # 如果指定了时间范围，进一步过滤时间索引
                if time_indices_to_process is not None:
                    # 只保留在时间范围内的时间索引
                    valid_time_indices = np.intersect1d(valid_time_indices, time_indices_to_process)

                # 批量添加样本
                for time_idx in valid_time_indices:
                    file_samples.append({
                        'nc_file': str(nc_file),
                        'idx_x': int(idx_x),
                        'idx_y': int(idx_y),
                        'center_time_idx': int(time_idx),
                        'missing_ratio': float(missing_ratios[time_idx])
                    })

    except Exception as e:
        logger.error(f"Error scanning file {nc_file}: {e}")

    return file_samples

def extract_coords_from_filename(filename: str) -> Optional[Tuple[float, float]]:
    """从NetCDF文件名直接解析经纬度坐标"""
    try:
        # 文件名格式: JRC_window_{lon}_{lat}.nc
        basename = Path(filename).stem
        if basename.startswith('JRC_window_'):
            parts = basename.split('_')
            if len(parts) >= 4:
                lon_str = parts[2]
                lat_str = parts[3]
                return float(lon_str), float(lat_str)
        elif basename.startswith('inpainted_'):
            parts = basename.split('_')
            if len(parts) >= 5:
                lon_str = parts[3]
                lat_str = parts[4]
                return float(lon_str), float(lat_str)
        else:
            logger.error(f"Unknown filename format: {filename}")
            return None
    except Exception as e:
        logger.debug(f"Could not extract coordinates from filename {filename}: {e}")
    return None

def find_nc_files(nc_dir: str, region_bounds: Optional[Tuple[float, float, float, float]] = None) -> List[Path]:
    """查找NetCDF文件，直接从文件名读取经纬度"""
    nc_files = list(Path(nc_dir).glob("*.nc"))

    if region_bounds is None:
        return nc_files

    # 根据区域边界过滤文件
    min_lon, min_lat, max_lon, max_lat = region_bounds
    selected_files = []

    for nc_file in nc_files:
        # 直接从文件名解析经纬度
        coords = extract_coords_from_filename(str(nc_file))
        if coords is None:
            logger.debug(f"Could not parse coordinates from filename: {nc_file.name}")
            continue

        file_lon, file_lat = coords
        if min_lon <= file_lon <= max_lon and min_lat <= file_lat <= max_lat:
            selected_files.append(nc_file)
            logger.debug(f"File {nc_file.name} is within region bounds: {file_lon}, {file_lat}")

    logger.info(f"Found {len(selected_files)} NetCDF files in region")
    return selected_files

class InferenceDataset(Dataset):
    """推理数据集，用于管理NetCDF文件中需要推理的tiles"""

    def __init__(self, nc_files: List[Path], window_size: int = 48, missing_threshold: float = 0.0,
                 scan_method: str = 'auto', time_range: Optional[Tuple[str, str]] = None,
                 ds_cache_capacity: int = 2, occ_cache_capacity: int = 500):
        """
        初始化推理数据集

        Args:
            nc_files: NetCDF文件列表
            window_size: 滑动窗口大小
            missing_threshold: 缺失数据阈值，只处理缺失比例大于此值的tiles
            scan_method: 扫描方法 ('auto', 'serial', 'parallel')
                - 'auto': 根据文件数量自动选择最优方法
                - 'serial': 串行扫描（原始方法）
                - 'parallel': 多进程并行扫描
            time_range: 时间范围过滤 (start_date, end_date)
        """
        self.nc_files = nc_files
        self.window_size = window_size
        self.missing_threshold = missing_threshold
        self.time_range = time_range
        self.data_processor = DataProcessor()

        # 每worker内的 xarray Dataset 句柄LRU缓存与 occ 缓存
        self._ds_cache: "OrderedDict[str, xr.Dataset]" = OrderedDict()
        self._ds_cache_capacity = int(ds_cache_capacity)
        self._occ_cache: "OrderedDict[Tuple[str,int,int], np.ndarray]" = OrderedDict()
        self._occ_cache_capacity = int(occ_cache_capacity)

        # 扫描所有需要推理的tiles
        self.tile_samples = []

        # 根据扫描方法选择合适的扫描函数
        if scan_method == 'auto':
            if len(nc_files) > 10:
                scan_method = 'parallel'
            else:
                scan_method = 'serial'

        logger.info(f"Using {scan_method} scanning method for {len(nc_files)} files")

        if scan_method == 'parallel':
            self._scan_tiles_parallel()
        else:
            self._scan_tiles()

        logger.info(f"Initialized InferenceDataset with {len(self.tile_samples)} tile samples")

    def _scan_tiles_parallel(self):
        """并行扫描所有NetCDF文件 - 高性能版本"""
        if len(self.nc_files) <= 2:
            # 文件数量少时直接使用串行版本
            return self._scan_tiles()

        logger.info(f"Starting parallel scanning of {len(self.nc_files)} NetCDF files")

        # 使用多进程并行处理文件
        max_workers = min(cpu_count() - 1, len(self.nc_files), 8)  # 限制最大进程数

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有文件的扫描任务
            future_to_file = {
                executor.submit(_scan_single_file, nc_file, self.missing_threshold, self.time_range): nc_file
                for nc_file in self.nc_files
            }

            # 收集结果
            for future in tqdm(as_completed(future_to_file),
                             total=len(self.nc_files),
                             desc="Scanning NetCDF files"):
                nc_file = future_to_file[future]
                try:
                    file_samples = future.result()
                    self.tile_samples.extend(file_samples)
                except Exception as e:
                    logger.error(f"Error scanning file {nc_file}: {e}")
                    continue

    def _scan_tiles(self):
        """扫描所有NetCDF文件，找到需要推理的tiles - 优化版本"""
        for nc_file in self.nc_files:
            try:
                with xr.open_dataset(nc_file, decode_times=True, mask_and_scale=False, cache=True) as ds:
                    # 获取mean_water_frequency来判断哪些tiles是有效的
                    mean_freq_array = ds.mean_water_frequency.values  # shape: (idx_x, idx_y)

                    # 找到所有非NaN的位置，这些位置需要推理
                    valid_mask = ~np.isnan(mean_freq_array)
                    valid_indices = np.where(valid_mask)

                    if len(valid_indices[0]) == 0:
                        continue  # 没有有效的tiles，跳过这个文件

                    # 时间范围过滤
                    time_indices_to_process = None
                    if self.time_range is not None:
                        try:
                            # 获取时间坐标
                            time_coords = pd.to_datetime(ds.time.values)
                            start_time, end_time = pd.to_datetime(self.time_range[0]), pd.to_datetime(self.time_range[1])

                            # 找到在时间范围内的时间索引
                            valid_time_mask = (time_coords >= start_time) & (time_coords <= end_time)
                            time_indices_to_process = np.where(valid_time_mask)[0]

                            if len(time_indices_to_process) == 0:
                                logger.debug(f"No time points in range {self.time_range} for file {nc_file.name}")
                                continue

                            logger.debug(f"File {nc_file.name}: filtering to {len(time_indices_to_process)}/{len(time_coords)} time points")
                        except Exception as e:
                            logger.warning(f"Error applying time filter to {nc_file}: {e}, processing all time points")
                            time_indices_to_process = None

                    # 批量读取所有有效tiles的missing_proportion数据
                    valid_coords = list(zip(valid_indices[0], valid_indices[1]))

                    # 使用向量化操作批量处理
                    missing_proportion_data = ds.missing_proportion.values  # shape: (idx_x, idx_y, time)

                    for idx_x, idx_y in valid_coords:
                        # 获取该tile的所有时间步的missing_ratio
                        missing_ratios = missing_proportion_data[idx_x, idx_y, :]

                        # 向量化找到所有满足条件的时间步
                        valid_time_mask = missing_ratios > self.missing_threshold
                        valid_time_indices = np.where(valid_time_mask)[0]

                        # 如果指定了时间范围，进一步过滤时间索引
                        if time_indices_to_process is not None:
                            # 只保留在时间范围内的时间索引
                            valid_time_indices = np.intersect1d(valid_time_indices, time_indices_to_process)

                        # 批量添加所有满足条件的样本
                        for time_idx in valid_time_indices:
                            self.tile_samples.append({
                                'nc_file': str(nc_file),
                                'idx_x': int(idx_x),
                                'idx_y': int(idx_y),
                                'center_time_idx': int(time_idx),
                                'missing_ratio': float(missing_ratios[time_idx])
                            })

            except Exception as e:
                logger.error(f"Error scanning file {nc_file}: {e}")
                continue

    def __len__(self) -> int:
        return len(self.tile_samples)

    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取单个样本数据

        Args:
            idx: 样本索引

        Returns:
            包含推理所需数据的字典
        """
        sample_info = self.tile_samples[idx]

        try:
            return self._load_sample_data(sample_info)
        except Exception as e:
            logger.error(f"Error loading sample {idx}: {e}")
            return self._create_dummy_sample()

    def _get_dataset_handle(self, nc_file: str) -> xr.Dataset:
        """每worker内LRU缓存的 Dataset 句柄，避免每样本反复打开/关闭文件。"""
        ds = self._ds_cache.get(nc_file)
        if ds is not None:
            # LRU: recent
            self._ds_cache.move_to_end(nc_file)
            return ds
        # 打开新句柄
        ds = xr.open_dataset(nc_file, decode_times=False, mask_and_scale=False, cache=True)
        self._ds_cache[nc_file] = ds
        # 淘汰旧句柄
        if len(self._ds_cache) > self._ds_cache_capacity:
            old_key, old_ds = self._ds_cache.popitem(last=False)
            try:
                old_ds.close()
            except Exception:
                pass
        return ds

    def _get_occ_cached(self, nc_file: str, ds: xr.Dataset, idx_x: int, idx_y: int) -> np.ndarray:
        key = (nc_file, int(idx_x), int(idx_y))
        occ = self._occ_cache.get(key)
        if occ is not None:
            self._occ_cache.move_to_end(key)
            return occ
        occ = ds.occ_data.isel(idx_x=idx_x, idx_y=idx_y).values
        self._occ_cache[key] = occ
        if len(self._occ_cache) > self._occ_cache_capacity:
            self._occ_cache.popitem(last=False)
        return occ

    def _load_sample_data(self, sample_info: Dict[str, Any]) -> Dict[str, Any]:
        """加载单个样本的数据（仅按需读取时间窗口，避免整条time维度全量读取；缓存句柄与occ）"""
        nc_file = sample_info['nc_file']
        idx_x = sample_info['idx_x']
        idx_y = sample_info['idx_y']
        center_time_idx = sample_info['center_time_idx']

        ds = self._get_dataset_handle(nc_file)
        # 仅按需读取时间窗口数据，避免全量 .values
        T = int(ds.sizes['time'])
        half_seq = self.window_size // 2
        start_time = max(0, center_time_idx - half_seq)
        end_time = min(T, center_time_idx + half_seq)

        if end_time - start_time < self.window_size:
            if start_time == 0:
                end_time = min(T, self.window_size)
            else:
                start_time = max(0, end_time - self.window_size)

        window_data = ds.data.isel(idx_x=idx_x, idx_y=idx_y, time=slice(start_time, end_time)).values
        center_frame_idx = center_time_idx - start_time

        # occurrence 2D，按tile缓存
        occ_data = self._get_occ_cached(nc_file, ds, idx_x, idx_y)

        # 预处理数据
        processed_window_data, window_missing_mask = self.data_processor.preprocess_jrc_data_with_mask(window_data)
        normalized_occ = self.data_processor.normalize_occurrence_data(occ_data)

        # 构建三通道输入数据
        water_channel = (processed_window_data == MODEL_VALUES['water']).astype(np.float32)
        missing_channel = (processed_window_data == MODEL_VALUES['missing']).astype(np.float32)

        # 计算SDF通道
        T, H, W = processed_window_data.shape
        sdf_channel = np.zeros((T, H, W), dtype=np.float32)

        for t in range(T):
            # Only compute SDF for frames with valid data (not all missing)
            frame_water = water_channel[t]
            frame_missing = missing_channel[t]

            # Skip frames that are entirely missing
            if np.sum(1 - frame_missing) > 0:  # Has some valid pixels
                # Create mask for valid pixels only
                valid_mask = (1 - frame_missing).astype(bool)

                if np.sum(frame_water[valid_mask]) > 0 and np.sum(1 - frame_water[valid_mask]) > 0:
                    # Has both water and land pixels, compute SDF
                    sdf_channel[t] = self.data_processor.compute_sdf(frame_water)
                    # Set SDF to 0 for missing pixels
                    sdf_channel[t][~valid_mask] = 0.0

        three_channel_data = np.stack([water_channel, missing_channel, sdf_channel], axis=1)  # (T, 3, H, W)

        return {
            'input_sequence': torch.from_numpy(three_channel_data).float(),
            'occurrence': torch.from_numpy(normalized_occ).float(),
            'center_frame_idx': torch.tensor(center_frame_idx).long(),
            'missing_mask': torch.from_numpy(window_missing_mask[center_frame_idx]).bool(),
            'sample_info': sample_info,
        }

    def _create_sliding_window_data(self, full_data: np.ndarray,
                                   center_time_idx: int, window_size: int) -> Tuple[np.ndarray, int]:
        """创建滑动窗口数据，参考dataset.py的边界处理逻辑"""
        time_size = full_data.shape[0]

        # 计算以center_time_idx为中心的时间窗口边界
        half_seq = window_size // 2
        start_time = max(0, center_time_idx - half_seq)
        end_time = min(time_size, center_time_idx + half_seq)

        # 确保我们得到确切的window_size帧
        if end_time - start_time < window_size:
            if start_time == 0:
                end_time = min(time_size, window_size)
            else:
                start_time = max(0, end_time - window_size)

        # 提取窗口数据
        window_data = full_data[start_time:end_time]

        # 计算窗口内的中心帧索引
        center_frame_idx = center_time_idx - start_time
        assert 0 <= center_frame_idx < window_size, f"Invalid center frame index: {center_frame_idx}"

        return window_data, center_frame_idx

    def _create_dummy_sample(self) -> Dict[str, Any]:
        """创建虚拟样本用于错误恢复"""
        T, H, W = self.window_size, 256, 256

        return {
            'input_sequence': torch.zeros(T, 2, H, W).float(),
            'occurrence': torch.zeros(H, W).float(),
            'center_frame_idx': torch.tensor(0).long(),
            'missing_mask': torch.zeros(H, W).bool(),
            'sample_info': {'nc_file': '', 'idx_x': 0, 'idx_y': 0, 'center_time_idx': 0},
        }


class InferenceVisualizationModule:
    """推理过程中的可视化模块"""

    def __init__(self, output_dir: Path, enabled: bool = True):
        """
        初始化可视化模块

        Args:
            output_dir: 输出目录
            enabled: 是否启用可视化
        """
        self.output_dir = Path(output_dir)
        self.enabled = enabled
        self.visualization_dir = self.output_dir / "visualization"

        if self.enabled:
            self.visualization_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Visualization module initialized, output dir: {self.visualization_dir}")

    def visualize_first_batch(self, batch: Dict[str, Any], predictions: np.ndarray,
                            num_samples: int = 5) -> None:
        """
        可视化第一个batch中的随机样本

        Args:
            batch: 批次数据
            predictions: 模型预测结果 (B, H, W) - 0-100概率值
            num_samples: 可视化样本数量
        """
        if not self.enabled:
            return

        try:
            batch_size = predictions.shape[0]
            actual_num_samples = min(num_samples, batch_size)

            # 随机选择样本索引
            sample_indices = random.sample(range(batch_size), actual_num_samples)

            # 创建子图 - 4列：输入、预测、occurrence、从NC文件读取的结果
            fig, axes = plt.subplots(actual_num_samples, 4, figsize=(16, 4 * actual_num_samples))
            if actual_num_samples == 1:
                axes = axes.reshape(1, -1)

            fig.suptitle('First Batch Inference Visualization with NC Verification', fontsize=16)

            # 创建颜色映射（参考evaluation/visualization）
            colors_yellow_blue = [(1, 1, 0), (0, 0, 1)]
            n_bins = 100
            yellow_blue_cmap = LinearSegmentedColormap.from_list("custom_yb", colors_yellow_blue, N=n_bins)

            for i, sample_idx in enumerate(sample_indices):
                self._visualize_single_sample(axes[i], batch, predictions, sample_idx, yellow_blue_cmap, self.output_dir)

            # 保存图像
            plt.tight_layout()
            save_path = self.visualization_dir / "first_batch_visualization.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"First batch visualization saved to: {save_path}")

        except Exception as e:
            logger.error(f"Error in first batch visualization: {e}")
            plt.close('all')

    def _visualize_single_sample(self, axes, batch: Dict[str, Any], predictions: np.ndarray,
                               sample_idx: int, yellow_blue_cmap, output_dir: Path) -> None:
        """
        可视化单个样本（参考evaluation/visualization风格）

        Args:
            axes: matplotlib轴对象
            batch: 批次数据
            predictions: 预测结果
            sample_idx: 样本索引
            yellow_blue_cmap: 颜色映射
            output_dir: 输出目录，用于查找保存的NC文件
        """
        # 提取数据
        input_seq = batch['input_sequence'][sample_idx].cpu().numpy()  # (T, 2, H, W)
        occurrence = batch['occurrence'][sample_idx].cpu().numpy()  # (H, W)
        center_frame_idx = batch['center_frame_idx'][sample_idx].item()
        prediction = predictions[sample_idx] / 100.0  # 转换为0-1概率

        # 获取中心帧的输入数据
        center_input = input_seq[center_frame_idx]  # (2, H, W)

        # 准备输入可视化（参考evaluation/visualization的prepare_input_visualization）
        input_vis = self._prepare_input_visualization(center_input)

        # 获取样本信息
        if isinstance(batch['sample_info'], list):
            sample_info = batch['sample_info'][sample_idx]
        else:
            sample_info = {k: v[sample_idx] if isinstance(v, (list, torch.Tensor)) else v
                          for k, v in batch['sample_info'].items()}

        nc_file = Path(sample_info['nc_file']).name
        idx_x, idx_y = sample_info['idx_x'], sample_info['idx_y']
        center_time = sample_info['center_time_idx']

        # 1. 输入图像（参考evaluation/visualization的风格）
        input_rgb = np.ones((input_vis.shape[0], input_vis.shape[1], 3))
        water_mask = input_vis[:, :, 0] > 0.5  # 水体掩码
        missing_mask = input_vis[:, :, 2] > 0.5  # 缺失掩码
        input_rgb[water_mask] = [0, 0, 1]  # 蓝色表示水体
        input_rgb[missing_mask] = [0.7, 0.7, 0.7]  # 灰色表示缺失

        axes[0].imshow(input_rgb)
        title = f'Sample {sample_idx} - Input\n{nc_file}\nTile({idx_x},{idx_y}) - Time({center_time})'
        axes[0].set_title(title, fontsize=10)
        axes[0].axis('off')

        # 2. 预测结果（使用黄蓝色映射）
        im1 = axes[1].imshow(prediction, cmap=yellow_blue_cmap, vmin=0, vmax=1)
        axes[1].set_title('Prediction')
        axes[1].axis('off')
        plt.colorbar(im1, ax=axes[1], fraction=0.046)

        # 3. 水体出现频率（使用viridis色映射）
        im2 = axes[2].imshow(occurrence, cmap='viridis', vmin=0, vmax=1)
        axes[2].set_title('Water Occurrence')
        axes[2].axis('off')
        plt.colorbar(im2, ax=axes[2], fraction=0.046)

        # 4. 从保存的NC文件读取的结果（用于验证）
        nc_result = self._read_nc_result(output_dir, sample_info, center_time)
        if nc_result is not None:
            im3 = axes[3].imshow(nc_result, cmap=yellow_blue_cmap, vmin=0, vmax=1)
            axes[3].set_title('NC File Result\n(Verification)')
            axes[3].axis('off')
            plt.colorbar(im3, ax=axes[3], fraction=0.046)

            # 计算差异
            diff = np.abs(prediction - nc_result)
            max_diff = np.max(diff)
            mean_diff = np.mean(diff)
            axes[3].text(0.02, 0.98, f'Max Diff: {max_diff:.4f}\nMean Diff: {mean_diff:.4f}',
                        transform=axes[3].transAxes, va='top', ha='left',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                        fontsize=8)
        else:
            # 如果无法读取NC文件，显示提示
            axes[3].text(0.5, 0.5, 'NC file not found\nor not yet saved',
                        transform=axes[3].transAxes, ha='center', va='center',
                        fontsize=12, color='red')
            axes[3].set_title('NC File Result\n(Not Available)')
            axes[3].axis('off')

    def _prepare_input_visualization(self, input_tensor: np.ndarray) -> np.ndarray:
        """
        准备输入可视化数据（参考evaluation/visualization）

        Args:
            input_tensor: 输入张量 (2, H, W) - [water_channel, missing_channel]

        Returns:
            可视化数组 (H, W, 3)
        """
        vis = np.zeros((3, *input_tensor.shape[1:]), dtype=np.float32)
        vis[0] = input_tensor[0]  # 水体通道
        vis[1] = 1 - input_tensor[0]  # 非水体通道
        vis[2] = input_tensor[1]  # 缺失通道
        return vis.transpose(1, 2, 0)  # (H, W, 3)

    def _read_nc_result(self, output_dir: Path, sample_info: Dict[str, Any], center_time: int) -> Optional[np.ndarray]:
        """
        从保存的NC文件中读取对应的预测结果

        Args:
            output_dir: 输出目录
            sample_info: 样本信息
            center_time: 中心时间索引

        Returns:
            预测结果数组 (H, W) 或 None（如果读取失败）
        """
        try:
            # 构建输出文件路径
            nc_file_path = Path(sample_info['nc_file'])
            output_nc_path = output_dir / f"inpainted_{nc_file_path.name}"

            if not output_nc_path.exists():
                logger.debug(f"Output NC file not found: {output_nc_path}")
                return None

            # 读取数据
            import xarray as xr
            with xr.open_dataset(output_nc_path, decode_times=False, mask_and_scale=False, cache=False) as ds:
                idx_x = sample_info['idx_x']
                idx_y = sample_info['idx_y']

                # 检查数据变量是否存在
                if 'data' not in ds.data_vars:
                    logger.debug(f"'data' variable not found in {output_nc_path}")
                    return None

                # 获取数据维度信息
                data_dims = ds.data.dims
                data_shape = ds.data.shape
                logger.debug(f"NC file dimensions: {data_dims}, shape: {data_shape}")

                # 检查索引范围
                if 'idx_x' in data_dims and idx_x >= ds.sizes.get('idx_x', 0):
                    logger.debug(f"idx_x out of range: {idx_x} >= {ds.sizes.get('idx_x', 0)}")
                    return None
                if 'idx_y' in data_dims and idx_y >= ds.sizes.get('idx_y', 0):
                    logger.debug(f"idx_y out of range: {idx_y} >= {ds.sizes.get('idx_y', 0)}")
                    return None
                if 'time' in data_dims and center_time >= ds.sizes.get('time', 0):
                    logger.debug(f"time out of range: {center_time} >= {ds.sizes.get('time', 0)}")
                    return None

                # 根据实际的数据结构读取数据
                tile_data = ds.data.isel(idx_x=idx_x, idx_y=idx_y, time=center_time).values

                # 转换为0-1概率值
                if tile_data.dtype == np.uint8 and np.max(tile_data) > 1:
                    tile_data = tile_data.astype(np.float32) / 100.0
                elif tile_data.dtype != np.float32 and np.max(tile_data) <= 1:
                    tile_data = tile_data.astype(np.float32)

                return tile_data

        except Exception as e:
            logger.debug(f"Failed to read NC result: {e}")
            return None


class NetCDFInferenceEngine:
    """NetCDF推理引擎"""

    def __init__(self, config: Optional[Union[str, Config]] = None, **overrides):
        """
        初始化推理引擎

        Args:
            config: 配置文件路径或Config对象
            **overrides: 配置覆盖参数
        """
        # 处理不同类型的配置输入
        if isinstance(config, str):
            # 配置文件路径
            self.cfg = get_config(config)
        elif isinstance(config, Config):
            # Config对象
            self.cfg = config
        else:
            # 使用默认配置
            self.cfg = get_config('/home/<USER>/Water/configs/config_v20.yaml')

        # 应用覆盖参数
        for key, value in overrides.items():
            if '.' in key:
                self.cfg.set(key, value)
            else:
                # 假设是inference配置
                self.cfg.set(f'inference.{key}', value)

        self.model = None
        self.device = torch.device(self.cfg.get('inference.device', 'cuda:0'))
        self.inference_dir = Path(self.cfg.get('inference.output_dir', './results'))
        # 初始化数据处理器
        self.data_processor = DataProcessor()

        # 内存监控
        self.memory_usage = 0
        self.max_memory_bytes = self.max_memory_gb * 1024**3
        # 统计用于分阶段保存
        self._total_tiles_processed = 0
        self._total_pixels_inpainted = 0
        # 溢写目录与LRU计数器
        spill_dir_cfg = self.cfg.get('inference.spill_dir', './.inference_spill')
        self._spill_dir = Path(spill_dir_cfg)
        self._spill_dir.mkdir(parents=True, exist_ok=True)
        self._access_counter = 0
        self._cache_bytes = 0

        # 初始化可视化模块
        visualization_enabled = self.cfg.get('inference.visualization.enabled', True)
        self.visualization_module = InferenceVisualizationModule(
            self.inference_dir, enabled=visualization_enabled
        )

        logger.info(f"Initialized inference engine with device: {self.device}")
        logger.info(f"Using configuration: batch_size={self.batch_size}, "
                   f"num_workers={self.num_workers}, "
                   f"missing_threshold={self.missing_threshold}")

    # 配置属性
    @property
    def model_checkpoint(self) -> str:
        return self.cfg.get('inference.checkpoint_path', './checkpoints/best_model.pth')

    @property
    def batch_size(self) -> int:
        return self.cfg.get('inference.batch_size', 16)

    @property
    def num_workers(self) -> int:
        return self.cfg.get('inference.num_workers', 4)

    @property
    def missing_threshold(self) -> float:
        return self.cfg.get('inference.missing_threshold', 0.0)
    @property
    def verify_writeback_enabled(self) -> bool:
        return self.cfg.get('inference.verify_writeback', True)

    @property
    def verify_samples_per_tile(self) -> int:
        return int(self.cfg.get('inference.verify_samples_per_tile', 2))

    @property
    def sliding_window_size(self) -> int:
        return self.cfg.get('inference.sliding_window_size', 48)

    @property
    def prefetch_buffer(self) -> int:
        return self.cfg.get('inference.prefetch_buffer', 2)

    @property
    def max_memory_gb(self) -> float:
        return self.cfg.get('inference.max_memory_gb', 8.0)

    @property
    def time_range(self) -> Optional[Tuple[str, str]]:
        """获取时间范围配置"""
        return self.cfg.get('inference.time_range')

    def load_model(self):
        """加载模型：严格参考 efficient_batch_inference.py 的方式"""
        if self.model is not None:
            return
        try:
            checkpoint = None
            # 优先安全方式加载（PyTorch 2.6+ 支持 weights_only=True）
            try:
                try:
                    if hasattr(torch, 'serialization') and hasattr(torch.serialization, 'add_safe_globals'):
                        torch.serialization.add_safe_globals([get_config])
                        logger.info("Registered get_config in torch.serialization.safe_globals for secure loading")
                except Exception as sg_err:
                    logger.debug(f"Failed to register safe globals: {sg_err}")

                checkpoint = torch.load(self.model_checkpoint, map_location=self.device, weights_only=True)
                logger.info("Checkpoint loaded with weights_only=True (safe mode)")
            except TypeError:
                # 兼容旧版 PyTorch
                checkpoint = torch.load(self.model_checkpoint, map_location=self.device)
                logger.info("Checkpoint loaded without weights_only (older PyTorch)")
            except Exception:
                # 最后兜底（信任 checkpoint 来源）
                logger.info("weights_only=True load failed; falling back to weights_only=False due to trusted checkpoint")
                checkpoint = torch.load(self.model_checkpoint, map_location=self.device, weights_only=False)

            # 使用配置创建模型
            model_config = self.cfg

            # 创建模型
            self.model = create_swin_water_net(model_config)

            # 加载权重（支持 dict 或直接 state_dict）
            state_dict = checkpoint.get('model_state_dict', checkpoint) if isinstance(checkpoint, dict) else checkpoint
            if isinstance(state_dict, dict) and any(k.startswith('module.') for k in state_dict.keys()):
                state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}

            missing_keys, unexpected_keys = self.model.load_state_dict(state_dict, strict=False)
            if missing_keys:
                logger.warning(f"Missing keys in model state_dict: {missing_keys}")
            if unexpected_keys:
                logger.warning(f"Unexpected keys in model state_dict: {unexpected_keys}")

            # 设备与性能设置
            self.model.to(self.device)

            # 确保所有子模块都在正确的设备上
            for name, module in self.model.named_modules():
                if hasattr(module, 'weight') and module.weight is not None:
                    if module.weight.device != self.device:
                        logger.warning(f"Moving {name} weights from {module.weight.device} to {self.device}")
                        module.to(self.device)
                if hasattr(module, 'bias') and module.bias is not None:
                    if module.bias.device != self.device:
                        logger.warning(f"Moving {name} bias from {module.bias.device} to {self.device}")
                        module.to(self.device)

            try:
                self.model = self.model.to(memory_format=torch.channels_last)
            except Exception:
                pass
            if self.device.type == 'cuda':
                try:
                    torch.backends.cudnn.benchmark = True
                except Exception:
                    pass
            self.model.eval()

            logger.info(f"Model loaded successfully from {self.model_checkpoint}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise

    def run_inference_with_dataloader(self, nc_files: List[Path], time_range: Optional[Tuple[str, str]] = None) -> Dict[str, Any]:
        """
        使用Dataset和DataLoader进行推理

        Args:
            nc_files: NetCDF文件列表
            time_range: 时间范围过滤 (start_date, end_date)

        Returns:
            推理结果字典
        """
        # 创建推理数据集 - 使用自动选择的最优扫描方法
        # 优先使用传入的time_range参数，如果没有则使用配置中的time_range
        effective_time_range = time_range if time_range is not None else self.time_range

        dataset = InferenceDataset(
            nc_files=nc_files,
            window_size=self.sliding_window_size,
            missing_threshold=self.missing_threshold,
            scan_method='auto',  # 自动选择最优扫描方法
            time_range=effective_time_range
        )

        if len(dataset) == 0:
            logger.warning("No samples found for inference")
            return {}

        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size,
            num_workers=self.num_workers,
            shuffle=False,
            pin_memory=True,
            drop_last=False,
            persistent_workers=(self.num_workers > 0)
        )

        # 加载模型
        self.load_model()

        # 存储结果（分阶段保存）
        all_results: Dict[str, Any] = {}
        tile_data_cache: Dict[str, Any] = {}
        per_file_buffer: Dict[Path, Dict[str, Any]] = {}
        flush_every = int(self.cfg.get('inference.flush_every_tiles', 1024))

        # 保存第一个batch的信息用于后续可视化
        first_batch_info = None

        logger.info(f"Starting inference on {len(dataset)} samples using DataLoader")

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc="Inference")):
                try:
                    # 将数据移到设备
                    batch_device = {
                        'input_sequence': batch['input_sequence'].to(self.device, non_blocking=True),
                        'occurrence': batch['occurrence'].to(self.device, non_blocking=True),
                        'center_frame_idx': batch['center_frame_idx'].to(self.device, non_blocking=True),
                        'missing_mask': batch['missing_mask'].to(self.device, non_blocking=True),
                    }

                    # 模型推理
                    outputs = self.model(batch_device)

                    # 解析输出
                    logits = self._extract_logits(outputs)
                    if logits is None:
                        logger.error(f"Failed to extract logits from model output for batch {batch_idx}")
                        continue

                    # 转换为0-100概率值
                    preds = self._logits_to_predictions(logits)

                    # 保存第一个batch的信息用于后续可视化
                    if batch_idx == 0 and self.visualization_module.enabled:
                        first_batch_info = {
                            'batch': {k: v.clone() if torch.is_tensor(v) else v for k, v in batch.items()},
                            'predictions': preds.copy()
                        }

                    # 处理批次结果并直接写回原始数据
                    batch_results = self._process_batch_results(batch, preds, tile_data_cache)
                    # 合并到全局与每文件缓冲
                    for k, v in batch_results.items():
                        all_results[k] = v
                        fpath = Path(v['nc_file']) if not isinstance(v['nc_file'], Path) else v['nc_file']
                        per_file_buffer.setdefault(fpath, {})[k] = v
                    # 判断分阶段保存
                    to_flush: Dict[Path, Dict[str, Any]] = {}
                    for fpath, entries in list(per_file_buffer.items()):
                        if len(entries) >= flush_every:
                            to_flush[fpath] = entries
                            per_file_buffer[fpath] = {}
                    # 执行保存并清理对应 all_results，累计统计
                    for fpath, entries in to_flush.items():
                        if entries:
                            self.save_results_to_nc(entries, self.inference_dir)
                            self._total_tiles_processed += len(entries)
                            self._total_pixels_inpainted += sum(e.get('inpainted_pixels', 0) for e in entries.values())
                            # 从 all_results 中清理，避免膨胀
                            for key in entries.keys():
                                all_results.pop(key, None)

                    # 自适应内存清理（降低频率并按碎片情况触发）
                    if batch_idx % 200 == 0:
                        try:
                            if torch.cuda.is_available():
                                torch.cuda.synchronize()
                                r = torch.cuda.memory_reserved()
                                a = torch.cuda.memory_allocated()
                                if r > 2 * a:
                                    torch.cuda.empty_cache()
                        except Exception:
                            pass
                        gc.collect()

                except Exception as e:
                    logger.error(f"Error processing batch {batch_idx}: {e}")
                    continue

        # 结束前 flush 剩余
        remaining = {}
        for fpath, entries in per_file_buffer.items():
            remaining.update(entries)
        if remaining:
            self.save_results_to_nc(remaining, self.inference_dir)
            self._total_tiles_processed += len(remaining)
            self._total_pixels_inpainted += sum(e.get('inpainted_pixels', 0) for e in remaining.values())
            # all_results 清空，避免返回巨大对象
            all_results.clear()

        # 在所有NC文件保存完成后进行可视化（包含验证）
        if first_batch_info is not None and self.visualization_module.enabled:
            logger.info("Generating visualization with NC file verification...")
            num_vis_samples = self.cfg.get('inference.visualization.num_samples', 5)
            self.visualization_module.visualize_first_batch(
                first_batch_info['batch'],
                first_batch_info['predictions'],
                num_vis_samples
            )

        logger.info(f"Inference completed. Processed {self._total_tiles_processed} tiles")
        return {}

    def _extract_logits(self, outputs) -> Optional[torch.Tensor]:
        """从模型输出中提取logits"""
        logits = None
        if isinstance(outputs, dict) and 'inpaint' in outputs:
            inpaint_out = outputs['inpaint']
            if isinstance(inpaint_out, dict) and 'logits' in inpaint_out:
                logits = inpaint_out['logits']
            elif torch.is_tensor(inpaint_out):
                logits = inpaint_out
        elif isinstance(outputs, dict):
            # 退化：取第一个tensor
            for v in outputs.values():
                if torch.is_tensor(v):
                    logits = v
                    break
        elif torch.is_tensor(outputs):
            logits = outputs

        return logits

    # ------- 缓存溢写与LRU管理：限制内存峰值 -------
    def _estimate_array_bytes(self, arr: np.ndarray) -> int:
        try:
            return int(arr.nbytes)
        except Exception:
            return 0

    def _spill_tile_to_memmap(self, tile_key: str, tile_cache: Dict[str, Any]):
        """将内存中的 full_tile_data 溢写到 memmap 文件并释放内存。"""
        full = tile_cache.get('full_tile_data')
        if full is None:
            return
        base = Path(tile_cache['nc_file']).stem
        path = self._spill_dir / f"{base}_{tile_cache['idx_x']}_{tile_cache['idx_y']}.mmap"
        # 使用 memmap 写入
        mm = np.memmap(path, dtype=full.dtype, mode='w+', shape=full.shape)
        mm[:] = full[:]
        del mm
        # 更新缓存元数据
        tile_cache['memmap_path'] = str(path)
        tile_cache['shape'] = tuple(full.shape)
        tile_cache['dtype'] = str(full.dtype)
        # 释放内存并更新计数
        nbytes = self._estimate_array_bytes(full)
        tile_cache['full_tile_data'] = None
        self._cache_bytes = max(0, self._cache_bytes - nbytes)

    def _ensure_cache_budget(self, cache: Dict[str, Dict[str, Any]]):
        """确保 tile_data_cache 的内存占用不超过阈值，必要时将最久未使用的 tile 溢写到磁盘。"""
        try:
            budget = int(self.max_memory_bytes)
        except Exception:
            budget = int(8 * 1024**3)
        # 仅考虑当前驻留内存的条目
        while self._cache_bytes > budget:
            candidates = [(k, v.get('last_access', -1)) for k, v in cache.items() if v.get('full_tile_data') is not None]
            if not candidates:
                break
            # 选择最久未访问
            evict_key, _ = min(candidates, key=lambda x: x[1])
            self._spill_tile_to_memmap(evict_key, cache[evict_key])

    def _open_memmap(self, tile_cache: Dict[str, Any], mode: str = 'r+'):
        path = tile_cache.get('memmap_path')
        if not path:
            return None
        dtype = np.dtype(tile_cache.get('dtype', 'uint8'))
        shape = tuple(tile_cache.get('shape'))
        return np.memmap(path, dtype=dtype, mode=mode, shape=shape)

    def _logits_to_predictions(self, logits: torch.Tensor) -> np.ndarray:
        """将logits转换为0-100的概率值"""
        # 转为(B,H,W)概率
        if logits.dim() == 4 and logits.size(1) >= 2:
            probs = torch.softmax(logits, dim=1)[:, 1]  # water通道
        elif logits.dim() == 4 and logits.size(1) == 1:
            probs = torch.sigmoid(logits[:, 0])
        elif logits.dim() == 3:
            probs = torch.sigmoid(logits)
        else:
            raise ValueError(f"Unsupported logits shape: {list(logits.shape)}")

        # 转换为0-100的概率值
        preds = (probs.detach().cpu().numpy() * 100).astype(np.uint8)  # (B,H,W) 0-100概率值
        return preds
    
    def _process_batch_results(self, batch: Dict[str, Any], preds: np.ndarray,
                                             tile_data_cache: Dict[str, Dict]) -> Dict[str, Any]:
        """
        处理批次结果并直接写回原始数据

        Args:
            batch: 批次数据，包含sample_info和full_tile_data
            preds: 模型预测结果 (B, H, W) - 0-100概率值
            tile_data_cache: tile数据缓存，用于存储原始数据和缺失掩码

        Returns:
            更新后的tile结果字典
        """
        batch_size = preds.shape[0]
        updated_tiles = {}

        for b in range(batch_size):
            # 提取样本信息
            if isinstance(batch['sample_info'], list):
                sample_info = batch['sample_info'][b]
            else:
                sample_info = {k: v[b] if isinstance(v, (list, torch.Tensor)) else v
                              for k, v in batch['sample_info'].items()}

            nc_file = sample_info['nc_file']
            idx_x = int(sample_info['idx_x'])
            idx_y = int(sample_info['idx_y'])
            center_time_idx = int(sample_info['center_time_idx'])  # 绝对时间位置

            tile_key = f"{nc_file}_{idx_x}_{idx_y}"

            # 初始化tile数据（如果尚未存在）
            if tile_key not in tile_data_cache:
                # 首次遇到该 tile：惰性读取整条 time 维度的数据并缓存
                with xr.open_dataset(nc_file, decode_times=False, mask_and_scale=False, cache=True) as ds:
                    full_tile_data = ds.data.isel(idx_x=idx_x, idx_y=idx_y).values
                # 不缓存整条缺失掩码，减少内存占用；inpainted_pixels 延后统计
                tile_data_cache[tile_key] = {
                    'nc_file': nc_file,
                    'idx_x': idx_x,
                    'idx_y': idx_y,
                    'full_tile_data': full_tile_data,
                    'inpainted_pixels': 0,
                    'last_access': self._access_counter,
                }
                # 更新缓存字节数并检查是否需要溢写
                self._access_counter += 1
                try:
                    self._cache_bytes += int(full_tile_data.nbytes)
                except Exception:
                    pass
                self._ensure_cache_budget(tile_data_cache)

            # 获取预测结果
            pred_frame = preds[b]  # (H, W) 0-100概率值
            # 直接写回到绝对时间位置的缺失区域（使用 batch 的中心帧 missing_mask，避免缓存整条掩码）
            tile_cache = tile_data_cache[tile_key]
            # 更新LRU访问时间
            tile_cache['last_access'] = self._access_counter
            self._access_counter += 1
            # 判断是否有足够的信息可写回（无论在内存还是 memmap）
            total_T = tile_cache['full_tile_data'].shape[0] if tile_cache.get('full_tile_data') is not None else tile_cache.get('shape', (0,))[0]
            if center_time_idx < total_T:
                if isinstance(batch['missing_mask'], list):
                    center_missing_mask = batch['missing_mask'][b].cpu().numpy()
                else:
                    mmask = batch['missing_mask']
                    center_missing_mask = (mmask[b].cpu().numpy() if torch.is_tensor(mmask) else mmask[b])
                # 统计本次写回的像素数量
                tile_cache['inpainted_pixels'] += int(np.sum(center_missing_mask))
                # 写回到内存或 memmap，并记录该帧的轻量校验和（用于验证写回位置）
                if tile_cache.get('full_tile_data') is not None:
                    # updated_frame = np.where(center_missing_mask, pred_frame, tile_cache['full_tile_data'][center_time_idx])
                    # 用预测结果全部覆盖
                    updated_frame = pred_frame
                    tile_cache['full_tile_data'][center_time_idx] = updated_frame
                    frame_sum = int(np.sum(updated_frame, dtype=np.uint64))
                else:
                    mm = self._open_memmap(tile_cache, mode='r+')
                    if mm is not None:
                        updated_frame = np.where(center_missing_mask, pred_frame, mm[center_time_idx])
                        mm[center_time_idx] = updated_frame
                        frame_sum = int(np.sum(updated_frame, dtype=np.uint64))
                        del mm
                    else:
                        frame_sum = 0
                # 记录已更新的时间索引与校验和
                tile_cache.setdefault('updated_times', set()).add(int(center_time_idx))
                checksums = tile_cache.setdefault('checksums', {})
                checksums[int(center_time_idx)] = frame_sum
                updated_tiles[tile_key] = True

        # 返回完成推理的tile结果
        final_results = {}
        for tile_key, tile_cache in tile_data_cache.items():
            if tile_key in updated_tiles:
                res = {
                    'nc_file': tile_cache['nc_file'],
                    'idx_x': tile_cache['idx_x'],
                    'idx_y': tile_cache['idx_y'],
                    'inpainted_pixels': tile_cache.get('inpainted_pixels', 0)
                }
                if tile_cache.get('full_tile_data') is not None:
                    res['inpainted_data'] = tile_cache['full_tile_data']
                else:
                    # 已溢写到 memmap
                    if tile_cache.get('memmap_path'):
                        res['memmap_path'] = tile_cache['memmap_path']
                        res['shape'] = tile_cache.get('shape')
                        res['dtype'] = tile_cache.get('dtype')
                # 写入更新的时间索引与校验和，便于后续快速验证
                if tile_cache.get('updated_times'):
                    res['updated_times'] = sorted(list(tile_cache['updated_times']))
                if tile_cache.get('checksums'):
                    res['checksums'] = {int(k): int(v) for k, v in tile_cache['checksums'].items()}
                final_results[tile_key] = res

        return final_results

    def save_results_to_nc(self, results: Dict[str, Any], output_dir: Path):
        """保存推理结果到NetCDF文件"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 按文件分组结果（将可能的字符串路径标准化为 Path）
        file_results: Dict[Path, List[Dict[str, Any]]] = {}
        for _, result in results.items():
            nc_file = Path(result['nc_file']) if not isinstance(result['nc_file'], Path) else result['nc_file']
            file_results.setdefault(nc_file, []).append(result)

        # 保存每个文件的结果
        for nc_file, file_result_list in file_results.items():
            try:
                output_path = output_dir / f"inpainted_{nc_file.name}"

                # 若输出文件不存在，则复制原始文件；存在则增量写入
                if not output_path.exists():
                    shutil.copyfile(nc_file, output_path)

                with NcDataset(str(output_path), mode='r+') as nc:
                    if 'data' not in nc.variables:
                        raise ValueError("Variable 'data' not found in NetCDF file")
                    var = nc.variables['data']
                    dims = var.dimensions  # 元组

                    def find_axis(candidates: List[str]) -> Optional[int]:
                        for i, d in enumerate(dims):
                            for c in candidates:
                                if d == c or c in d:
                                    return i
                        return None

                    time_axis = find_axis(['time'])
                    idx_x_axis = find_axis(['idx_x', 'x_idx'])
                    idx_y_axis = find_axis(['idx_y', 'y_idx'])

                    for result in file_result_list:
                        idx_x = int(result['idx_x'])
                        idx_y = int(result['idx_y'])
                        inpainted_data = result.get('inpainted_data')
                        memmap_path = result.get('memmap_path')
                        if inpainted_data is None and memmap_path:
                            dtype = np.dtype(result.get('dtype', 'uint8'))
                            shape = tuple(result.get('shape'))
                            mm = np.memmap(memmap_path, dtype=dtype, mode='r', shape=shape)
                            inpainted_data = np.asarray(mm)
                            del mm
                            try:
                                os.remove(memmap_path)
                            except Exception:
                                pass
                        # 构造切片索引：time 全部、指定 idx_x/idx_y、空间维全部
                        indexers = [slice(None)] * len(dims)
                        if time_axis is not None:
                            indexers[time_axis] = slice(None)
                        if idx_x_axis is not None:
                            indexers[idx_x_axis] = idx_x
                        if idx_y_axis is not None:
                            indexers[idx_y_axis] = idx_y
                        var[tuple(indexers)] = inpainted_data

                    # 添加推理信息到文件属性
                    nc.setncattr('inpainted', 'true')
                    nc.setncattr('inpaint_timestamp', pd.Timestamp.now().isoformat())
                    nc.setncattr('inpaint_model', self.model_checkpoint)

                logger.info(f"Saved inpainted results to {output_path}")

            except Exception as e:
                logger.error(f"Error saving results for {nc_file}: {e}")



    def verify_writeback(self, results: Dict[str, Any], output_dir: str, max_samples_per_tile: int = 2) -> Dict[str, List[str]]:
        """轻量校验：抽样检查写回的帧是否位于正确的 time 索引。
        基于 _process_batch_results 中记录的 updated_times 和 checksums，到输出文件按帧读取并校验校验和。
        返回 {nc_file: [mismatch_msg,...]}，为空表示全部通过。
        """
        # 按文件聚合
        by_file: Dict[Path, List[Dict[str, Any]]] = {}
        for _, r in results.items():
            p = Path(r['nc_file']) if not isinstance(r['nc_file'], Path) else r['nc_file']
            if r.get('updated_times') and r.get('checksums'):
                by_file.setdefault(p, []).append(r)
        mismatches: Dict[str, List[str]] = {}
        for src_nc, tiles in by_file.items():
            out_nc = Path(output_dir) / f"inpainted_{src_nc.name}"
            if not out_nc.exists():
                mismatches.setdefault(str(src_nc), []).append(f"Output not found: {out_nc}")
                continue
            with NcDataset(str(out_nc), mode='r') as nc:
                if 'data' not in nc.variables:
                    mismatches.setdefault(str(src_nc), []).append("Variable 'data' not in output")
                    continue
                var = nc.variables['data']
                dims = var.dimensions
                # 轴定位
                def find_axis(cands: List[str]) -> Optional[int]:
                    for i, d in enumerate(dims):
                        for c in cands:
                            if d == c or c in d:
                                return i
                    return None
                t_ax = find_axis(['time'])
                x_ax = find_axis(['idx_x', 'x_idx'])
                y_ax = find_axis(['idx_y', 'y_idx'])
                for r in tiles:
                    idx_x = int(r['idx_x']); idx_y = int(r['idx_y'])
                    uts: List[int] = list(r['updated_times']) if isinstance(r['updated_times'], list) else r['updated_times']
                    uts = sorted(uts)[:max_samples_per_tile]
                    for ti in uts:
                        indexers = [slice(None)] * len(dims)
                        if t_ax is not None: indexers[t_ax] = int(ti)
                        if x_ax is not None: indexers[x_ax] = idx_x
                        if y_ax is not None: indexers[y_ax] = idx_y
                        frame = np.asarray(var[tuple(indexers)])  # (H,W)
                        s = int(np.sum(frame, dtype=np.uint64))
                        expect = int(r['checksums'][int(ti)])
                        if s != expect:
                            mismatches.setdefault(str(src_nc), []).append(
                                f"tile({idx_x},{idx_y}) time={ti} checksum mismatch: got {s}, expect {expect}")
        return mismatches

    def run_inference(self, nc_dir: str, output_dir: str,
                     region_bounds: Optional[Tuple[float, float, float, float]] = None,
                     time_range: Optional[Tuple[str, str]] = None) -> Dict[str, Any]:
        """运行推理主流程 - 使用Dataset和DataLoader"""
        logger.info("Starting NetCDF inference process with Dataset and DataLoader")
        start_time = time.time()

        # 设置当前输出目录，供批处理保存使用
        self._current_output_dir = Path(output_dir)

        # 将溢写目录设置为输出目录下的子目录（优先配置 inference.spill_subdir，默认 .spill）
        spill_subdir = '.spill'
        self._spill_dir = Path(output_dir) / spill_subdir
        self._spill_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Spill directory set to: {self._spill_dir}")

        # 查找NetCDF文件
        nc_files = find_nc_files(nc_dir, region_bounds)
        if not nc_files:
            logger.error("No NetCDF files found")
            return {}

        if time_range:
            logger.info(f"Time range filter: {time_range[0]} to {time_range[1]}")

        # 使用新的DataLoader方法进行推理
        all_results = self.run_inference_with_dataloader(nc_files, time_range)

        if all_results:
            self.save_results_to_nc(all_results, self.inference_dir)        

        # 可选：轻量校验写回位置
        if self.verify_writeback_enabled:
            logger.info("Verifying writeback (lightweight checks)...")
            mismatches = self.verify_writeback(all_results, output_dir, max_samples_per_tile=self.verify_samples_per_tile)
            if mismatches:
                logger.warning(f"Writeback verification found mismatches: {sum(len(v) for v in mismatches.values())}")
                for k, msgs in list(mismatches.items())[:3]:
                    logger.warning(f"{k}: {msgs[:3]}")
            else:
                logger.info("Writeback verification passed.")

        # 统计信息
        total_time = time.time() - start_time
        total_pixels = sum(result['inpainted_pixels'] for result in all_results.values())

        inference_stats = {
            'total_tiles_processed': len(all_results),
            'total_pixels_inpainted': total_pixels,
            'total_time_seconds': total_time,
            'pixels_per_second': total_pixels / total_time if total_time > 0 else 0,
            'output_directory': output_dir
        }

        logger.info(f"Inference completed in {total_time:.2f} seconds")
        logger.info(f"Processed {len(all_results)} tiles, inpainted {total_pixels} pixels")

        return inference_stats

    # def infer_and_restore(self, nc_dir: str, output_dir: str,
    #                      region_bounds: Optional[Tuple[float, float, float, float]] = None,
    #                      time_range: Optional[Tuple[str, str]] = None,
    #                      restore_regions: Optional[Dict[str, Tuple[float, float, float, float]]] = None) -> Dict[str, Any]:
    #     """推理并恢复为TIF文件的完整流程"""

    #     # 步骤1: 运行推理
    #     logger.info("=" * 50)
    #     logger.info("STEP 1: Running inference on NetCDF files")
    #     logger.info("=" * 50)

    #     inpainted_dir = Path(output_dir) / "inpainted_nc"
    #     inference_stats = self.run_inference(nc_dir, str(inpainted_dir), region_bounds, time_range)

    #     if not inference_stats:
    #         logger.error("Inference failed")
    #         return {}

    #     # 步骤2: 恢复为TIF文件
    #     logger.info("=" * 50)
    #     logger.info("STEP 2: Restoring TIF files from inpainted NetCDF")
    #     logger.info("=" * 50)

    #     tif_output_dir = Path(output_dir) / "restored_tifs"
    #     restorer = NetCDFToTIFRestorer(str(inpainted_dir), str(tif_output_dir))

    #     # 使用推理后的NetCDF文件进行恢复
    #     restoration_results = restorer.restore_all(
    #         regions=restore_regions,
    #         time_range=time_range,
    #         include_occurrence=True,
    #         num_workers=self.num_workers
    #     )

    #     # 合并结果
    #     final_results = {
    #         'inference_stats': inference_stats,
    #         'restoration_results': restoration_results,
    #         'inpainted_nc_dir': str(inpainted_dir),
    #         'restored_tif_dir': str(tif_output_dir)
    #     }

    #     logger.info("=" * 50)
    #     logger.info("PROCESS COMPLETED SUCCESSFULLY")
    #     logger.info("=" * 50)
    #     logger.info(f"Inpainted NetCDF files: {inpainted_dir}")
    #     logger.info(f"Restored TIF files: {tif_output_dir}")

    #     return final_results

def create_inference_engine(config_path: Optional[str] = None, **kwargs) -> NetCDFInferenceEngine:
    """
    创建推理引擎的便捷函数

    Args:
        config_path: 配置文件路径
        **kwargs: 配置覆盖参数

    Returns:
        NetCDFInferenceEngine对象
    """
    return NetCDFInferenceEngine(config_path, **kwargs)


def main(config_path: Optional[str] = None,
         model_checkpoint: Optional[str] = None,
         nc_dir: Optional[str] = None,
         output_dir: Optional[str] = None,
         device: Optional[str] = None,
         batch_size: Optional[int] = None,
         num_workers: Optional[int] = None,
         missing_threshold: Optional[float] = None,
         region_bounds: Optional[Tuple[float, float, float, float]] = None,
         time_range: Optional[Tuple[str, str]] = None,
         visualization_enabled: bool = True,
         num_vis_samples: int = 5):
    """
    主函数 - 直接参数接口

    Args:
        config_path: 配置文件路径
        model_checkpoint: 模型检查点路径
        nc_dir: NetCDF文件目录
        output_dir: 输出目录
        device: 推理设备
        batch_size: 批次大小
        num_workers: 工作线程数
        missing_threshold: 缺失像素比例阈值
        region_bounds: 区域边界 (min_lon, min_lat, max_lon, max_lat)
        start_time: 开始时间 (YYYY-MM-DD)
        end_time: 结束时间 (YYYY-MM-DD)
        time_range: 时间范围 (start, end) - 优先于start_time/end_time
        visualization_enabled: 是否启用可视化
        num_vis_samples: 可视化样本数量

    Returns:
        推理结果字典
    """

    # 构建配置覆盖参数
    overrides = {}
    if model_checkpoint:
        overrides['inference.checkpoint_path'] = model_checkpoint
    if nc_dir:
        overrides['inference.nc_dir'] = nc_dir
    if output_dir:
        overrides['inference.output_dir'] = output_dir
    if device:
        overrides['inference.device'] = device
    if batch_size is not None:
        overrides['inference.batch_size'] = batch_size
    if num_workers is not None:
        overrides['inference.num_workers'] = num_workers
    if missing_threshold is not None:
        overrides['inference.missing_threshold'] = missing_threshold

    # 可视化配置
    overrides['inference.visualization.enabled'] = visualization_enabled
    overrides['inference.visualization.num_samples'] = num_vis_samples

    # 创建推理引擎
    engine = NetCDFInferenceEngine(config_path, **overrides)

    # 从配置获取默认参数，传入参数优先
    cfg = engine.cfg
    final_nc_dir = nc_dir or cfg.get('inference.nc_dir', 'data/netcdf_files')
    final_output_dir = output_dir or cfg.get('inference.output_dir', './results')

    # 设置区域和时间范围
    final_region_bounds = region_bounds or cfg.get('inference.region_bounds')

    # 时间范围解析 - 支持两种参数方式
    final_time_range = None
    if time_range:
        # 使用 time_range 参数
        final_time_range = time_range
    else:
        # 从配置文件获取
        final_time_range = cfg.get('inference.time_range')

    print(f"Starting inference with configuration:")
    print(f"  Config file: {config_path or 'default'}")
    print(f"  NC directory: {final_nc_dir}")
    print(f"  Output directory: {final_output_dir}")
    print(f"  Device: {engine.device}")
    print(f"  Batch size: {engine.batch_size}")
    print(f"  Region bounds: {final_region_bounds}")
    print(f"  Time range: {final_time_range}")
    print(f"  Visualization enabled: {visualization_enabled}")
    if visualization_enabled:
        print(f"  Visualization samples: {num_vis_samples}")

    results = engine.run_inference(
        nc_dir=final_nc_dir,
        output_dir=final_output_dir,
        region_bounds=final_region_bounds,
        time_range=final_time_range
    )
    print(f"Inference completed. Results saved to: {final_output_dir}")
    return results

if __name__ == "__main__":
    # 示例调用 - 可以根据需要修改参数
    main(
        config_path='/home/<USER>/Water/configs/config_v20.yaml',
        nc_dir='/mnt/storage/xiaozhen/Water/Clip/JRC4',
        model_checkpoint='/mnt/storage/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_2/best.pt',
        output_dir='/mnt/storage/xiaozhen/Water/Predictions/swin_waternet_v20_2',
        time_range=('2020-09', '2020-09'),
        region_bounds=(115, 28, 118, 30),
        num_workers=1,
        visualization_enabled=True,
        num_vis_samples=10
    )