#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Compute monthly water area from restored TIFs and plot monthly changes.

Assumptions and notes:
- TIFs are produced by prediction/restoration.py with JRC encoding:
  land=1, water=2, nodata=255 (see JRC_VALUES below).
- Files are typically named like: mosaic_YYYYMMDD_HHMM.tif (time derived from NetCDF).
- Output directory for restored TIFs depends on how restoration was run:
  * If using EnhancedNetCDFToTIFRestorer directly: files are saved under the given output_dir
    with names like 'mosaic_YYYYMMDD_HHMM.tif'.
  * In older inference flow, restored tifs may be in <output_dir>/restored_tifs/.

This script:
- Scans a directory recursively for TIFs matching a glob pattern (default: mosaic_*.tif)
- Parses timestamps from filenames to derive month
- Optionally applies a spatial mask from shapefile (.shp) or TIF file (.tif/.tiff) to limit
  calculation to a specific area of interest
- Computes water area per file considering CRS:
  * For geographic CRS (lat/lon, e.g., EPSG:4326): uses a spherical-geodesic per-row pixel area
    formula: A = R^2 * dλ * (sin φ2 - sin φ1)
  * For projected CRS (meter units): per-pixel area is |det(affine)| (square meters)
- Aggregates per month and saves CSV + line plot

Usage example:
    python prediction/monthly_water_area.py \
        --tif_dir /path/to/restored_tifs \
        --pattern 'mosaic_*.tif' \
        --out_dir /path/to/output

    # With shapefile mask
    python prediction/monthly_water_area.py \
        --tif_dir /path/to/restored_tifs \
        --mask_file /path/to/study_area.shp \
        --out_dir /path/to/output

    # With TIF mask
    python prediction/monthly_water_area.py \
        --tif_dir /path/to/restored_tifs \
        --mask_file /path/to/mask.tif \
        --out_dir /path/to/output

Mask file requirements:
- Shapefile (.shp): Requires geopandas. All geometries in the file will be used as the area of interest.
- TIF file (.tif/.tiff): Non-zero pixels define the area of interest. Will be reprojected to match
  the target TIF if CRS or resolution differs.

Outputs:
- monthly_water_area.csv (columns: month, water_area_km2)
- monthly_water_area.png (line chart)
"""
from __future__ import annotations

import argparse
import math
import re
from pathlib import Path
from typing import List, Optional, Tuple

import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # headless
import matplotlib.pyplot as plt

try:
    import rasterio
    from rasterio.transform import Affine
    from rasterio.features import geometry_mask
except Exception as e:
    raise RuntimeError(f"rasterio is required to run this script: {e}")

try:
    import geopandas as gpd
    from shapely.geometry import mapping
    GEOPANDAS_AVAILABLE = True
except ImportError:
    GEOPANDAS_AVAILABLE = False

# JRC encoding defaults (kept consistent with prediction/restoration.py)
JRC_VALUES = {
    'no_data': 255,
    'land': 1,
    'water': 2,
}
# Try to import authoritative JRC_VALUES if available
try:
    from data.dataset import JRC_VALUES as _JRC_VALUES
    JRC_VALUES = _JRC_VALUES
except Exception:
    pass

EARTH_RADIUS_M = 6378137.0  # WGS84 semi-major; spherical approximation for area formula


def is_geographic_crs(crs) -> bool:
    try:
        return crs is not None and crs.is_geographic
    except Exception:
        # Fallback: string check
        try:
            return str(crs).upper().startswith('EPSG:4326')
        except Exception:
            return False


def parse_timestamp_from_name(name: str) -> Optional[pd.Timestamp]:
    """Parse timestamp from filename.
    Supports patterns:
    - YYYYMMDD_HHMM
    - YYYYMMDD
    - YYYYMM
    Returns pandas.Timestamp or None if not found.
    """
    stem = Path(name).stem
    m = re.search(r"(\d{8})_(\d{4})", stem)
    if m:
        datestr, timestr = m.group(1), m.group(2)
        try:
            return pd.to_datetime(datestr + timestr, format="%Y%m%d%H%M")
        except Exception:
            pass
    m = re.search(r"(\d{8})", stem)
    if m:
        datestr = m.group(1)
        try:
            return pd.to_datetime(datestr, format="%Y%m%d")
        except Exception:
            pass
    m = re.search(r"(\d{6})", stem)
    if m:
        ym = m.group(1)
        try:
            return pd.to_datetime(ym, format="%Y%m")
        except Exception:
            pass
    return None


def per_row_pixel_area_geographic(transform: Affine, height: int, width: int) -> np.ndarray:
    """Compute per-row pixel area (m^2) for a north-up geographic raster.
    Uses spherical formula A = R^2 * dλ * (sin φ2 - sin φ1).
    Returns array of shape (height,) with the area of one pixel for each row.
    """
    # Check rotation terms
    if not np.isclose(transform.b, 0.0) or not np.isclose(transform.d, 0.0):
        raise ValueError("Rotated/skewed transforms are not supported for geographic area computation.")

    dlon_deg = abs(transform.a)
    dlat_deg = abs(transform.e)
    top_lat = transform.f
    # Note: transform.e typically negative; rows increase downward

    dlon_rad = math.radians(dlon_deg)
    dlat_rad = math.radians(dlat_deg)  # not directly used; compute with sin term per row

    # Row edge latitudes: φ(row) at top edge; φ(row+1) at bottom edge
    # top_edge_lat(row) = top_lat + row * transform.e
    rows = np.arange(height, dtype=np.float64)
    lat1 = top_lat + rows * transform.e
    lat2 = top_lat + (rows + 1) * transform.e

    # Convert to radians
    lat1_rad = np.radians(lat1)
    lat2_rad = np.radians(lat2)

    # Clamp to valid range to avoid NaNs if slightly beyond [-90, 90]
    lat1_rad = np.clip(lat1_rad, -math.pi / 2, math.pi / 2)
    lat2_rad = np.clip(lat2_rad, -math.pi / 2, math.pi / 2)

    area_per_pixel_row = (EARTH_RADIUS_M ** 2) * dlon_rad * np.abs(np.sin(lat2_rad) - np.sin(lat1_rad))
    return area_per_pixel_row  # length = height


def per_pixel_area_projected(transform: Affine) -> float:
    """Per-pixel area (m^2) for projected CRS: absolute determinant of affine."""
    # Area of a pixel parallelogram = |det([[a, b], [d, e]])|
    return float(abs(transform.a * transform.e - transform.b * transform.d))


def load_mask_from_file(mask_file: Path, target_crs, target_transform, target_shape) -> Optional[np.ndarray]:
    """
    Load mask from shapefile or TIF file and reproject to target raster.

    Args:
        mask_file: Path to shapefile (.shp) or TIF file (.tif/.tiff)
        target_crs: Target CRS for reprojection
        target_transform: Target affine transform
        target_shape: Target shape (height, width)

    Returns:
        Boolean mask array where True indicates areas to include in calculation,
        or None if mask could not be loaded
    """
    if not mask_file.exists():
        raise FileNotFoundError(f"Mask file not found: {mask_file}")

    file_ext = mask_file.suffix.lower()

    if file_ext == '.shp':
        # Handle shapefile
        if not GEOPANDAS_AVAILABLE:
            raise RuntimeError("geopandas is required to process shapefile masks. "
                             "Install with: pip install geopandas")

        try:
            # Read shapefile
            gdf = gpd.read_file(mask_file)

            # Reproject to target CRS if needed
            if gdf.crs != target_crs:
                gdf = gdf.to_crs(target_crs)

            # Convert geometries to rasterio format
            geometries = [mapping(geom) for geom in gdf.geometry if geom is not None]

            if not geometries:
                raise ValueError(f"No valid geometries found in {mask_file}")

            # Create mask using rasterio.features.geometry_mask
            # geometry_mask returns True for pixels OUTSIDE geometries, so we invert it
            mask = ~geometry_mask(
                geometries,
                transform=target_transform,
                invert=False,
                out_shape=target_shape
            )

            return mask

        except Exception as e:
            raise RuntimeError(f"Failed to process shapefile {mask_file}: {e}")

    elif file_ext in ('.tif', '.tiff'):
        # Handle TIF file
        try:
            with rasterio.open(mask_file) as mask_ds:
                # Read mask data
                mask_data = mask_ds.read(1)

                # Check if reprojection is needed
                if mask_ds.crs != target_crs or mask_ds.transform != target_transform or mask_data.shape != target_shape:
                    # Reproject mask to target grid
                    from rasterio.warp import reproject, Resampling

                    # Create output array
                    reprojected_mask = np.zeros(target_shape, dtype=mask_data.dtype)

                    # Reproject
                    reproject(
                        source=mask_data,
                        destination=reprojected_mask,
                        src_transform=mask_ds.transform,
                        src_crs=mask_ds.crs,
                        dst_transform=target_transform,
                        dst_crs=target_crs,
                        resampling=Resampling.nearest
                    )

                    mask_data = reprojected_mask

                # Convert to boolean mask (non-zero values are included)
                mask = mask_data != 0

                # Handle nodata values
                if mask_ds.nodata is not None:
                    mask = mask & (mask_data != mask_ds.nodata)

                return mask

        except Exception as e:
            raise RuntimeError(f"Failed to process TIF mask {mask_file}: {e}")

    else:
        raise ValueError(f"Unsupported mask file format: {file_ext}. "
                        "Supported formats: .shp, .tif, .tiff")


def compute_water_area_m2(tif_path: Path, mask_file: Optional[Path] = None) -> Tuple[Optional[pd.Timestamp], float]:
    """Compute total water area (m^2) for one TIF file, optionally within a specified mask.

    Args:
        tif_path: Path to the TIF file
        mask_file: Optional path to shapefile (.shp) or TIF file (.tif/.tiff) defining the area of interest

    Returns:
        (timestamp, area_m2). Timestamp may be None if not parsable.
    """
    with rasterio.open(tif_path) as ds:
        arr = ds.read(1)
        nodata = ds.nodata if ds.nodata is not None else JRC_VALUES['no_data']
        water_mask = (arr == JRC_VALUES['water'])
        if nodata is not None:
            valid_mask = (arr != nodata)
            water_mask &= valid_mask
        height, width = arr.shape

        # Apply spatial mask if provided
        if mask_file is not None:
            try:
                spatial_mask = load_mask_from_file(mask_file, ds.crs, ds.transform, (height, width))
                if spatial_mask is not None:
                    water_mask &= spatial_mask
            except Exception as e:
                print(f"Warning: Failed to apply mask from {mask_file}: {e}")
                print("Proceeding without spatial mask...")

        if is_geographic_crs(ds.crs):
            area_per_row = per_row_pixel_area_geographic(ds.transform, height, width)
            # Vectorized row-wise counts
            counts_per_row = water_mask.sum(axis=1).astype(np.float64)
            area_m2 = float(np.dot(counts_per_row, area_per_row))
        else:
            pix_area = per_pixel_area_projected(ds.transform)
            area_m2 = float(water_mask.sum(dtype=np.int64)) * pix_area

        ts = parse_timestamp_from_name(tif_path.name)
        return ts, area_m2


def scan_tifs(tif_dir: Path, pattern: str) -> List[Path]:
    return sorted([p for p in tif_dir.rglob(pattern) if p.suffix.lower() in ('.tif', '.tiff')])


def main():
    parser = argparse.ArgumentParser(description="Compute monthly water area from restored TIFs and plot monthly changes.")
    parser.add_argument('--tif_dir', type=str, required=True, help='Directory containing restored TIFs (e.g., output_dir from restoration).')
    parser.add_argument('--pattern', type=str, default='mosaic_*.tif', help="Glob pattern to find TIFs (default: 'mosaic_*.tif').")
    parser.add_argument('--out_dir', type=str, default=None, help='Directory to save CSV and plot (default: same as tif_dir).')
    parser.add_argument('--csv_name', type=str, default='monthly_water_area.csv', help='Output CSV filename.')
    parser.add_argument('--fig_name', type=str, default='monthly_water_area.png', help='Output figure filename.')
    parser.add_argument('--mask_file', type=str, default=None,
                       help='Optional path to shapefile (.shp) or TIF file (.tif/.tiff) defining the area of interest. '
                            'Only water pixels within this area will be counted.')

    args = parser.parse_args()
    tif_dir = Path(args.tif_dir)
    out_dir = Path(args.out_dir) if args.out_dir else tif_dir
    out_dir.mkdir(parents=True, exist_ok=True)

    mask_file = Path(args.mask_file) if args.mask_file else None

    # Validate mask file if provided
    if mask_file is not None:
        if not mask_file.exists():
            raise FileNotFoundError(f"Mask file not found: {mask_file}")

        file_ext = mask_file.suffix.lower()
        if file_ext == '.shp' and not GEOPANDAS_AVAILABLE:
            raise RuntimeError("geopandas is required to process shapefile masks. "
                             "Install with: pip install geopandas")

        if file_ext not in ('.shp', '.tif', '.tiff'):
            raise ValueError(f"Unsupported mask file format: {file_ext}. "
                           "Supported formats: .shp, .tif, .tiff")

    tifs = scan_tifs(tif_dir, args.pattern)
    if not tifs:
        raise FileNotFoundError(f"No TIFs found under {tif_dir} with pattern {args.pattern}")

    records = []
    for tif in tifs:
        ts, area_m2 = compute_water_area_m2(tif)
        if ts is None:
            # Skip files without parsable date
            continue
        records.append({'timestamp': ts, 'area_km2': area_m2 / 1e6, 'file': str(tif)})

    if not records:
        raise RuntimeError("No records with parsable timestamps were found. Check filename patterns.")

    df = pd.DataFrame.from_records(records)
    df['month'] = df['timestamp'].dt.to_period('M').dt.to_timestamp()
    monthly = df.groupby('month', as_index=False)['area_km2'].sum().sort_values('month')

    csv_path = out_dir / args.csv_name
    monthly.to_csv(csv_path, index=False)

    # Plot
    plt.figure(figsize=(10, 4))
    plt.plot(monthly['month'], monthly['area_km2'], marker='o')
    plt.grid(True, alpha=0.3)
    plt.title('Monthly Water Area (km^2)')
    plt.xlabel('Month')
    plt.ylabel('Water Area (km^2)')
    plt.tight_layout()
    fig_path = out_dir / args.fig_name
    plt.savefig(fig_path, dpi=150)

    # Simple stdout summary
    print(f"Processed {len(df)} files, covering {len(monthly)} months.")
    print(f"CSV saved to: {csv_path}")
    print(f"Figure saved to: {fig_path}")


if __name__ == '__main__':
    main()

