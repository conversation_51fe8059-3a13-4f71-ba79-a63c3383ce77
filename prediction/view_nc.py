"""
NC Viewer for Inpainted Prediction Files
=======================================

Purpose
- Inspect inpainted NetCDF files produced by prediction/inference.
- Handle dimension names/order, paths, and data encoding.
- For a given idx_x, idx_y and time index, extract the corresponding tile and visualize it.
- Visualization style roughly follows evaluation/visualization.py (custom colormaps and a frequency layered panel if available).

Usage examples
- Use your geoai environment's Python executable directly (do not `conda activate`):
  /path/to/geoai/bin/python prediction/view_nc.py \
    --nc prediction/inference_outputs/inpainted_sample.nc \
    --idx_x 3 --idx_y 7 --time 12 \
    --save out.png --show

Notes
- Expects variable name 'data' by default (as written by inference). You can change via --var.
- If an occurrence/frequency variable exists (e.g., 'occ_data'), it will be shown as a layered color panel.
- If the data is uint8 in [0,100], it will be converted to [0,1] for visualization.
"""

from pathlib import Path
import argparse
import sys
from typing import Dict, Optional, Tuple, List, Union

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.patches as mpatches
from matplotlib.colors import LinearSegmentedColormap
import pandas as pd

JRC_VALUES = {
    'no_data': 255,
    'land': 1,
    'water': 2,
    'no_observation': 0
}

def _lower_name(name: str) -> str:
    return name.lower() if isinstance(name, str) else str(name)


def _find_axis(dims: Tuple[str, ...], candidates: List[str]) -> Optional[int]:
    """Find axis index whose name equals or contains any candidate substring (case-insensitive)."""
    dims_l = [_lower_name(d) for d in dims]
    for i, d in enumerate(dims_l):
        for c in candidates:
            c_l = _lower_name(c)
            if d == c_l or c_l in d:
                return i
    return None


def _detect_axes(var) -> Dict[str, Optional[int]]:
    dims = tuple(var.dims)
    time_axis = _find_axis(dims, ["time"])  # time
    idx_x_axis = _find_axis(dims, ["idx_x", "x_idx", "tile_x"])  # tile x index
    idx_y_axis = _find_axis(dims, ["idx_y", "y_idx", "tile_y"])  # tile y index
    # Remaining spatial dims (e.g., y/x, height/width, lat/lon)
    spatial_candidates = ["y", "x", "height", "width", "lat", "latitude", "lon", "longitude", "row", "col"]
    # Collect all candidate positions not already taken
    taken = {ax for ax in [time_axis, idx_x_axis, idx_y_axis] if ax is not None}
    spatial_axes = []
    for cand in spatial_candidates:
        ax = _find_axis(dims, [cand])
        if ax is not None and ax not in taken and ax not in spatial_axes:
            spatial_axes.append(ax)
        if len(spatial_axes) >= 2:
            break
    # Fallback: choose remaining dims in order
    if len(spatial_axes) < 2:
        for i in range(len(dims)):
            if i not in taken and i not in spatial_axes:
                spatial_axes.append(i)
            if len(spatial_axes) >= 2:
                break
    return {
        "time": time_axis,
        "idx_x": idx_x_axis,
        "idx_y": idx_y_axis,
        "spatial": spatial_axes[:2],
        "dims": dims,
    }


def _scale_to_unit(arr: np.ndarray) -> np.ndarray:
    """Scale/cast data for visualization:
    - JRC categorical (0=no_obs,1=land,2=water,255=no_data) -> float with {NaN,0,1}
    - uint8 percentages [0,100] -> [0,1]
    - others -> float32
    """
    if arr.dtype == np.uint8:
        # Treat as 0..100 percentages from inference
        if arr.max(initial=0) > 1:
            return (arr.astype(np.float32) / 100.0).clip(0.0, 1.0)
        return arr.astype(np.float32)
    if not np.issubdtype(arr.dtype, np.floating):
        arr = arr.astype(np.float32)
    return arr


def _layered_frequency_rgb(freq: np.ndarray) -> np.ndarray:
    """Create layered RGB map following evaluation/visualization.py style.
    Layers: background white; >0 low blue; 0.2-0.8 mid; 0.4-0.6 high.
    """
    freq_colors = {
        'background': '#FFFFFF',  # white
        'low': '#A6CEE3',         # light blue (0-0.2 and 0.8-1.0)
        'mid': '#1F78B4',         # mid blue (0.2-0.4, 0.6-0.8)
        'high': '#08306B',        # deep blue (0.4-0.6)
    }
    rgb_bg = mcolors.hex2color(freq_colors['background'])
    rgb_low = mcolors.hex2color(freq_colors['low'])
    rgb_mid = mcolors.hex2color(freq_colors['mid'])
    rgb_high = mcolors.hex2color(freq_colors['high'])

    out = np.empty((freq.shape[0], freq.shape[1], 3), dtype=np.float32)
    out[:] = rgb_bg

    mask_0_1 = freq > 0
    mask_0_2_0_8 = (freq >= 0.2) & (freq <= 0.8)
    mask_0_4_0_6 = (freq >= 0.4) & (freq <= 0.6)

    out[mask_0_1] = rgb_low
    out[mask_0_2_0_8] = rgb_mid
    out[mask_0_4_0_6] = rgb_high
    return out


def _yb_colormap() -> LinearSegmentedColormap:
    colors_yellow_blue = [(1, 1, 0), (0, 0, 1)]
    return LinearSegmentedColormap.from_list("custom_yb", colors_yellow_blue, N=100)


def extract_tile(ds: xr.Dataset, var_name: str, idx_x: int, idx_y: int, time_idx: int) -> np.ndarray:
    if var_name not in ds.data_vars:
        raise KeyError(f"Variable '{var_name}' not found. Available: {list(ds.data_vars)}")
    var = ds[var_name]
    axes = _detect_axes(var)
    dims = axes["dims"]

    # Build isel mapping by dimension name
    indexers = {}
    if axes["time"] is not None:
        indexers[dims[axes["time"]]] = int(time_idx)
    if axes["idx_x"] is not None:
        indexers[dims[axes["idx_x"]]] = int(idx_x)
    if axes["idx_y"] is not None:
        indexers[dims[axes["idx_y"]]] = int(idx_y)

    # Select tile
    tile = var.isel(**indexers)

    # After isel, expect a 2D array (H, W). If 3D remains, try to squeeze/handle
    data = tile.values
    if data.ndim == 3:
        # Try squeeze singleton dims; else take the first along the smallest axis
        squeezed = np.squeeze(data)
        if squeezed.ndim == 2:
            data = squeezed
        else:
            # pick first slice along the first axis
            data = data[0]
    if data.ndim != 2:
        raise ValueError(f"Expected 2D tile after indexing, got shape {data.shape} for dims {tile.dims}")

    return _scale_to_unit(data)


def try_extract_frequency(ds: xr.Dataset, idx_x: int, idx_y: int) -> Optional[np.ndarray]:
    # Heuristic variable names for occurrence/frequency
    cand_names = ["occ_data", "occurrence", "frequency", "water_freq", "freq"]
    for name in cand_names:
        if name in ds.data_vars:
            var = ds[name]
            axes = _detect_axes(var)
            dims = axes["dims"]
            indexers = {}
            if axes["idx_x"] is not None:
                indexers[dims[axes["idx_x"]]] = int(idx_x)
            if axes["idx_y"] is not None:
                indexers[dims[axes["idx_y"]]] = int(idx_y)
            try:
                arr = var.isel(**indexers).values
                arr = np.squeeze(arr)
                if arr.ndim == 3 and axes["time"] is not None:
                    # If time dimension exists here, average over time
                    arr = arr.mean(axis=axes["time"])  # may be wrong axis after squeeze; fallback below
                if arr.ndim == 3:
                    arr = arr.mean(axis=0)
                if arr.ndim == 2:
                    # Assume already 0..1, but ensure float
                    return arr.astype(np.float32)
            except Exception:
                continue
    return None



def composite_time(ds: xr.Dataset, var_name: str, time_idx: int) -> np.ndarray:
    """Stitch all tiles over idx_x/idx_y at a given time into one large 2D array.
    Uses dataset attrs tile_size and tile_overlap if available; otherwise infers from dims.
    Overlaps are averaged in overlapping regions.
    """
    if var_name not in ds.data_vars:
        raise KeyError(f"Variable '{var_name}' not found. Available: {list(ds.data_vars)}")
    var = ds[var_name]

    axes = _detect_axes(var)
    dims = axes["dims"]

    # Select time slice if time dimension exists
    if axes["time"] is not None:
        time_dim = dims[axes["time"]]
        var_t = var.isel({time_dim: int(time_idx)})
    else:
        var_t = var

    # Re-detect axes after removing time
    axes_t = _detect_axes(var_t)
    dims_t = tuple(var_t.dims)
    ix_axis = axes_t["idx_x"]
    iy_axis = axes_t["idx_y"]

    # Determine spatial dim names by explicit name matching
    dim_names = list(dims_t)
    y_candidates = [d for d in dim_names if _lower_name(d) in ("y", "height", "lat", "latitude", "row")]
    x_candidates = [d for d in dim_names if _lower_name(d) in ("x", "width", "lon", "longitude", "col")]
    y_dim = y_candidates[0] if y_candidates else None
    x_dim = x_candidates[0] if x_candidates else None

    # If no tile indices, assume var_t is already a 2D field with spatial dims present
    if ix_axis is None and iy_axis is None:
        if y_dim is None or x_dim is None:
            raise ValueError(f"Cannot determine spatial axes for variable '{var_name}' dims={dims_t}")
        arr = var_t.transpose(y_dim, x_dim).values
        return _scale_to_unit(np.squeeze(arr))

    # Determine grid sizes
    nx = int(var_t.sizes[dims_t[ix_axis]]) if ix_axis is not None else 1
    ny = int(var_t.sizes[dims_t[iy_axis]]) if iy_axis is not None else 1

    # Attributes for overlap/size
    # If spatial dims exist on variable (e.g., 'data'), use those tile sizes; otherwise use attrs for stat variables
    if y_dim is not None and x_dim is not None:
        tile_h = int(var_t.sizes[y_dim])
        tile_w = int(var_t.sizes[x_dim])
    else:
        tile_h = tile_w = int(ds.attrs.get('tile_size', 256))

    tile_size_attr = int(ds.attrs.get('tile_size', max(tile_h, tile_w)))
    overlap_attr = int(ds.attrs.get('tile_overlap', 0))
    stride = tile_size_attr - overlap_attr if overlap_attr < tile_size_attr else tile_size_attr

    # Composite size; prefer attr 'large_window_size' if present
    large_size = int(ds.attrs.get('large_window_size', ny * tile_h - (ny - 1) * (tile_h - stride)))
    comp_h = large_size
    comp_w = large_size

    accum = np.zeros((comp_h, comp_w), dtype=np.float32)
    weight = np.zeros((comp_h, comp_w), dtype=np.float32)

    # Iterate grid and place tiles
    for ix in range(nx):
        for iy in range(ny):
            indexers = {}
            if ix_axis is not None:
                indexers[dims_t[ix_axis]] = ix
            if iy_axis is not None:
                indexers[dims_t[iy_axis]] = iy
            tile_da = var_t.isel(**indexers)
            if y_dim is not None and x_dim is not None:
                tile_da = tile_da.transpose(y_dim, x_dim)
                tile_arr = _scale_to_unit(np.squeeze(tile_da.values))
            else:
                # Stat variable: scalar per tile; fill tile array with that value
                val = float(np.asarray(tile_da.values).squeeze())
                tile_arr = np.full((tile_h, tile_w), val, dtype=np.float32)
            h, w = tile_arr.shape
            row0 = iy * stride
            col0 = ix * stride
            row1 = row0 + h
            col1 = col0 + w
            # Bounds safety
            if row1 > comp_h or col1 > comp_w:
                # clip to bounds
                h_clip = min(h, comp_h - row0)
                w_clip = min(w, comp_w - col0)
                if h_clip <= 0 or w_clip <= 0:
                    continue
                tile_arr = tile_arr[:h_clip, :w_clip]
                row1 = row0 + h_clip
                col1 = col0 + w_clip
            accum[row0:row1, col0:col1] += tile_arr
            weight[row0:row1, col0:col1] += 1.0

    # Avoid division by zero
    mask = weight > 0
    out = np.zeros_like(accum)
    out[mask] = accum[mask] / weight[mask]
    return out


def visualize_tile(tile01: np.ndarray, freq: Optional[np.ndarray], title: str = "", grid_stride: Optional[int] = None) -> None:
    # Layout: prediction + optional frequency layered
    ncols = 2 if freq is not None else 1
    fig, axes = plt.subplots(1, ncols, figsize=(10 * ncols, 8))
    if ncols == 1:
        axes = [axes]

    # Prediction panel
    cmap = _yb_colormap()
    im0 = axes[0].imshow(tile01, cmap=cmap, vmin=0, vmax=1)
    axes[0].set_title((title + " - Prediction").strip(" -"))
    axes[0].axis('off')
    plt.colorbar(im0, ax=axes[0], fraction=0.046)

    # Optional grid overlay to inspect seams
    if grid_stride and grid_stride > 0:
        h, w = tile01.shape
        for x in range(0, w, grid_stride):
            axes[0].axvline(x - 0.5, color='k', linestyle='--', linewidth=0.5, alpha=0.3)
        for y in range(0, h, grid_stride):
            axes[0].axhline(y - 0.5, color='k', linestyle='--', linewidth=0.5, alpha=0.3)

    # Frequency layered panel
    if freq is not None:
        layered = _layered_frequency_rgb(freq)
        axes[1].imshow(layered)
        axes[1].set_title("Frequency Layers")
        axes[1].axis('off')
        legend_elements = [
            mpatches.Patch(color="#A6CEE3", label="0.0-0.2, 0.8-1.0"),
            mpatches.Patch(color="#1F78B4", label="0.2-0.8"),
            mpatches.Patch(color="#08306B", label="0.4-0.6"),
        ]
        axes[1].legend(handles=legend_elements, loc='lower right', fontsize='small')

    plt.tight_layout()


def print_dataset_info(ds: xr.Dataset, var_name: str) -> None:
    print("=== Dataset Info ===")
    print(f"Global attrs: {dict(ds.attrs) if ds.attrs else {}}")
    if var_name in ds.data_vars:
        var = ds[var_name]
        print(f"Variable '{var_name}': dims={tuple(var.dims)} shape={tuple(var.shape)} dtype={var.dtype}")
        print(f"  encoding={getattr(var, 'encoding', {})}")
        print(f"  attrs={getattr(var, 'attrs', {})}")
    else:
        print(f"Variable '{var_name}' not found. Available: {list(ds.data_vars)}")




def _resolve_time_index(nc_path: Path, var_name: str, time_arg: Union[str, int]) -> int:
    """Resolve time_arg (int or 'YYYY-MM' string) into integer index based on dataset's time coord."""
    # If already an int, return directly
    if isinstance(time_arg, int):
        return int(time_arg)
    # If numeric string
    try:
        return int(str(time_arg))
    except Exception:
        pass

    # Open with decode_times=True to read datetime-like coords
    with xr.open_dataset(nc_path, decode_times=True, mask_and_scale=False, cache=False) as ds_dt:
        if var_name not in ds_dt.data_vars:
            # Fallback: try any variable to locate time coord
            if 'time' not in ds_dt.coords:
                raise KeyError(f"Variable '{var_name}' not found and no 'time' coord in dataset for time parsing.")
            time_vals = ds_dt['time'].values
        else:
            var = ds_dt[var_name]
            axes = _detect_axes(var)
            if axes['time'] is None:
                raise KeyError("No time dimension detected for variable when resolving time index.")
            time_dim = tuple(var.dims)[axes['time']]
            # Prefer coord on variable; else global coord
            if time_dim in var.coords:
                time_vals = var.coords[time_dim].values
            elif time_dim in ds_dt.coords:
                time_vals = ds_dt.coords[time_dim].values
            else:
                raise KeyError(f"Time dimension '{time_dim}' has no coordinate found.")

        # Convert coordinates to pandas datetime when possible
        time_dt = pd.to_datetime(time_vals, errors='coerce')
        target_str = str(time_arg).strip()
        # Build target month string
        try:
            target_dt = pd.to_datetime(target_str)
            target_month = target_dt.strftime('%Y-%m')
        except Exception:
            # Directly treat as 'YYYY-MM'
            target_month = target_str

        # Exact month match first
        try:
            times_month = pd.Series(time_dt).dt.strftime('%Y-%m')
        except Exception:
            # If conversion failed (all NaT), try string matching on original values
            times_month = pd.Series(pd.Index(time_vals).astype(str)).str.slice(0, 7)

        matches = np.where(times_month.values == target_month)[0]
        if matches.size > 0:
            return int(matches[0])

        # Fallback: nearest by absolute time difference (requires valid datetimes)
        if not pd.isna(time_dt).all():
            diffs = np.abs((time_dt - pd.to_datetime(target_month)).astype('timedelta64[D]').astype('int64'))
            best = int(np.nanargmin(diffs))
            return best

        raise ValueError(f"Could not resolve time index for '{time_arg}'.")

def main(argv=None):
    parser = argparse.ArgumentParser(description="View a tile from an inpainted NetCDF file.")
    parser.add_argument('--nc', required=True, type=str, help='Path to NetCDF file (e.g., inpainted_*.nc)')
    parser.add_argument('--var', default='data', type=str, help="Variable name to visualize (default: 'data')")
    parser.add_argument('--idx_x', type=int, default=0, help='Tile index along idx_x (optional; default 0)')
    parser.add_argument('--idx_y', type=int, default=0, help='Tile index along idx_y (optional; default 0)')
    parser.add_argument('--time', required=True, type=str, help="Time index (int) or label like 'YYYY-MM'")
    parser.add_argument('--composite', action='store_true', help='When set, stitch all idx_x/idx_y tiles at the given time into a single large image')
    parser.add_argument('--save', default=None, type=str, help='Path to save the figure (png). If omitted, not saved.')
    parser.add_argument('--grid_stride', type=int, default=None, help='Draw grid lines on the image every N pixels (e.g., 240) to inspect tile seams')
    parser.add_argument('--show', action='store_true', help='Show the figure window')
    args = parser.parse_args(argv)

    nc_path = Path(args.nc)
    if not nc_path.exists():
        print(f"Error: file not found: {nc_path}")
        sys.exit(1)

    # Resolve time index (supports 'YYYY-MM')
    time_idx = _resolve_time_index(nc_path, args.var, args.time)

    # Open without times decoding/scaling to match inference I/O
    with xr.open_dataset(nc_path, decode_times=False, mask_and_scale=False, cache=False) as ds:
        print_dataset_info(ds, args.var)

        if args.composite:
            tile01 = composite_time(ds, args.var, time_idx)
            freq = None
        else:
            # Extract tile for the given indices
            tile01 = extract_tile(ds, args.var, args.idx_x, args.idx_y, time_idx)
            # Try to extract frequency (optional)
            freq = try_extract_frequency(ds, args.idx_x, args.idx_y)

    title = f"{nc_path.name} {'composite' if args.composite else f'idx_x={args.idx_x} idx_y={args.idx_y}'} t={args.time}"
    visualize_tile(tile01, freq, title, grid_stride=args.grid_stride)

    if args.save:
        out_path = Path(args.save)
        out_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(out_path, dpi=300, bbox_inches='tight')
        print(f"Saved figure to {out_path}")

    if args.show:
        plt.show()
    else:
        plt.close('all')


if __name__ == '__main__':
    main()

