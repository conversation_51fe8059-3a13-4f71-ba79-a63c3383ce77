"""
Swin Transformer U-Net Decoder for Video Water Body Detection
Designed to work with Swin Transformer encoder features
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import logging

logger = logging.getLogger(__name__)


class UpBlock(nn.Module):
    """Single upsampling block for U-Net decoder"""

    def __init__(self, in_channels, skip_channels, out_channels):
        super().__init__()
        self.upsample = nn.ConvTranspose2d(in_channels, out_channels, 4, 2, 1)
        self.norm1 = nn.GroupNorm(max(1, out_channels // 16), out_channels)
        self.activation1 = nn.GELU()

        # Fusion layer for skip connection
        self.fusion = nn.Conv2d(out_channels + skip_channels, out_channels, 3, padding=1)
        self.norm2 = nn.GroupNorm(max(1, out_channels // 16), out_channels)
        self.activation2 = nn.GELU()

    def forward(self, x, skip_feature):
        # Upsample
        x = self.upsample(x)
        x = self.norm1(x)
        x = self.activation1(x)

        # Align spatial dimensions if needed
        if x.shape[2:] != skip_feature.shape[2:]:
            logger.warning(f"Skip feature resolution {skip_feature.shape[2:]} does not match upsampled resolution {x.shape[2:]}. Resizing.")
            skip_feature = F.interpolate(skip_feature, size=x.shape[2:], mode='bilinear', align_corners=False)

        # Concatenate and fuse
        x = torch.cat([x, skip_feature], dim=1)
        x = self.fusion(x)
        x = self.norm2(x)
        x = self.activation2(x)

        return x

class Decoder(nn.Module):
    """
    U-Net style decoder for Swin Transformer features
    Processes multi-scale encoder features and outputs segmentation
    """

    def __init__(self, img_size=256, num_classes=2,
                 embed_dim=96, depths=[2, 2, 6, 2]):
        super().__init__()
        self.img_size = img_size
        self.num_classes = num_classes
        self.num_stage = len(depths)
        # Calculate feature dimensions based on Swin Transformer architecture
        # Features are ordered from deepest to shallowest: [stage3, stage2, stage1, stage0]
        self.feature_dims = [embed_dim * (2 ** i) for i in range(self.num_stage)][::-1]  # [768, 384, 192, 96] for embed_dim=96

        # Build upsampling blocks
        self.up_blocks = nn.ModuleList()
        for i in range(self.num_stage - 1):
            in_ch = self.feature_dims[i]      # Current level channels
            skip_ch = self.feature_dims[i+1]  # Skip connection channels
            out_ch = skip_ch                  # Output channels (same as skip)

            self.up_blocks.append(UpBlock(in_ch, skip_ch, out_ch))
        
        # Final upsampling input channels
        final_in_ch = self.feature_dims[-1]
        
        # Final upsampling to original resolution
        self.final_upsample = nn.Sequential(
            nn.ConvTranspose2d(final_in_ch, 64, 4, 2, 1),
            nn.GroupNorm(8, 64),
            nn.GELU(),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.GroupNorm(4, 32),
            nn.GELU(),
            nn.ConvTranspose2d(32, 16, 4, 2, 1),
            nn.GroupNorm(2, 16),
            nn.GELU(),
            nn.Conv2d(16, 16, 3, padding=1),
            nn.GroupNorm(2, 16),
            nn.GELU()
        )

        # Final classification layer
        self.final_conv = nn.Sequential(
            nn.Conv2d(16, 8, 3, padding=1),
            nn.GroupNorm(2, 8),
            nn.GELU(),
            nn.Conv2d(8, num_classes, 1)
        )

    def forward(self, encoder_features):
        """
        Forward pass implementation
        Args:
            encoder_features: List of features from encoder [deepest, ..., shallowest]
        Returns:
            logits: (B, num_classes, H, W) segmentation logits
        """
        # Start with deepest features
        x = encoder_features[0]
        skip_features = encoder_features[1:]

        # Progressive upsampling with skip connections
        for i, up_block in enumerate(self.up_blocks):
            skip_feature = skip_features[i]
            x = up_block(x, skip_feature)

        # Final upsampling to original resolution
        x = self.final_upsample(x)

        # Ensure output matches target size
        if x.shape[2:] != (self.img_size, self.img_size):
            logger.warning(f"Output resolution {x.shape[2:]} does not match target resolution {self.img_size}. Resizing.")
            x = F.interpolate(x, size=(self.img_size, self.img_size), mode='bilinear', align_corners=False)

        # Final classification
        logits = self.final_conv(x)

        # Numerical stability
        logits = torch.nan_to_num(logits, nan=0.0, posinf=10.0, neginf=-10.0)
        logits = torch.clamp(logits, min=-10.0, max=10.0)

        return logits