"""
BCE + Dice Combined Loss Function for Probability Prediction

Combines Binary Cross-Entropy and Dice losses for models outputting probability values.
Both model outputs and labels are probability values in [0, 1] range.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import Dict, Tuple, Optional
from evaluation.metrics import MetricsCalculator

logger = logging.getLogger(__name__)


class Loss(nn.Module):
    """
    Combined BCE + Dice loss for probability prediction
    """
    
    def __init__(self, 
                 bce_weight=0.5,
                 dice_weight=0.5,
                 dice_smooth=1e-6):
        super().__init__()
        
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.dice_smooth = dice_smooth
        
        # Loss bounds for numerical stability
        self.register_buffer('max_loss_value', torch.tensor(10.0))
        self.register_buffer('min_loss_value', torch.tensor(1e-6))

        self.metrics_calculator = MetricsCalculator()
        
    def to(self, device):
        """Ensures all buffers and parameters are moved to the specified device"""
        super().to(device)
        return self
    
    def _compute_bce_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                          mask: torch.Tensor) -> torch.Tensor:
        """
        Compute Binary Cross-Entropy loss
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1]
            mask: (B, H, W) - valid mask
            
        Returns:
            bce_loss: scalar tensor
        """
        device = predictions.device
        valid_mask = mask.bool()
                        
        # Apply mask
        pred_valid = predictions[valid_mask]
        target_valid = targets[valid_mask]
                
        # Compute BCE loss
        bce_loss = F.binary_cross_entropy(pred_valid, target_valid, reduction='mean')
        
        # Safety check
        bce_loss = torch.clamp(bce_loss, min=self.min_loss_value, max=self.max_loss_value)
        
        if torch.isnan(bce_loss) or torch.isinf(bce_loss):
            logger.warning("NaN/Inf in BCE loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True)
        
        return bce_loss

    def _compute_dice_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                          mask: torch.Tensor) -> torch.Tensor:
        """
        Compute Dice loss
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1]
            mask: (H, W) - valid mask
            
        Returns:
            dice_loss: scalar tensor
        """
        device = predictions.device
        valid_mask = mask.bool()
                        
        # Apply mask
        pred_valid = predictions[valid_mask]
        target_valid = targets[valid_mask]
                        
        # Compute Dice coefficient
        intersection = (pred_valid * target_valid).sum()
        union = pred_valid.sum() + target_valid.sum()
        
        dice_coeff = (2.0 * intersection + self.dice_smooth) / (union + self.dice_smooth)
        dice_loss = 1.0 - dice_coeff
        
        # Safety check
        dice_loss = torch.clamp(dice_loss, min=0.0, max=1.0)
        
        if torch.isnan(dice_loss) or torch.isinf(dice_loss):
            logger.warning("NaN/Inf in Dice loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True)
        
        return dice_loss
    
    def _compute_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                     mask: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        """
        Compute combined BCE + Dice loss
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1]
            mask: (H, W) - valid mask
            
        Returns:
            total_loss: scalar tensor
            metrics: dictionary containing individual loss values
        """
        bce_loss = self._compute_bce_loss(predictions, targets, mask)
        dice_loss = self._compute_dice_loss(predictions, targets, mask)
        
        total_loss = self.bce_weight * bce_loss + self.dice_weight * dice_loss
        
        return total_loss

    def _calculate_metrics(self, predictions: torch.Tensor, targets: torch.Tensor,
                          mask: torch.Tensor, water_frequency: Optional[torch.Tensor]) -> Dict:
        """
        Calculate metrics for inpainting task
        """
        # Compute metrics
        with torch.no_grad():
            pred_binary = (predictions > 0.5).bool()

            target_bool = (targets > 0.5).bool()
            valid_mask_bool = mask.bool()

            eval_metrics = self.metrics_calculator.calculate_metrics(
                pred_binary, target_bool, valid_mask_bool, water_frequency
            )

            return eval_metrics

    def forward(self, outputs: Dict, batch: Dict, mode: str = 'train') -> Tuple[torch.Tensor, Dict]:
        """
        Compute combined BCE + Dice loss
        
        Args:
            outputs: Model outputs containing 'inpaint' predictions
            batch: Batch data containing ground truth and masks
            mode: 'train' or 'eval' (for compatibility)
        """
        device = batch['ground_truth'].device
        self.to(device)
        
        # Extract data
        target = batch['ground_truth']  # (B, H, W) - probability values
        # Normalize targets to [0, 1] if needed
        if target.max() > 1.0:
            raise ValueError("Target values are not in [0, 1] range")

        mask = batch['missing_mask']    # (H, W) - valid mask
        if mask.sum() == 0:
            raise ValueError("Missing mask is all zeros")

        water_frequency = batch.get('occurrence')

        # Get predictions
        pred_logits = outputs['inpaint']['logits']  # (B, 2, H, W)
        pred_probabilities = torch.softmax(pred_logits, dim=1)[:, 1, :, :]  # (B, H, W)
        
        total_loss = self._compute_loss(pred_probabilities, target, mask)

        if mode == 'train':
            return total_loss
        elif mode == 'eval':
            metrics = self._calculate_metrics(pred_probabilities, target, mask, water_frequency)
            return total_loss, metrics
        else:
            raise ValueError(f"Unknown mode {mode}")
        