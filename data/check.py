import os
import xarray as xr
import numpy as np
from pathlib import Path
import shutil
from tqdm import tqdm
from datetime import datetime

def check_variable_range(ds, var_name, min_val, max_val):
    """
    检查变量的值域是否在指定范围内
    
    Args:
        ds: xarray数据集
        var_name: 变量名
        min_val: 最小值
        max_val: 最大值
    
    Returns:
        tuple: (是否在范围内, 实际最小值, 实际最大值)
    """
    try:
        data = ds[var_name].values
        # 忽略NaN值
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) == 0:
            return True, None, None  # 如果全是NaN，认为是有效的
        
        actual_min = np.min(valid_data)
        actual_max = np.max(valid_data)
        
        is_valid = actual_min >= min_val and actual_max <= max_val
        
        return is_valid, actual_min, actual_max
    except Exception as e:
        print(f"检查变量 {var_name} 值域时出错: {e}")
        return False, None, None

def check_nc_file_complete(file_path, required_variables, value_ranges):
    """
    检查单个nc文件是否包含所有必需的变量并且值域正确
    
    Args:
        file_path: nc文件路径
        required_variables: 必需的变量列表
        value_ranges: 各变量的值域要求
    
    Returns:
        tuple: (是否通过检查, 错误信息列表)
    """
    errors = []
    
    try:
        # 打开nc文件
        ds = xr.open_dataset(file_path)
        
        # 获取文件中的所有变量
        file_variables = set(ds.data_vars.keys())
        
        # 检查是否包含所有必需的变量
        missing_vars = set(required_variables) - file_variables
        
        if missing_vars:
            errors.append(f"缺少变量: {missing_vars}")
            ds.close()
            return False, errors
        
        # # 检查每个变量的值域
        # print(f"\n检查文件: {file_path.name}")
        # for var_name, (min_val, max_val) in value_ranges.items():
        #     if var_name in file_variables:
        #         is_valid, actual_min, actual_max = check_variable_range(ds, var_name, min_val, max_val)
                
        #         if not is_valid:
        #             error_msg = f"变量 '{var_name}' 值域错误: 期望[{min_val}, {max_val}], 实际[{actual_min:.4f}, {actual_max:.4f}]"
        #             errors.append(error_msg)
        #             print(f"  ✗ {error_msg}")
        #         else:
        #             if actual_min is not None:
        #                 print(f"  ✓ {var_name}: [{actual_min:.4f}, {actual_max:.4f}] 在范围 [{min_val}, {max_val}] 内")
        #             else:
        #                 print(f"  ✓ {var_name}: 全部为NaN值")
        
        # 关闭数据集
        ds.close()
        
        if errors:
            return False, errors
        else:
            # print(f"  ✓ 文件检查通过")
            return True, []
            
    except Exception as e:
        errors.append(f"读取文件时出错: {e}")
        return False, errors

def check_and_clean_nc_files(folder_path, backup=True):
    """
    检查文件夹内所有nc文件，删除或备份不符合要求的文件
    """
    # 定义必需的变量列表
    required_variables = [
        'data',
        'water_proportion',
        'missing_proportion',
        'mean_water_frequency',
        'occ_data',
        'tile_col_offset',
        'tile_row_offset',
        'tile_lon',
        'tile_lat'
    ]
    
    # 定义各变量的值域要求
    value_ranges = {
        'data': (0, 100),
        'water_proportion': (0, 1),
        'missing_proportion': (0, 1),
        'mean_water_frequency': (0, 1),
        'occ_data': (0, 100)
    }
    
    folder = Path(folder_path)
    
    if not folder.exists():
        print(f"文件夹 {folder_path} 不存在")
        return
    
    # 创建备份文件夹
    if backup:
        backup_folder = folder / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_folder.mkdir(exist_ok=True)
        print(f"创建备份文件夹: {backup_folder}")
    
    nc_files = list(folder.glob("*.nc"))
    
    if not nc_files:
        print(f"文件夹 {folder_path} 中没有找到nc文件")
        return
    
    print(f"\n找到 {len(nc_files)} 个nc文件")
    print("=" * 80)
    
    valid_files = []
    invalid_files = []
    error_details = {}
    
    for nc_file in tqdm(nc_files):
        is_valid, errors = check_nc_file_complete(nc_file, required_variables, value_ranges)
        
        if is_valid:
            valid_files.append(nc_file)
        else:
            invalid_files.append(nc_file)
            error_details[nc_file.name] = errors
            
            if backup:
                # 移动到备份文件夹
                try:
                    shutil.move(str(nc_file), str(backup_folder / nc_file.name))
                    print(f"  → 已将文件移动到备份文件夹")
                except Exception as e:
                    print(f"  → 移动文件时出错: {e}")
            else:
                # 直接删除
                try:
                    os.remove(nc_file)
                    print(f"  → 已删除文件")
                except Exception as e:
                    print(f"  → 删除文件时出错: {e}")
    
    # 打印详细的错误报告
    print("\n" + "=" * 80)
    print("检查完成！")
    print(f"✓ 有效文件数: {len(valid_files)}")
    print(f"✗ 无效文件数: {len(invalid_files)}")
    
    if invalid_files:
        print("\n无效文件详情:")
        for file_name, errors in error_details.items():
            print(f"\n{file_name}:")
            for error in errors:
                print(f"  - {error}")
        
        if backup:
            print(f"\n无效文件已移动到: {backup_folder}")

def generate_summary_report(folder_path):
    """
    生成检查报告
    """
    required_variables = [
        'data',
        'water_proportion',
        'missing_proportion',
        'mean_water_frequency',
        'occ_data',
        'tile_col_offset',
        'tile_row_offset',
        'tile_lon',
        'tile_lat'
    ]
    
    value_ranges = {
        'data': (0, 100),
        'water_proportion': (0, 1),
        'missing_proportion': (0, 1),
        'mean_water_frequency': (0, 1),
        'occ_data': (0, 100)
    }
    
    folder = Path(folder_path)
    nc_files = list(folder.glob("*.nc"))
    
    report_file = folder / f"nc_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"NC文件检查报告\n")
        f.write(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"文件夹路径: {folder_path}\n")
        f.write(f"文件总数: {len(nc_files)}\n")
        f.write("=" * 80 + "\n\n")
        
        for nc_file in nc_files:
            f.write(f"\n文件: {nc_file.name}\n")
            is_valid, errors = check_nc_file_complete(nc_file, required_variables, value_ranges)
            
            if is_valid:
                f.write("状态: ✓ 通过\n")
            else:
                f.write("状态: ✗ 未通过\n")
                f.write("错误详情:\n")
                for error in errors:
                    f.write(f"  - {error}\n")
            f.write("-" * 40 + "\n")
    
    print(f"\n检查报告已保存到: {report_file}")

def main():
    """
    主函数
    """
    print("NC文件完整性检查工具")
    print("=" * 80)
    print("检查项目:")
    print("1. 必需变量: data, water_proportion, missing_proportion, mean_water_frequency,")
    print("   occ_data, tile_col_offset, tile_row_offset, tile_lon, tile_lat")
    print("2. 值域检查:")
    print("   - data: [0, 100]")
    print("   - water_proportion: [0, 1]")
    print("   - missing_proportion: [0, 1]")
    print("   - mean_water_frequency: [0, 1]")
    print("   - occ_data: [0, 100]")
    print("=" * 80)
    
    folder_path = input("\n请输入要检查的文件夹路径: ").strip()
    
    print(f"\n将要检查文件夹: {folder_path}")
    print("\n选择操作模式:")
    print("1. 仅生成检查报告")
    print("2. 备份无效文件（推荐）")
    print("3. 直接删除无效文件")
    
    mode = input("\n请选择 (1/2/3): ").strip()
    
    if mode == '1':
        generate_summary_report(folder_path)
    elif mode == '2':
        confirm = input("\n将备份不符合要求的nc文件，是否继续？(y/n): ").strip().lower()
        if confirm == 'y':
            check_and_clean_nc_files(folder_path, backup=True)
    elif mode == '3':
        print("\n⚠️  警告：将直接删除不符合要求的nc文件！")
        confirm = input("是否继续？(y/n): ").strip().lower()
        if confirm == 'y':
            check_and_clean_nc_files(folder_path, backup=False)
    else:
        print("无效选择，操作已取消")

if __name__ == "__main__":
    main()