#!/usr/bin/env python3
"""
NetCDF File Diagnostic Tool

This script helps diagnose issues with NetCDF files in the dataset,
providing detailed information about file accessibility, structure, and data integrity.
"""

import os
import sys
from pathlib import Path
import xarray as xr
import numpy as np
import pandas as pd
from tqdm import tqdm
import logging
from collections import defaultdict
import argparse

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class NetCDFDiagnostic:
    """Diagnostic tool for NetCDF files"""
    
    def __init__(self):
        self.required_vars = ['water_proportion', 'missing_proportion', 
                             'mean_water_frequency', 'tile_lon', 'tile_lat']
        self.engines = ['netcdf4', 'scipy', 'h5netcdf']
        
        self.results = {
            'total_files': 0,
            'successful': 0,
            'failed_to_open': 0,
            'missing_variables': 0,
            'zero_dimensions': 0,
            'data_integrity_issues': 0,
            'only_nan_data': 0,
            'by_engine': defaultdict(int),
            'error_details': defaultdict(list)
        }
    
    def diagnose_file(self, nc_file: Path) -> dict:
        """
        Diagnose a single NetCDF file
        
        Args:
            nc_file: Path to NetCDF file
            
        Returns:
            Dictionary with diagnostic results
        """
        result = {
            'file': str(nc_file),
            'status': 'unknown',
            'engine_used': None,
            'issues': [],
            'dimensions': {},
            'variables': [],
            'data_samples': {}
        }
        
        # Try to open with different engines
        ds = None
        for engine in self.engines:
            try:
                ds = xr.open_dataset(nc_file, engine=engine)
                result['engine_used'] = engine
                self.results['by_engine'][engine] += 1
                break
            except Exception as e:
                result['issues'].append(f"Failed with {engine}: {str(e)[:100]}")
                continue
        
        if ds is None:
            result['status'] = 'failed_to_open'
            self.results['failed_to_open'] += 1
            return result
        
        try:
            with ds:
                # Check dimensions
                result['dimensions'] = dict(ds.dims)
                if any(size == 0 for size in ds.dims.values()):
                    result['status'] = 'zero_dimensions'
                    result['issues'].append("Zero-sized dimensions found")
                    self.results['zero_dimensions'] += 1
                    return result
                
                # Check variables
                result['variables'] = list(ds.data_vars) + list(ds.coords)
                missing_vars = [var for var in self.required_vars 
                              if var not in result['variables']]
                
                if missing_vars:
                    result['status'] = 'missing_variables'
                    result['issues'].append(f"Missing variables: {missing_vars}")
                    self.results['missing_variables'] += 1
                    return result
                
                # Test data integrity
                try:
                    x_size = ds.dims.get('idx_x', 1)
                    y_size = ds.dims.get('idx_y', 1)
                    t_size = ds.dims.get('time', 1)
                    
                    # Sample a small chunk
                    sample_x = min(2, x_size)
                    sample_y = min(2, y_size) 
                    sample_t = min(2, t_size)
                    
                    water_sample = ds.water_proportion[0:sample_x, 0:sample_y, 0:sample_t].values
                    missing_sample = ds.missing_proportion[0:sample_x, 0:sample_y, 0:sample_t].values
                    
                    result['data_samples']['water_sample_shape'] = water_sample.shape
                    result['data_samples']['water_has_valid'] = not np.all(np.isnan(water_sample))
                    result['data_samples']['missing_has_valid'] = not np.all(np.isnan(missing_sample))
                    
                    if np.all(np.isnan(water_sample)) and np.all(np.isnan(missing_sample)):
                        result['status'] = 'only_nan_data'
                        result['issues'].append("All sampled data is NaN")
                        self.results['only_nan_data'] += 1
                        return result
                    
                    # Test time coordinates
                    if 'time' in ds.coords:
                        time_sample = ds.time[0:min(2, t_size)].values
                        result['data_samples']['time_parseable'] = True
                        try:
                            pd.to_datetime(time_sample)
                        except:
                            result['data_samples']['time_parseable'] = False
                            result['issues'].append("Time coordinates not parseable")
                    
                except Exception as e:
                    result['status'] = 'data_integrity_issues'
                    result['issues'].append(f"Data access failed: {str(e)[:100]}")
                    self.results['data_integrity_issues'] += 1
                    return result
                
                # If we got here, the file is good
                result['status'] = 'success'
                self.results['successful'] += 1
                
        except Exception as e:
            result['status'] = 'error'
            result['issues'].append(f"Unexpected error: {str(e)[:100]}")
            
        return result
    
    def diagnose_directory(self, data_dir: Path, sample_size: int = None) -> dict:
        """
        Diagnose all NetCDF files in a directory
        
        Args:
            data_dir: Directory containing NetCDF files
            sample_size: If specified, only diagnose this many files (for quick testing)
            
        Returns:
            Dictionary with overall diagnostic results
        """
        logger.info(f"Scanning directory: {data_dir}")
        
        # Find all NetCDF files
        nc_files = list(data_dir.rglob("*.nc"))
        if not nc_files:
            logger.error(f"No NetCDF files found in {data_dir}")
            return self.results
        
        logger.info(f"Found {len(nc_files)} NetCDF files")
        
        # Sample files if requested
        if sample_size and sample_size < len(nc_files):
            nc_files = np.random.choice(nc_files, size=sample_size, replace=False)
            logger.info(f"Sampling {sample_size} files for quick diagnosis")
        
        self.results['total_files'] = len(nc_files)
        
        # Diagnose each file
        detailed_results = []
        for nc_file in tqdm(nc_files, desc="Diagnosing files"):
            file_result = self.diagnose_file(nc_file)
            detailed_results.append(file_result)
            
            # Store error details
            if file_result['status'] != 'success':
                self.results['error_details'][file_result['status']].append(file_result)
        
        self.results['detailed_results'] = detailed_results
        
        return self.results
    
    def print_summary(self):
        """Print diagnostic summary"""
        results = self.results
        
        print("\n" + "=" * 80)
        print("NetCDF File Diagnostic Summary")
        print("=" * 80)
        
        print(f"📁 Total files analyzed: {results['total_files']:,}")
        print(f"✅ Successful: {results['successful']:,} ({results['successful']/results['total_files']*100:.1f}%)")
        print(f"❌ Failed: {results['total_files'] - results['successful']:,} ({(results['total_files'] - results['successful'])/results['total_files']*100:.1f}%)")
        
        print(f"\n📊 Issue Breakdown:")
        print(f"   • Failed to open: {results['failed_to_open']:,}")
        print(f"   • Missing variables: {results['missing_variables']:,}")
        print(f"   • Zero dimensions: {results['zero_dimensions']:,}")
        print(f"   • Data integrity issues: {results['data_integrity_issues']:,}")
        print(f"   • Only NaN data: {results['only_nan_data']:,}")
        
        print(f"\n🔧 Engine Usage:")
        for engine, count in results['by_engine'].items():
            print(f"   • {engine}: {count:,} files")
        
        # Show examples of each error type
        print(f"\n🔍 Error Examples:")
        for error_type, examples in results['error_details'].items():
            if examples:
                print(f"\n   {error_type.upper()}:")
                for i, example in enumerate(examples[:3]):  # Show first 3 examples
                    print(f"     {i+1}. {Path(example['file']).name}")
                    if example['issues']:
                        print(f"        Issues: {'; '.join(example['issues'][:2])}")
                if len(examples) > 3:
                    print(f"     ... and {len(examples) - 3} more")
        
        print("=" * 80)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Diagnose NetCDF files in dataset")
    parser.add_argument("data_dir", help="Directory containing NetCDF files")
    parser.add_argument("--sample", "-s", type=int, help="Sample size for quick testing")
    parser.add_argument("--output", "-o", help="Output file for detailed results (JSON)")
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        logger.error(f"Directory not found: {data_dir}")
        return 1
    
    # Run diagnostics
    diagnostic = NetCDFDiagnostic()
    results = diagnostic.diagnose_directory(data_dir, args.sample)
    
    # Print summary
    diagnostic.print_summary()
    
    # Save detailed results if requested
    if args.output:
        import json
        output_path = Path(args.output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Detailed results saved to: {output_path}")
    
    return 0


if __name__ == "__main__":
    exit(main()) 