#!/usr/bin/env python3
"""
Export Missing Patterns from Build Index

This script reads the index file generated by build_index.py and extracts 
the missing samples to create a missing pattern database compatible with dataset.py.

The output npz file will contain missing patterns that can be used for data 
augmentation during training.
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import xarray as xr
from pathlib import Path
from typing import Dict, List, Any, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
from dataclasses import dataclass
from collections import defaultdict

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Try to import matplotlib for visualization
try:
    import matplotlib.pyplot as plt
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None

logger = logging.getLogger(__name__)


@dataclass
class MissingPattern:
    """Represents a missing data pattern"""
    pattern: np.ndarray  # Boolean mask where True = missing
    missing_ratio: float
    pattern_type: str  # 'real'
    shape: tuple
    metadata: Dict[str, Any]


def load_index_file(index_path: Path) -> Dict[str, Any]:
    """
    Load the index file generated by build_index.py
    
    Args:
        index_path: Path to the index JSON file
        
    Returns:
        Dictionary containing index data
    """
    if not index_path.exists():
        raise FileNotFoundError(f"Index file not found: {index_path}")
    
    logger.info(f"Loading index file: {index_path}")
    
    with open(index_path, 'r', encoding='utf-8') as f:
        index_data = json.load(f)
    
    # Validate required keys
    if 'missing_samples_by_ratio' not in index_data:
        raise ValueError("Index file missing 'missing_samples_by_ratio' key")
    
    # Log basic statistics
    missing_samples = index_data['missing_samples_by_ratio']
    total_missing = sum(len(samples) for samples in missing_samples.values())
    
    logger.info(f"Found missing samples across {len(missing_samples)} ratio bins")
    logger.info(f"Total missing samples: {total_missing}")
    
    # Print distribution
    for bin_idx, samples in missing_samples.items():
        logger.info(f"  Bin {bin_idx}: {len(samples)} samples")
    
    return index_data


def extract_pattern_from_sample(sample: Dict[str, Any], min_missing_ratio: float = 0.05) -> Optional[MissingPattern]:
    """
    Extract missing pattern from a single sample
    
    Args:
        sample: Sample dictionary with file path and coordinates
        min_missing_ratio: Minimum missing ratio threshold
        
    Returns:
        MissingPattern object or None if invalid
    """
    try:
        file_path = sample.get('file_path') or sample.get('source_file')
        if not file_path:
            return None
        
        # Ensure indices are integers
        idx_x = int(sample['idx_x'])
        idx_y = int(sample['idx_y'])
        time_idx = int(sample['time_idx'])
        
        # Load and process the data
        with xr.open_dataset(file_path) as ds:
            # Extract data tile
            tile_data = ds.data[idx_x, idx_y, time_idx].values
            
            # Create missing mask (JRC no_data=255, no_observation=0)
            missing_mask = (tile_data == 255) | (tile_data == 0)
            missing_ratio = float(np.mean(missing_mask))
            
            # Only include if missing ratio meets threshold
            if missing_ratio < min_missing_ratio:
                return None
            
            # Create pattern object
            pattern = MissingPattern(
                pattern=missing_mask,
                missing_ratio=missing_ratio,
                pattern_type='real',
                shape=missing_mask.shape,
                metadata={
                    'file_path': file_path,
                    'idx_x': idx_x,
                    'idx_y': idx_y,
                    'time_idx': time_idx,
                    'water_proportion': sample.get('water_proportion', 0.0),
                    'missing_proportion': sample.get('missing_proportion', 0.0),
                    'mean_water_frequency': sample.get('mean_water_frequency', None),
                    'tile_lon': sample.get('tile_lon', 0.0),
                    'tile_lat': sample.get('tile_lat', 0.0),
                    'year': sample.get('year', 0),
                    'month': sample.get('month', 0)
                }
            )
            
            return pattern
            
    except Exception as e:
        logger.debug(f"Error processing sample: {e}")
        return None


def process_file_samples(file_samples: tuple) -> List[MissingPattern]:
    """
    Process samples from a single file
    
    Args:
        file_samples: Tuple of (file_path, samples_list, min_missing_ratio)
        
    Returns:
        List of extracted MissingPattern objects
    """
    file_path, samples, min_missing_ratio = file_samples
    patterns = []
    
    for sample in samples:
        pattern = extract_pattern_from_sample(sample, min_missing_ratio)
        if pattern is not None:
            patterns.append(pattern)
    
    return patterns


def visualize_patterns(patterns: List[MissingPattern], output_file: str, num_samples: int = 16) -> None:
    """
    Create visualization of missing patterns
    
    Args:
        patterns: List of MissingPattern objects
        output_file: Output file path for visualization
        num_samples: Number of patterns to visualize
    """
    if not MATPLOTLIB_AVAILABLE:
        logger.error("Matplotlib is not installed. Please install it to use visualization: pip install matplotlib")
        return
    
    if not patterns:
        logger.warning("No patterns to visualize")
        return
    
    # Select representative patterns across different missing ratios
    patterns_sorted = sorted(patterns, key=lambda p: p.missing_ratio)
    
    # Sample patterns evenly across the missing ratio range
    if len(patterns_sorted) > num_samples:
        indices = np.linspace(0, len(patterns_sorted) - 1, num_samples, dtype=int)
        selected_patterns = [patterns_sorted[i] for i in indices]
    else:
        selected_patterns = patterns_sorted
    
    # Calculate grid size
    num_patterns = len(selected_patterns)
    cols = min(4, num_patterns)
    rows = (num_patterns + cols - 1) // cols
    
    # Create figure
    fig, axes = plt.subplots(rows, cols, figsize=(cols * 3, rows * 3.5))
    if rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1:
        axes = [axes]
    else:
        axes = axes.flatten()
    
    fig.suptitle(f"Missing Patterns Visualization ({num_patterns} samples)", fontsize=16)
    
    # Plot patterns
    for i, pattern in enumerate(selected_patterns):
        ax = axes[i] if i < len(axes) else None
        if ax is None:
            break
            
        # Display pattern (missing=white, data=black)
        ax.imshow(pattern.pattern, cmap='gray', interpolation='nearest')
        
        # Add title with information
        title = f"Ratio: {pattern.missing_ratio:.3f}\n"
        if pattern.metadata:
            lat = pattern.metadata.get('tile_lat', 0)
            lon = pattern.metadata.get('tile_lon', 0)
            year = pattern.metadata.get('year', 0)
            title += f"Lat: {lat:.1f}, Lon: {lon:.1f}\nYear: {year}"
        
        ax.set_title(title, fontsize=10)
        ax.set_xticks([])
        ax.set_yticks([])
    
    # Hide unused subplots
    for i in range(num_patterns, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    logger.info(f"📈 Visualization saved to: {output_file}")


def export_missing_patterns(index_file: Path, 
                          output_file: Path,
                          min_missing_ratio: float = 0.05,
                          max_workers: int = 8,
                          visualize: bool = False) -> None:
    """
    Export all missing patterns from index file to npz database
    
    Args:
        index_file: Path to index file from build_index.py
        output_file: Path for output npz file
        min_missing_ratio: Minimum missing ratio threshold
        max_workers: Number of parallel workers for extraction
        visualize: Whether to create visualization
    """
    logger.info("=" * 80)
    logger.info("Exporting Missing Patterns to NPZ Database")
    logger.info("=" * 80)
    
    # Load index data
    index_data = load_index_file(index_file)
    
    # Collect all missing samples
    all_samples = []
    for bin_idx_str, samples in index_data['missing_samples_by_ratio'].items():
        all_samples.extend(samples)
    
    logger.info(f"Total missing samples to process: {len(all_samples)}")
    logger.info(f"Minimum missing ratio threshold: {min_missing_ratio}")
    
    # Group samples by file for efficient processing
    file_to_samples = defaultdict(list)
    for sample in all_samples:
        file_path = sample.get('file_path') or sample.get('source_file')
        if file_path:
            file_to_samples[file_path].append(sample)
    
    logger.info(f"Processing {len(file_to_samples)} unique files")
    
    # Process files in parallel
    all_patterns = []
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit tasks
        futures = []
        for file_path, samples in file_to_samples.items():
            future = executor.submit(process_file_samples, (file_path, samples, min_missing_ratio))
            futures.append(future)
        
        # Collect results
        for future in tqdm(as_completed(futures), total=len(futures), desc="Extracting patterns"):
            try:
                patterns = future.result()
                all_patterns.extend(patterns)
            except Exception as e:
                logger.error(f"Error processing file: {e}")
    
    logger.info(f"Successfully extracted {len(all_patterns)} missing patterns")
    
    if not all_patterns:
        logger.warning("No patterns extracted! Check minimum missing ratio threshold.")
        return
    
    # Convert patterns to format compatible with dataset.py
    simplified_patterns = []
    for pattern in all_patterns:
        pattern_dict = {
            'pattern': pattern.pattern.astype(np.uint8),  # Save as uint8 to save space
            'missing_ratio': pattern.missing_ratio
        }
        simplified_patterns.append(pattern_dict)
    
    # Save to NPZ file in a format compatible with dataset.py
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Create config dictionary
    config = {
        'min_missing_ratio': min_missing_ratio,
        'total_patterns': len(simplified_patterns),
        'extraction_method': 'direct_export'
    }
    
    # Save in a format dataset.py can directly load
    # Store patterns as a list of dictionaries for easier loading
    np.savez_compressed(
        output_file, 
        patterns=simplified_patterns,  # Store as list directly
        config=config  # Store config as dict directly
    )
    
    logger.info(f"Saved {len(simplified_patterns)} patterns to {output_file}")
    
    # Generate visualization if requested
    if visualize:
        try:
            viz_file = output_file.with_suffix('.png')
            logger.info(f"Generating pattern visualization: {viz_file}")
            visualize_patterns(all_patterns, str(viz_file), num_samples=16)
        except Exception as e:
            logger.error(f"Failed to generate visualization: {e}")
    
    # Print statistics
    missing_ratios = [p.missing_ratio for p in all_patterns]
    logger.info("=" * 80)
    logger.info("Export Completed Successfully!")
    logger.info("=" * 80)
    logger.info(f"📊 Export Statistics:")
    logger.info(f"   • Total patterns exported: {len(all_patterns):,}")
    logger.info(f"   • Missing ratio range: [{np.min(missing_ratios):.3f}, {np.max(missing_ratios):.3f}]")
    logger.info(f"   • Missing ratio mean: {np.mean(missing_ratios):.3f} ± {np.std(missing_ratios):.3f}")
    logger.info(f"   • Pattern types: {set(p.pattern_type for p in all_patterns)}")
    logger.info(f"📁 Output file: {output_file}")
    logger.info("✅ Missing pattern export completed!")


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(
        description="Export all missing patterns from build_index.py output to NPZ database",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Required arguments
    parser.add_argument("index_file", 
                       help="Path to index JSON file from build_index.py")
    parser.add_argument("output_file", 
                       help="Output path for missing pattern NPZ database")
    
    # Optional arguments
    parser.add_argument("--min-ratio", type=float, default=0.05, 
                       help="Minimum missing ratio threshold")
    parser.add_argument("--workers", "-w", type=int, default=8, 
                       help="Number of parallel workers for pattern extraction")
    
    # Visualization
    parser.add_argument("--visualize", action="store_true",
                       help="Generate pattern visualization PNG")
    
    # Logging
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Convert paths
    index_file = Path(args.index_file)
    output_file = Path(args.output_file)
    
    # Validate input
    if not index_file.exists():
        logger.error(f"Input index file does not exist: {index_file}")
        return 1
    
    # Create output directory
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # Export missing patterns
        export_missing_patterns(
            index_file=index_file,
            output_file=output_file,
            min_missing_ratio=args.min_ratio,
            max_workers=args.workers,
            visualize=args.visualize
        )
        
        logger.info("🎉 Export process completed successfully!")
        return 0
        
    except Exception as e:
        logger.error(f"❌ Export failed: {e}")
        logger.exception("Full traceback:")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 