import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns

# 读取JSON文件
file_path = '/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/train_indices.json'

with open(file_path, 'r') as f:
    data = json.load(f)

# 转换为DataFrame
df = pd.DataFrame(data)

# 创建分析函数
def analyze_distribution(df, column_name, bins, labels=None):
    """分析指定列的分布"""
    print(f"\n{'='*50}")
    print(f"{column_name} 分布分析")
    print(f"{'='*50}")
    
    # 基本统计信息
    print(f"\n基本统计信息:")
    print(f"总数: {len(df)}")
    print(f"最小值: {df[column_name].min():.4f}")
    print(f"最大值: {df[column_name].max():.4f}")
    print(f"平均值: {df[column_name].mean():.4f}")
    print(f"标准差: {df[column_name].std():.4f}")
    
    # 分区间统计
    if bins is not None:
        df['bin'] = pd.cut(df[column_name], bins=bins, labels=labels, include_lowest=True)
        bin_counts = df['bin'].value_counts().sort_index()
        
        print(f"\n区间分布:")
        for interval, count in bin_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{interval}: {count} ({percentage:.2f}%)")
        
        # 绘制柱状图
        plt.figure(figsize=(10, 6))
        bin_counts.plot(kind='bar')
        plt.title(f'{column_name} Distribution')
        plt.xlabel('Intervals')
        plt.ylabel('Count')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'{column_name}_distribution.png')
        plt.close()
        
        # 清理临时列
        df.drop('bin', axis=1, inplace=True)

# 1. Water Proportion 分析
water_bins = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
water_labels = ['0-0.2', '0.2-0.4', '0.4-0.6', '0.6-0.8', '0.8-1.0']

# 特殊处理：统计精确等于0和1的数量
water_zero = (df['water_proportion'] == 0).sum()
water_one = (df['water_proportion'] == 1.0).sum()
water_between = ((df['water_proportion'] > 0) & (df['water_proportion'] < 1.0)).sum()

print("\n" + "="*50)
print("Water Proportion 特殊值统计")
print("="*50)
print(f"等于0的数量: {water_zero} ({water_zero/len(df)*100:.2f}%)")
print(f"等于1的数量: {water_one} ({water_one/len(df)*100:.2f}%)")
print(f"介于0和1之间的数量: {water_between} ({water_between/len(df)*100:.2f}%)")

# 对介于0和1之间的值进行区间分析
df_between = df[(df['water_proportion'] > 0) & (df['water_proportion'] < 1.0)]
if len(df_between) > 0:
    analyze_distribution(df_between, 'water_proportion', water_bins, water_labels)

# 2. Missing Proportion 分析
missing_bins = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
missing_labels = ['0-0.2', '0.2-0.4', '0.4-0.6', '0.6-0.8', '0.8-1.0']

# 特殊处理：统计精确等于0的数量
missing_zero = (df['missing_proportion'] == 0).sum()
print(f"\n等于0的missing_proportion数量: {missing_zero} ({missing_zero/len(df)*100:.2f}%)")

analyze_distribution(df, 'missing_proportion', missing_bins, missing_labels)

# 3. Year 分析
print("\n" + "="*50)
print("Year 分布分析")
print("="*50)
year_counts = df['year'].value_counts().sort_index()
print("\n各年份数据量:")
for year, count in year_counts.items():
    print(f"{year}: {count} ({count/len(df)*100:.2f}%)")

# 绘制年份分布图
plt.figure(figsize=(12, 6))
year_counts.plot(kind='bar')
plt.title('Year Distribution')
plt.xlabel('Year')
plt.ylabel('Count')
plt.tight_layout()
plt.savefig('year_distribution.png')
plt.close()

# 4. Month 分析
print("\n" + "="*50)
print("Month 分布分析")
print("="*50)
month_counts = df['month'].value_counts().sort_index()
print("\n各月份数据量:")
for month, count in month_counts.items():
    print(f"{month}: {count} ({count/len(df)*100:.2f}%)")

# 绘制月份分布图
plt.figure(figsize=(10, 6))
month_counts.plot(kind='bar')
plt.title('Month Distribution')
plt.xlabel('Month')
plt.ylabel('Count')
plt.tight_layout()
plt.savefig('month_distribution.png')
plt.close()

# 5. Tile_lon 分析
lon_bins = np.arange(-180, 181, 30)  # 每30度一个区间
lon_labels = [f"{lon_bins[i]} to {lon_bins[i+1]}" for i in range(len(lon_bins)-1)]
analyze_distribution(df, 'tile_lon', lon_bins, lon_labels)

# 6. Tile_lat 分析
lat_bins = np.arange(-90, 91, 15)  # 每15度一个区间
lat_labels = [f"{lat_bins[i]} to {lat_bins[i+1]}" for i in range(len(lat_bins)-1)]
analyze_distribution(df, 'tile_lat', lat_bins, lat_labels)

# 7. 创建综合报告
print("\n" + "="*50)
print("综合统计报告")
print("="*50)
print(f"总样本数: {len(df)}")
print(f"唯一文件数: {df['file_path'].nunique()}")
print(f"时间范围: {df['year'].min()}-{df['year'].max()}")
print(f"经度范围: [{df['tile_lon'].min():.2f}, {df['tile_lon'].max():.2f}]")
print(f"纬度范围: [{df['tile_lat'].min():.2f}, {df['tile_lat'].max():.2f}]")

# 8. 创建相关性分析
print("\n相关性分析:")
correlation_cols = ['water_proportion', 'missing_proportion', 'mean_water_frequency', 'tile_lon', 'tile_lat']
correlation_matrix = df[correlation_cols].corr()

plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
            square=True, linewidths=1, cbar_kws={"shrink": 0.8})
plt.title('Correlation Matrix')
plt.tight_layout()
plt.savefig('correlation_matrix.png')
plt.close()

print("\n分析完成！图表已保存到当前目录。")