"""
Unified Dataset Loading for Water Body Spatiotemporal Modeling

This module implements:
1. Unified data loading for training and inference
2. Integration with balanced sampling index
3. Missing pattern database integration for data augmentation
4. 120-frame temporal window support (60 before + 60 after)
5. Multi-modal feature fusion (water masks, occurrence, geo/temporal features)
6. Efficient data loading with proper padding and normalization
"""

import os
import numpy as np
import pandas as pd
import xarray as xr
import torch
from torch.utils.data import Dataset, DataLoader, Sampler  # extend import
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
import json
import random
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict
import warnings
import hashlib
from scipy.ndimage import distance_transform_edt  # NEW: for SDF computation
from model.loss import compute_dynamic_degree  # NEW: for dynamic degree computation

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)

# JRC data value definitions
JRC_VALUES = {
    'no_data': 255,
    'land': 1,
    'water': 2,
    'no_observation': 0
}

# Model input value definitions
MODEL_VALUES = {
    'missing': -1,  # Missing data
    'land': 0,      # Land
    'water': 1      # Water
}


class DataProcessor:
    """Handles data preprocessing and normalization"""

    def __init__(self):
        """Initialize data processor"""
        pass

    def compute_sdf(self, water_mask: np.ndarray) -> np.ndarray:
        """
        Compute Signed Distance Transform (SDF) for water mask

        Args:
            water_mask: Binary water mask (1=water, 0=land)

        Returns:
            sdf: Signed distance transform where:
                 - Positive values inside water (distance to nearest land)
                 - Negative values outside water (distance to nearest water)
                 - Zero at boundaries
        """
        # Ensure binary mask
        water_binary = (water_mask > 0.5).astype(np.uint8)
        land_binary = 1 - water_binary

        # Compute distance transforms
        # Distance from water pixels to nearest land pixel (positive inside water)
        water_to_land_dist = distance_transform_edt(water_binary)

        # Distance from land pixels to nearest water pixel (negative outside water)
        land_to_water_dist = distance_transform_edt(land_binary)

        # Combine into signed distance field
        sdf = np.where(water_binary, water_to_land_dist, -land_to_water_dist)

        return sdf.astype(np.float32)

    def preprocess_jrc_data(self, data: np.ndarray) -> np.ndarray:
        """
        Preprocess JRC water data to model format

        Args:
            data: Raw JRC data

        Returns:
            processed_data: Processed data
        """
        # Create output array
        processed = np.full_like(data, MODEL_VALUES['missing'], dtype=np.float32)

        # Map JRC values to model values
        processed[data == JRC_VALUES['water']] = MODEL_VALUES['water']
        processed[data == JRC_VALUES['land']] = MODEL_VALUES['land']

        # Create missing mask (True = missing)
        missing_mask = (data == JRC_VALUES['no_data']) | (data == JRC_VALUES['no_observation'])
        processed[missing_mask] = MODEL_VALUES['missing']

        return processed

    def preprocess_jrc_data_with_mask(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess JRC water data to model format and return missing mask

        Args:
            data: Raw JRC data

        Returns:
            processed_data: Processed data
            missing_mask: Boolean mask where True indicates missing data
        """
        # Create output array
        processed = np.full_like(data, MODEL_VALUES['missing'], dtype=np.float32)

        # Map JRC values to model values
        processed[data == JRC_VALUES['water']] = MODEL_VALUES['water']
        processed[data == JRC_VALUES['land']] = MODEL_VALUES['land']

        # Create missing mask (True = missing)
        missing_mask = (data == JRC_VALUES['no_data']) | (data == JRC_VALUES['no_observation'])
        processed[missing_mask] = MODEL_VALUES['missing']

        return processed, missing_mask

    def normalize_occurrence_data(self, occ_data: np.ndarray) -> np.ndarray:
        """Normalize occurrence data to [0, 1] range"""
        # Handle no-data values
        valid_mask = occ_data != JRC_VALUES['no_data']
        normalized = np.zeros_like(occ_data, dtype=np.float32)
        normalized[valid_mask] = occ_data[valid_mask] / 100.0
        normalized[~valid_mask] = MODEL_VALUES['missing']  # Fill invalid with missing

        return normalized


class MissingPatternDatabase:
    """Missing pattern database"""

    def __init__(self, missing_db_file: Path, expected_missing_ratio: float):
        self.missing_db_file = Path(missing_db_file)
        self.missing_db = self._load_missing_db(expected_missing_ratio)

    def _load_missing_db(self, expected_missing_ratio):
        """Load missing pattern database"""
        if not self.missing_db_file or not self.missing_db_file.exists():
            logger.warning("Missing pattern database not found")
            return None

        data = np.load(self.missing_db_file, allow_pickle=True)
        self.patterns = data['patterns']
        self.config = data['config'].item()

        # 正确提取missing_ratio值并计算最小值，并过滤
        filter_patterns = [pattern for pattern in self.patterns if pattern['missing_ratio'] <= expected_missing_ratio]
        self.patterns = filter_patterns
        missing_ratios = [pattern['missing_ratio'] for pattern in self.patterns]
        min_missing_ratio = np.min(missing_ratios)
        max_missing_ratio = np.max(missing_ratios)
        logger.info(f"Missing ratio range: min={min_missing_ratio:.4f}, max={max_missing_ratio:.4f}")
        logger.info(f"Loaded missing pattern database with {self.config['total_patterns']} patterns")
        return self.patterns

    def get_random_pattern(self, mode=None, file_path=None):
        """Get a random pattern with a specific missing ratio"""
        if mode == 'val' and file_path is not None:
            state = random.getstate()
            hash_object = hashlib.md5(file_path.encode())
            hash_value = int(hash_object.hexdigest(), 16)
            random.seed(42+hash_value)
            random_pattern = random.choice(self.patterns)
            random.setstate(state)
        elif mode == 'evaluation' and file_path is not None:
            basename = os.path.basename(file_path)
            state = random.getstate()
            hash_object = hashlib.md5(basename.encode())
            hash_value = int(hash_object.hexdigest(), 16)
            random.seed(42+hash_value)
            random_pattern = random.choice(self.patterns)
            random.setstate(state)
        else:
            random_pattern = random.choice(self.patterns)
        return random_pattern['pattern'], random_pattern['missing_ratio']

class WaterBodyDataset(Dataset):
    """Unified dataset for water body spatiotemporal modeling"""

    def __init__(self,
                 index_file: Union[str, Path],
                 missing_db_file: Optional[Union[str, Path]] = None,
                 expected_missing_ratio: float = 1.0,
                 config: Optional[Dict] = None,
                 mode: str = 'train',
                 device: Optional[str] = None,
                 use_missing_augmentation: bool = True,
                 variation_rate_min: Optional[float] = None,
                 variation_rate_max: Optional[float] = None):
        """
        Initialize dataset

        Args:
            index_file: Path to balanced dataset index
            missing_db_file: Path to missing pattern database
            config: Configuration dictionary
            mode: Dataset mode ('train', 'val', 'test')
            use_missing_augmentation: Whether to use missing data augmentation
        """
        self.index_file = Path(index_file)
        self.missing_db_file = Path(missing_db_file) if missing_db_file else None
        self.mode = mode
        self.use_missing_augmentation = use_missing_augmentation
        self.expected_missing_ratio = expected_missing_ratio
        # New: variation rate thresholds
        self.variation_rate_min = variation_rate_min
        self.variation_rate_max = variation_rate_max
        # Default configuration
        default_config = {
            'data': {
                'sequence_length': 120,
                'tile_size': 256,
                'augment': False,
                # Optional: allow thresholds via config as fallback
                'variation_rate_min': None,
                'variation_rate_max': None,
            },
            'model': {
                'tasks': ['inpaint', 'predict', 'change']
            }
        }

        self.config = config or default_config
        # Fill missing threshold args from config if not provided
        data_cfg = self.config.get('data', {})
        if self.variation_rate_min is None:
            self.variation_rate_min = data_cfg.get('variation_rate_min', None)
        if self.variation_rate_max is None:
            self.variation_rate_max = data_cfg.get('variation_rate_max', None)
        
        logger.info(f"Variation rate thresholds: min={self.variation_rate_min}, max={self.variation_rate_max}")
        
        self.sequence_length = self.config.get('data', {}).get('sequence_length', 120)
        self.tile_size = self.config.get('data', {}).get('tile_size', 256)
        self.augment = False

        # Initialize components
        self.data_processor = DataProcessor()

        # Set device before loading index
        self.device = device

        # Load index
        self._load_index()

        # Load missing pattern database
        self.missing_db = self._load_missing_db()

        # Only log on main process to avoid duplicate messages in distributed training
        if not os.environ.get('LOCAL_RANK', '0') or os.environ.get('LOCAL_RANK', '0') == '0':
            logger.info(f"Initialized WaterBodyDataset in {mode} mode with {len(self.samples)} samples")
            logger.info(f"Missing augmentation enabled: {self.use_missing_augmentation}")

    def _load_index(self):
        """Load balanced dataset index"""
        if not self.index_file.exists():
            raise FileNotFoundError(f"Index file not found: {self.index_file}")

        with open(self.index_file, 'r') as f:
            index_data = json.load(f)

        # Use no-missing samples as our primary data source
        # These are water body images with no missing data (ground truth)
        self.no_missing_samples = index_data.get('no_missing_samples', {})

        if not self.no_missing_samples:
            raise ValueError(f"No no-missing samples found in index file: {self.index_file}")

        # Flatten samples from all files
        self.samples = []
        for file_path, file_samples in self.no_missing_samples.items():
            if self.device == 'a100':
                file_path = file_path.replace('/fossfs/xiaozhen/Clip/JRC4/', '/mnt/storage/xiaozhen/Water/Clip/JRC4/')
                if not Path(file_path).exists():
                    logger.warning(f"File not found: {file_path}")
                    continue

            elif self.device == 'L40':
                file_path = file_path.replace('/fossfs/xiaozhen/Clip/JRC4/', '/lustre1/g/geog_geors/xiaozhen/Water/JRC4/')
                if not Path(file_path).exists():
                    logger.warning(f"File not found: {file_path}")
                    continue

            for sample in file_samples:
                # Mark as no-missing for reference
                sample['is_no_missing'] = True
                # Ensure file_path key exists (compatibility with IndexBuilder output)
                if 'file_path' not in sample:
                    # In newer IndexBuilder versions, samples may use 'source_file'
                    sample['file_path'] = file_path

                # Variation rate threshold filtering (if configured and field exists)
                vr = sample.get('variation_rate', None)
                if (self.variation_rate_min is not None or self.variation_rate_max is not None) and vr is not None:
                    try:
                        vr_f = float(vr)
                    except Exception:
                        vr_f = None
                    if vr_f is None:
                        pass  # keep sample; downstream can compute if needed
                    else:
                        if self.variation_rate_min is not None and vr_f < float(self.variation_rate_min):
                            continue
                        if self.variation_rate_max is not None and vr_f > float(self.variation_rate_max):
                            continue

                # ------------------------------------------------------------------
                # Defensive type conversion ------------------------------------------------
                # Some index builders may accidentally export numeric indices as strings.
                # When these values are used for NumPy / xarray fancy indexing later on,
                # a string dtype (e.g. '<U1') will trigger the runtime error we observed:
                #   "invalid indexer array, does not have integer dtype".
                # To harden the pipeline we convert them to Python ints here.
                # ------------------------------------------------------------------
                for key in ('idx_x', 'idx_y', 'time_idx'):
                    if key in sample and not isinstance(sample[key], (int, np.integer)):
                        try:
                            sample[key] = int(sample[key])
                        except Exception as type_err:
                            logger.warning(
                                f"Failed to cast {key}='{sample[key]}' to int for sample from {file_path}: {type_err}")
                self.samples.append(sample)

        # Split by mode
        total_samples = len(self.samples)
        state = random.getstate()
        random.seed(42)
        random.shuffle(self.samples)
        random.setstate(state)

        if self.mode == 'train':
            self.samples = self.samples[:int(0.8 * total_samples)]
        elif self.mode == 'val':
            self.samples = self.samples[int(0.8 * total_samples): min(int(0.8 * total_samples)+9827, int(0.9 * total_samples))]
        elif self.mode == 'debug':
            self.samples = self.samples[:500]
        elif self.mode == 'test':  # test
            self.samples = self.samples[int(0.9 * total_samples):]
        elif self.mode == 'evaluation':
            self.samples = self.samples

    # NEW: Helper to fetch mean_water_frequency for a sample
    def _fetch_mean_water_frequency(self, sample: Dict[str, Any]) -> float:
        """Read mean_water_frequency for given sample location"""
        file_path = sample['file_path']
        idx_x = int(sample['idx_x'])
        idx_y = int(sample['idx_y'])
        ds = None
        try:
            ds = xr.open_dataset(file_path, engine='netcdf4', cache=False)
            mean_freq = float(ds.mean_water_frequency[idx_x, idx_y].values)
        except Exception as e:
            logger.warning(f"Error reading mean_water_frequency from {file_path}: {e}")
            mean_freq = 0.5  # Neutral value
        finally:
            if ds is not None:
                try:
                    ds.close()
                except Exception:
                    pass
        return mean_freq

    def _load_missing_db(self):
        """Load missing pattern database"""
        if not self.missing_db_file or not self.missing_db_file.exists() or not self.use_missing_augmentation:
            logger.warning("Missing pattern database not used or not found")
            return None

        try:
            missing_db = MissingPatternDatabase(self.missing_db_file, self.expected_missing_ratio)
            return missing_db
        except Exception as e:
            logger.error(f"Failed to load missing pattern database: {e}")
            return None

    def __len__(self) -> int:
        """Return dataset size"""
        return len(self.samples)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample

        Args:
            idx: Sample index

        Returns:
            Dictionary containing model inputs
        """
        sample_info = self.samples[idx]

        try:
            # Load data from NetCDF file
            sample_data = self._load_sample_data(sample_info)

            # Process and augment data
            processed_sample = self._process_sample(sample_data)

            return processed_sample

        except Exception as e:
            # Include detailed context for debugging corrupted NetCDF/HDF files
            fp = sample_info.get('file_path') if isinstance(sample_info, dict) else 'N/A'
            ix = sample_info.get('idx_x') if isinstance(sample_info, dict) else 'N/A'
            iy = sample_info.get('idx_y') if isinstance(sample_info, dict) else 'N/A'
            ti = sample_info.get('time_idx') if isinstance(sample_info, dict) else 'N/A'
            logger.error(
                f"[PID {os.getpid()}] Error loading sample {idx}: file={fp}, idx_x={ix}, idx_y={iy}, time_idx={ti}. Error: {e}",
                exc_info=True
            )
            # Return a dummy sample to avoid breaking the batch
            return self._create_dummy_sample()

    def _load_sample_data(self, sample_info: Dict[str, Any]) -> Dict[str, Any]:
        """Load data for a single sample from NetCDF file"""
        file_path = sample_info['file_path']

        # Apply path replacement for a100 device
        if self.device == 'a100':
            file_path = file_path.replace('/fossfs/xiaozhen/Clip/JRC4/', '/mnt/storage/xiaozhen/Water/Clip/JRC4/')

        elif self.device == 'L40':
            file_path = file_path.replace('/fossfs/xiaozhen/Clip/JRC4/', '/lustre1/g/geog_geors/xiaozhen/Water/JRC4/')

        # Ensure indices are proper integers – cast defensively to avoid string dtypes
        idx_x = int(sample_info['idx_x'])
        idx_y = int(sample_info['idx_y'])
        center_time_idx = int(sample_info['time_idx'])

        # Optional pre-open debug logging to identify problematic files causing segfaults
        if os.environ.get('DATASET_IO_DEBUG') == '1':
            try:
                logger.warning(
                    f"[PID {os.getpid()}] About to open NetCDF: file={file_path}, idx_x={idx_x}, idx_y={idx_y}, time_idx={center_time_idx}"
                )
            except Exception:
                pass

        # Use explicit file handle management to avoid HDF5 conflicts in multiprocessing
        ds = None
        try:
            # Open dataset with explicit engine and caching disabled for multiprocessing safety
            ds = xr.open_dataset(file_path, chunks={'time': 10}, engine='netcdf4', cache=False)

            # Get time dimension size
            time_size = ds.dims['time']

            # Calculate temporal window bounds centered on the sample
            half_seq = self.sequence_length // 2
            start_time = max(0, center_time_idx - half_seq)
            end_time = min(time_size, center_time_idx + half_seq)

            # Ensure we get exactly sequence_length frames
            if end_time - start_time < self.sequence_length:
                if start_time == 0:
                    end_time = min(time_size, self.sequence_length)
                else:
                    start_time = max(0, end_time - self.sequence_length)

            # Load temporal sequence data
            data = ds.data[idx_x, idx_y, start_time:end_time].values
            occ_data = ds.occ_data[idx_x, idx_y].values

            # Get time coordinates
            time_coords = pd.to_datetime(ds.time[start_time:end_time].values)

            # Get static features
            mean_freq = float(ds.mean_water_frequency[idx_x, idx_y].values)
            tile_lon = float(ds.tile_lon[idx_x, idx_y].values)
            tile_lat = float(ds.tile_lat[idx_x, idx_y].values)

            # Calculate the center frame index in the loaded sequence
            center_frame_idx = int(center_time_idx - start_time)

        finally:
            # Explicitly close the dataset to prevent HDF5 object leaks
            if ds is not None:
                try:
                    ds.close()
                except Exception:
                    pass  # Ignore close errors to prevent breaking the training loop

        return {
            'data': data,
            'occ_data': occ_data,
            'time_coords': time_coords,
            'mean_water_frequency': mean_freq,
            'tile_lon': tile_lon,
            'tile_lat': tile_lat,
            'center_frame_idx': center_frame_idx,
            'file_path': file_path
        }

    def __del__(self):
        """Cleanup method to ensure proper resource cleanup"""
        try:
            # Force garbage collection to help clean up any remaining file handles
            import gc
            gc.collect()
        except Exception:
            pass

    def _process_sample(self, sample_data: Dict[str, Any]):
        """Process raw sample data into model inputs"""
        # Extract data
        raw_data = sample_data['data']  # (T, H, W)
        occ_data = sample_data['occ_data']  # (H, W)
        time_coords = sample_data['time_coords']
        center_frame_idx = sample_data['center_frame_idx']
        file_path = sample_data['file_path']

        # Preprocess JRC data (no-missing samples should have no missing data)
        processed_data = self.data_processor.preprocess_jrc_data(raw_data)

        # ------------------------------------------------------------------
        # Build three-channel representation
        #   ch0: water(1) vs land(0)  (missing treated as 0)
        #   ch1: missing mask        (missing=1, otherwise 0)
        #   ch2: signed distance transform (SDF)
        # ------------------------------------------------------------------
        water_channel = (processed_data == MODEL_VALUES['water']).astype(np.float32)
        missing_channel_full = (processed_data == MODEL_VALUES['missing'])
        missing_channel = missing_channel_full.astype(np.float32)

        # Compute SDF for each frame
        T, H, W = processed_data.shape
        sdf_channel = np.zeros((T, H, W), dtype=np.float32)

        for t in range(T):
            # Only compute SDF for frames with valid data (not all missing)
            frame_water = water_channel[t]
            frame_missing = missing_channel[t]

            # Skip frames that are entirely missing
            if np.sum(1 - frame_missing) > 0:  # Has some valid pixels
                # Create mask for valid pixels only
                valid_mask = (1 - frame_missing).astype(bool)

                if np.sum(frame_water[valid_mask]) > 0 and np.sum(1 - frame_water[valid_mask]) > 0:
                    # Has both water and land pixels, compute SDF
                    sdf_channel[t] = self.data_processor.compute_sdf(frame_water)
                    # Set SDF to 0 for missing pixels
                    sdf_channel[t][~valid_mask] = 0.0

        three_channel_data = np.stack([water_channel, missing_channel, sdf_channel], axis=1)  # (T, 3, H, W)

        # Generate ground-truth of water/land for center frame (single channel)
        if 0 <= center_frame_idx < processed_data.shape[0]:
            ground_truth_raw = processed_data[center_frame_idx].copy()
        else:
            mid_idx = processed_data.shape[0] // 2
            ground_truth_raw = processed_data[mid_idx].copy()

        ground_truth = (ground_truth_raw == MODEL_VALUES['water']).astype(np.float32)  # (H,W)

        # The input sequence is a copy that will be augmented
        augmented_data = three_channel_data.copy()  # work on 3-channel

        # Prepare full-sequence missing mask (True = missing) for loss/metrics
        missing_mask_center = missing_channel_full[center_frame_idx]  # boolean (H,W)

        # This will also hold artificial missing pixels injected later (only center frame)
        artificial_mask_center = np.zeros_like(ground_truth_raw, dtype=bool)

        # Apply pattern to the center frame
        if 0 <= center_frame_idx < processed_data.shape[0] and self.missing_db is not None:
            # Get a random missing pattern
            missing_pattern, _ = self.missing_db.get_random_pattern(self.mode, file_path)

            if missing_pattern is not None:
                # Insert artificial missing pixels into center frame mask
                artificial_mask_center = missing_pattern.astype(bool)

                # Apply mask to water/land channel (set to 0), mark missing channel =1, and set SDF to 0
                augmented_data[center_frame_idx, 0][artificial_mask_center] = 0.0  # water channel
                augmented_data[center_frame_idx, 1][artificial_mask_center] = 1.0  # missing channel
                augmented_data[center_frame_idx, 2][artificial_mask_center] = 0.0  # SDF channel

                # Update center-frame missing mask
                missing_mask_center |= artificial_mask_center

        # Process occurrence data
        normalized_occ = self.data_processor.normalize_occurrence_data(occ_data)

        # Extract geographic and temporal information for new model
        tile_lon = sample_data['tile_lon']
        tile_lat = sample_data['tile_lat']

        # Extract year and month from center frame timestamp
        center_timestamp = time_coords[center_frame_idx] if 0 <= center_frame_idx < len(time_coords) else time_coords[len(time_coords)//2]
        year = center_timestamp.year
        month = center_timestamp.month

        # Ensure proper sequence length with padding if needed
        T = augmented_data.shape[0]
        if T < self.sequence_length:
            # Pad sequence (water channel 0, missing channel 1, SDF channel 0) with appropriate values
            pad_size = self.sequence_length - T
            pad_shape = (pad_size,) + augmented_data.shape[1:]
            pad_block = np.zeros(pad_shape, dtype=augmented_data.dtype)
            # For missing channel, set to 1 (indicating missing data in padded frames)
            pad_block[:, 1, :, :] = 1.0  # missing channel = 1 for padded frames
            augmented_data = np.concatenate([
                augmented_data,
                pad_block
            ], axis=0)
            # NOTE: missing_mask_center 与 temporal_features 为中心帧 / 静态信息，无需填充

        # Convert to tensors - 修改ground_truth为单帧
        return {
            # Core sequence data
            'input_sequence': torch.from_numpy(augmented_data).float(),
            'missing_mask': torch.from_numpy(missing_mask_center).bool(),
            'ground_truth': torch.from_numpy(ground_truth).float(),  # (H,W) binary water mask
            'occurrence': torch.from_numpy(normalized_occ).float(),
            'center_frame_idx': torch.tensor(center_frame_idx).long(),

            # Geographic and temporal context (new format for enhanced model)
            'tile_lon': torch.tensor(tile_lon).float(),
            'tile_lat': torch.tensor(tile_lat).float(),
            'year': torch.tensor(year).long(),
            'month': torch.tensor(month).long(),

            # # NEW: dynamic degree for curriculum learning
            # 'dynamic_degree': torch.tensor(sample_info.get('dynamic_degree', float(compute_dynamic_degree(torch.tensor(sample_data['mean_water_frequency'])).item()))).float(),
        }

    def _create_dummy_sample(self) -> Dict[str, torch.Tensor]:
        """Create a dummy sample for error recovery"""
        T, H, W = self.sequence_length, self.tile_size, self.tile_size

        return {
            # Core sequence data (dummy with three channels)
            'input_sequence': torch.zeros(T, 3, H, W).float(),
            'missing_mask': torch.zeros(H, W).bool(),
            'ground_truth': torch.zeros(H, W).float(),
            'occurrence': torch.zeros(H, W).float(),
            'center_frame_idx': torch.tensor(-1).long(),

            # Geographic and temporal context (dummy values)
            'tile_lon': torch.tensor(0.0).float(),
            'tile_lat': torch.tensor(0.0).float(),
            'year': torch.tensor(2020).long(),
            'month': torch.tensor(6).long(),
            }

# --------------------------- Curriculum Sampler ---------------------------
class CurriculumSampler(Sampler):
    """Sampler that gradually exposes samples with higher dynamic_degree as epochs progress."""
    def __init__(self,
                 dataset: 'WaterBodyDataset',
                 start_fraction: float = 0.3,
                 end_fraction: float = 1.0,
                 total_epochs: int = 20,
                 shuffle_within: bool = True):
        if not hasattr(dataset, 'samples'):
            raise ValueError("Dataset must have 'samples' attribute with dynamic_degree.")
        self.dataset = dataset
        self.start_fraction = max(0.0, min(start_fraction, 1.0))
        self.end_fraction = max(self.start_fraction, min(end_fraction, 1.0))
        self.total_epochs = max(1, total_epochs)
        self.shuffle_within = shuffle_within
        # Pre-compute indices sorted by dynamic_degree (dataset is already sorted)
        self.indices_sorted = list(range(len(dataset)))
        self.epoch = 0

    def set_epoch(self, epoch: int):
        """Update current epoch (call at start of every epoch)."""
        self.epoch = epoch

    def _current_cutoff(self) -> int:
        # Linear schedule
        frac = self.start_fraction + (self.end_fraction - self.start_fraction) * min(self.epoch, self.total_epochs - 1) / (self.total_epochs - 1)
        cutoff = int(len(self.dataset) * frac)
        cutoff = max(1, min(cutoff, len(self.dataset)))
        return cutoff

    def __iter__(self):
        cutoff = self._current_cutoff()
        subset = self.indices_sorted[:cutoff]
        if self.shuffle_within:
            g = torch.Generator()
            g.manual_seed(self.epoch)
            subset = torch.randperm(cutoff, generator=g).tolist()
        return iter(subset)

    def __len__(self):
        return self._current_cutoff()


# ------------------ NEW: CurriculumDistributedSampler ------------------
class CurriculumDistributedSampler(Sampler):
    """Distributed version of CurriculumSampler that works with DDP.

    Each epoch increases the visible fraction of the dataset (easy→hard) in a
    synchronized way across all ranks, then performs the usual distributed
    slicing so that every rank sees a unique subset.
    """

    def __init__(self,
                 dataset: 'WaterBodyDataset',
                 num_replicas: int,
                 rank: int,
                 start_fraction: float = 0.3,
                 end_fraction: float = 1.0,
                 total_epochs: int = 20,
                 shuffle: bool = True,
                 seed: int = 0):
        if not hasattr(dataset, 'samples'):
            raise ValueError("Dataset must have 'samples' attribute with dynamic_degree.")

        self.dataset = dataset
        self.num_replicas = num_replicas
        self.rank = rank
        self.start_fraction = max(0.0, min(start_fraction, 1.0))
        self.end_fraction = max(self.start_fraction, min(end_fraction, 1.0))
        self.total_epochs = max(1, total_epochs)
        self.shuffle = shuffle
        self.seed = seed

        # ---------------- 内置排序逻辑 ----------------
        # 计算每个样本的动态程度并排序
        dynamic_samples = []
        for sample in dataset.samples:
            try:
                # 使用已有的 mean_water_frequency（如果存在）
                if 'mean_water_frequency' in sample and sample['mean_water_frequency'] is not None:
                    mean_freq = float(sample['mean_water_frequency'])
                else:
                    # 从数据文件中读取 mean_water_frequency
                    mean_freq = self._fetch_mean_water_frequency(sample)

                # 计算动态程度
                sample['dynamic_degree'] = float(compute_dynamic_degree(torch.tensor(mean_freq)).item())
            except Exception as e:
                logger.warning(f"Failed to compute dynamic degree for sample {sample.get('file_path', 'N/A')}: {e}")
                sample['dynamic_degree'] = 1.0  # 降级到最高难度
            dynamic_samples.append(sample)

        # 按动态程度升序排序（简单→困难）
        dynamic_samples.sort(key=lambda s: s['dynamic_degree'])

        # 创建排序后的索引列表
        self.indices_sorted = list(range(len(dynamic_samples)))
        self.epoch = 0

    def _fetch_mean_water_frequency(self, sample: Dict[str, Any]) -> float:
        """从数据文件中读取 mean_water_frequency"""
        file_path = sample['file_path']
        idx_x = int(sample['idx_x'])
        idx_y = int(sample['idx_y'])
        ds = None
        try:
            ds = xr.open_dataset(file_path, engine='netcdf4', cache=False)
            mean_freq = float(ds.mean_water_frequency[idx_x, idx_y].values)
        except Exception as e:
            logger.warning(f"Error reading mean_water_frequency from {file_path}: {e}")
            mean_freq = 0.5  # 中性值
        finally:
            if ds is not None:
                try:
                    ds.close()
                except Exception:
                    pass
        return mean_freq

    def set_epoch(self, epoch: int):
        self.epoch = epoch

    # Helper to compute current visible subset size
    def _current_cutoff(self):
        frac = self.start_fraction + (self.end_fraction - self.start_fraction) * \
               min(self.epoch, self.total_epochs - 1) / (self.total_epochs - 1)
        cutoff = int(len(self.dataset) * frac)
        return max(1, min(cutoff, len(self.dataset)))

    def __iter__(self):
        # Determine subset for this epoch
        cutoff = self._current_cutoff()
        indices = self.indices_sorted[:cutoff]

        # Optionally shuffle using shared seed so all ranks shuffle the same list
        if self.shuffle:
            g = torch.Generator()
            g.manual_seed(self.seed + self.epoch)
            indices = torch.randperm(cutoff, generator=g).tolist()

        # Pad so that it is evenly divisible across replicas
        total_size = int(np.ceil(len(indices) / self.num_replicas)) * self.num_replicas
        if len(indices) < total_size:
            indices += indices[: (total_size - len(indices))]

        # Subsample for this rank
        rank_indices = indices[self.rank:total_size:self.num_replicas]
        return iter(rank_indices)

    def __len__(self):
        # Return length of this rank's subset
        return int(np.ceil(self._current_cutoff() / self.num_replicas))


def create_dataloaders(index_file: Union[str, Path],
                      missing_db_file: Optional[Union[str, Path]] = None,
                      config: Optional[Dict] = None,
                      batch_size: int = 8,
                      num_workers: int = 8) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test data loaders

    Args:
        index_file: Path to balanced dataset index
        missing_db_file: Path to missing pattern database
        config: Configuration dictionary
        batch_size: Batch size
        num_workers: Number of workers

    Returns:
        (train_loader, val_loader, test_loader)
    """
    # Create datasets
    train_dataset = WaterBodyDataset(
        index_file=index_file,
        missing_db_file=missing_db_file,
        config=config,
        mode='train',
        use_missing_augmentation=True
    )

    val_dataset = WaterBodyDataset(
        index_file=index_file,
        missing_db_file=missing_db_file,
        config=config,
        mode='val',
        use_missing_augmentation=True  # Also apply augmentation in validation mode
    )

    test_dataset = WaterBodyDataset(
        index_file=index_file,
        missing_db_file=missing_db_file,
        config=config,
        mode='test',
        use_missing_augmentation=True  # Also apply augmentation in test mode
    )

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        num_workers=num_workers,
        sampler=CurriculumSampler(train_dataset),  # Curriculum learning sampler
        pin_memory=True,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        num_workers=num_workers,
        shuffle=False,
        pin_memory=True,
        drop_last=False
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        num_workers=num_workers,
        shuffle=False,
        pin_memory=True,
        drop_last=False
    )

    logger.info(f"Created data loaders: train={len(train_loader)}, val={len(val_loader)}, test={len(test_loader)}")

    return train_loader, val_loader, test_loader


def main():
    """Main function for testing dataset"""
    import argparse

    parser = argparse.ArgumentParser(description="Test WaterBodyDataset")
    parser.add_argument("index_file", help="Path to dataset index file")
    parser.add_argument("--missing-db", help="Path to missing pattern database")
    parser.add_argument("--batch-size", type=int, default=4, help="Batch size")

    args = parser.parse_args()

    # Create data loaders
    train_loader, val_loader, test_loader = create_dataloaders(
        args.index_file,
        args.missing_db,
        batch_size=args.batch_size,
        num_workers=0  # Use 0 for testing
    )

    # Test loading a batch
    print("Testing data loading...")
    for i, batch in enumerate(train_loader):
        print(f"Batch {i}:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape} ({value.dtype})")
            else:
                print(f"  {key}: {type(value)}")

        if i >= 2:  # Test only a few batches
            break

    print("Dataset test completed successfully!")


if __name__ == "__main__":
    main()