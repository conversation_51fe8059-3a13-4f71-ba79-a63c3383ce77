#!/usr/bin/env python3
"""
水体比例变化率分析脚本
从no_missing_samples数据中提取同一位置(file_name, idx_x, idx_y)下的water_proportion变化率
计算公式：变化率 = (最大值 - 最小值) / 平均值
生成变化率的直方图分布
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
from pathlib import Path

def load_no_missing_samples(json_file_path):
    """
    加载JSON文件中的no_missing_samples数据

    Args:
        json_file_path: JSON文件路径

    Returns:
        dict: no_missing_samples数据
    """
    try:
        with open(json_file_path, 'r') as f:
            data = json.load(f)

        no_missing_samples = data.get('no_missing_samples', {})
        if not no_missing_samples:
            raise ValueError("未找到no_missing_samples数据")

        print(f"成功加载数据，包含 {len(no_missing_samples)} 个文件")
        return no_missing_samples

    except Exception as e:
        print(f"加载JSON文件失败: {e}")
        return {}

def group_samples_by_location(no_missing_samples):
    """
    按照(file_path, idx_x, idx_y)分组样本（使用完整路径避免同名文件冲突）

    Args:
        no_missing_samples: no_missing_samples数据字典

    Returns:
        dict: 按位置分组的样本数据
    """
    location_groups = defaultdict(list)

    total_samples = 0
    for file_path, samples in no_missing_samples.items():
        for sample in samples:
            idx_x = sample['idx_x']
            idx_y = sample['idx_y']
            water_proportion = sample['water_proportion']

            # 使用(file_path, idx_x, idx_y)作为位置标识，确保唯一性
            location_key = (file_path, idx_x, idx_y)
            location_groups[location_key].append(water_proportion)
            total_samples += 1

    print(f"总样本数: {total_samples}")
    print(f"唯一位置数: {len(location_groups)}")

    return location_groups

def calculate_variation_rates(location_groups, min_samples=2):
    """
    计算每个位置的水体比例变化率

    Args:
        location_groups: 按位置分组的样本数据
        min_samples: 最小样本数阈值，少于此数量的位置将被忽略

    Returns:
        list: 变化率列表
    """
    variation_rates = []
    valid_locations = 0

    for _, water_proportions in location_groups.items():
        if len(water_proportions) < min_samples:
            continue

        water_proportions = np.array(water_proportions)

        # 计算统计量
        max_val = np.max(water_proportions)
        min_val = np.min(water_proportions)
        mean_val = np.mean(water_proportions)

        # 避免除零错误
        if mean_val > 0:
            variation_rate = (max_val - min_val) / mean_val
            variation_rates.append(variation_rate)
            valid_locations += 1

    print(f"有效位置数 (样本数>={min_samples}): {valid_locations}")
    print(f"计算出的变化率数量: {len(variation_rates)}")

    return variation_rates

def plot_variation_histogram(variation_rates, save_path='water_proportion_variation_histogram.png'):
    """
    绘制变化率直方图

    Args:
        variation_rates: 变化率列表
        save_path: 保存路径
    """
    if not variation_rates:
        print("没有变化率数据可绘制")
        return

    # 设置图形样式
    plt.style.use('default')
    _, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 转换为numpy数组便于分析
    rates = np.array(variation_rates)

    # 左图：直方图 (使用英文标签避免字体问题)
    ax1.hist(rates, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('Variation Rate (Max-Min)/Mean')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Water Proportion Variation Rate Distribution')
    ax1.grid(True, alpha=0.3)

    # 添加统计信息
    stats_text = f'Samples: {len(rates)}\n'
    stats_text += f'Mean: {np.mean(rates):.4f}\n'
    stats_text += f'Median: {np.median(rates):.4f}\n'
    stats_text += f'Std: {np.std(rates):.4f}\n'
    stats_text += f'Max: {np.max(rates):.4f}\n'
    stats_text += f'Min: {np.min(rates):.4f}'

    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 右图：箱线图
    ax2.boxplot(rates, vert=True)
    ax2.set_ylabel('Variation Rate')
    ax2.set_title('Water Proportion Variation Rate Box Plot')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

    print(f"图表已保存至: {save_path}")

def analyze_variation_statistics(variation_rates):
    """
    分析变化率的详细统计信息

    Args:
        variation_rates: 变化率列表
    """
    if not variation_rates:
        print("没有变化率数据可分析")
        return

    rates = np.array(variation_rates)

    print("\n=== 水体比例变化率统计分析 ===")
    print(f"样本数量: {len(rates)}")
    print(f"均值: {np.mean(rates):.6f}")
    print(f"中位数: {np.median(rates):.6f}")
    print(f"标准差: {np.std(rates):.6f}")
    print(f"最小值: {np.min(rates):.6f}")
    print(f"最大值: {np.max(rates):.6f}")

    # 分位数分析
    percentiles = [5, 10, 25, 50, 75, 90, 95, 99]
    print("\n分位数分析:")
    for p in percentiles:
        value = np.percentile(rates, p)
        print(f"  {p}%分位数: {value:.6f}")

    # 变化率区间分析
    print("\n变化率区间分布:")
    bins = [0, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0, float('inf')]
    bin_labels = ['0-0.1', '0.1-0.2', '0.2-0.5', '0.5-1.0', '1.0-2.0', '2.0-5.0', '>5.0']

    for i in range(len(bins)-1):
        if bins[i+1] == float('inf'):
            count = np.sum(rates >= bins[i])
        else:
            count = np.sum((rates >= bins[i]) & (rates < bins[i+1]))
        percentage = count / len(rates) * 100
        print(f"  {bin_labels[i]}: {count} ({percentage:.2f}%)")

def main():
    """主函数"""
    # 数据文件路径
    json_file_path = "/fossfs/xiaozhen/Sample/swin_waternet_v20_3/samples.json"

    print("开始分析水体比例变化率...")
    print(f"数据文件: {json_file_path}")

    # 1. 加载数据
    no_missing_samples = load_no_missing_samples(json_file_path)
    if not no_missing_samples:
        return

    # 2. 按位置分组
    location_groups = group_samples_by_location(no_missing_samples)

    # 3. 计算变化率
    variation_rates = calculate_variation_rates(location_groups, min_samples=2)

    # 4. 统计分析
    analyze_variation_statistics(variation_rates)

    # 5. 绘制直方图
    plot_variation_histogram(variation_rates)

    print("\n分析完成！")

if __name__ == "__main__":
    main()