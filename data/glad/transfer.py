#!/usr/bin/env python3
import json
import subprocess
import os
import tempfile
import atexit
from pathlib import Path
import argparse
import sys
import shutil

class SSHConnectionManager:
    def __init__(self, remote_host, remote_user):
        self.remote_host = remote_host
        self.remote_user = remote_user
        self.control_path = None
        self.master_process = None
        
    def establish_connection(self):
        """建立SSH主连接"""
        # 创建临时目录存放control socket
        temp_dir = tempfile.mkdtemp()
        self.control_path = os.path.join(temp_dir, "ssh_control_%h_%p_%r")
        
        # 建立SSH主连接
        ssh_cmd = [
            'ssh', '-M', '-S', self.control_path,
            '-o', 'ControlPersist=yes',
            '-o', 'ControlMaster=yes',
            '-N', '-f',
            f'{self.remote_user}@{self.remote_host}'
        ]
        
        print(f"建立SSH连接到 {self.remote_user}@{self.remote_host}")
        print("请输入密码（只需输入一次）：")
        
        try:
            subprocess.run(ssh_cmd, check=True)
            print("SSH连接已建立")
            return True
        except subprocess.CalledProcessError:
            print("无法建立SSH连接")
            return False
    
    def close_connection(self):
        """关闭SSH主连接"""
        if self.control_path:
            close_cmd = ['ssh', '-S', self.control_path, '-O', 'exit', 
                        f'{self.remote_user}@{self.remote_host}']
            try:
                subprocess.run(close_cmd, check=True, capture_output=True)
            except:
                pass

def extract_file_paths(json_file):
    """从JSON文件中提取no_missing_samples部分的所有唯一file_path"""
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        file_paths = set()
        
        if 'no_missing_samples' in data:
            no_missing_data = data['no_missing_samples']
            
            if isinstance(no_missing_data, dict):
                for file_path in no_missing_data.keys():
                    file_paths.add(file_path)
                    
                for file_path, records in no_missing_data.items():
                    if isinstance(records, list):
                        for record in records:
                            if isinstance(record, dict) and 'file_path' in record:
                                file_paths.add(record['file_path'])
        else:
            print("警告: JSON文件中没有找到'no_missing_samples'部分")
        
        return list(file_paths)
    
    except Exception as e:
        print(f"读取JSON文件时出错: {e}")
        return []

def transfer_files_with_control(file_paths, ssh_manager, remote_path, use_rsync=True):
    """使用SSH ControlMaster传输文件"""
    successful_transfers = []
    failed_transfers = []
    
    control_path = ssh_manager.control_path
    remote_user = ssh_manager.remote_user
    remote_host = ssh_manager.remote_host
    
    # 确保远程目录存在
    ssh_mkdir_cmd = [
        'ssh', '-S', control_path,
        f'{remote_user}@{remote_host}',
        f'mkdir -p {remote_path}'
    ]
    try:
        subprocess.run(ssh_mkdir_cmd, check=True)
    except subprocess.CalledProcessError:
        print(f"无法在远程服务器创建目录: {remote_path}")
        return successful_transfers, failed_transfers
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            failed_transfers.append(file_path)
            continue

        # 直接传输到目标文件夹，不保留原始文件夹结构
        # 传输文件
        if use_rsync:
            cmd = [
                'rsync', '-avz', '--progress',
                '-e', f'ssh -S {control_path}',
                file_path,
                f'{remote_user}@{remote_host}:{remote_path}/'
            ]
        else:
            cmd = [
                'scp', '-o', f'ControlPath={control_path}',
                file_path,
                f'{remote_user}@{remote_host}:{remote_path}/'
            ]
        
        print(f"正在传输: {file_path}")
        try:
            subprocess.run(cmd, check=True)
            successful_transfers.append(file_path)
            print(f"成功传输: {file_path}")
        except subprocess.CalledProcessError as e:
            print(f"传输失败: {file_path}, 错误: {e}")
            failed_transfers.append(file_path)
    
    return successful_transfers, failed_transfers

def transfer_files_locally(file_paths, target_path, skip_existing=True):
    """在同一服务器内部传输文件"""
    successful_transfers = []
    failed_transfers = []
    skipped_transfers = []

    # 确保目标目录存在
    try:
        os.makedirs(target_path, exist_ok=True)
        print(f"目标目录已准备: {target_path}")
    except Exception as e:
        print(f"无法创建目标目录 {target_path}: {e}")
        return successful_transfers, failed_transfers, skipped_transfers

    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            failed_transfers.append(file_path)
            continue

        # 获取文件名（不包含路径）
        filename = os.path.basename(file_path)
        target_file_path = os.path.join(target_path, filename)

        # 检查目标文件是否已存在
        if skip_existing and os.path.exists(target_file_path):
            print(f"文件已存在，跳过: {target_file_path}")
            skipped_transfers.append(file_path)
            continue

        print(f"正在复制: {file_path} -> {target_file_path}")
        try:
            # 使用shutil.copy2保留文件元数据
            shutil.copy2(file_path, target_file_path)
            successful_transfers.append(file_path)
            print(f"成功复制: {file_path}")
        except Exception as e:
            print(f"复制失败: {file_path}, 错误: {e}")
            failed_transfers.append(file_path)

    return successful_transfers, failed_transfers, skipped_transfers

def main():
    parser = argparse.ArgumentParser(description='从JSON文件的no_missing_samples部分提取文件路径并传输文件')
    parser.add_argument('json_file', help='包含文件路径的JSON文件')
    parser.add_argument('target_path', help='目标路径（本地路径或远程服务器路径）')
    parser.add_argument('--local', action='store_true', help='在本地服务器内部传输文件')
    parser.add_argument('--remote-host', help='远程服务器地址（远程传输时必需）')
    parser.add_argument('--remote-user', help='远程服务器用户名（远程传输时必需）')
    parser.add_argument('--use-scp', action='store_true', help='使用scp而不是rsync（仅远程传输）')
    parser.add_argument('--overwrite', action='store_true', help='覆盖已存在的文件（默认跳过已存在文件）')
    parser.add_argument('--dry-run', action='store_true', help='只显示要传输的文件，不实际传输')
    
    args = parser.parse_args()

    # 验证参数
    if not args.local and (not args.remote_host or not args.remote_user):
        print("错误: 远程传输需要指定 --remote-host 和 --remote-user 参数")
        sys.exit(1)

    # 提取文件路径
    print("正在从JSON文件的no_missing_samples部分提取文件路径...")
    file_paths = extract_file_paths(args.json_file)

    if not file_paths:
        print("未找到任何文件路径")
        sys.exit(1)

    unique_paths = list(set(file_paths))
    print(f"需要传输 {len(unique_paths)} 个文件")

    if args.dry_run:
        print("\n将要传输的文件:")
        for path in unique_paths:
            print(f"  {path}")
        return

    # 初始化变量
    successful = []
    failed = []
    skipped = []

    # 根据传输类型执行不同的逻辑
    if args.local:
        # 本地传输
        print(f"\n开始本地传输文件到: {args.target_path}")
        skip_existing = not args.overwrite
        if skip_existing:
            print("模式: 跳过已存在的文件")
        else:
            print("模式: 覆盖已存在的文件")
        successful, failed, skipped = transfer_files_locally(unique_paths, args.target_path, skip_existing)
    else:
        # 远程传输
        print(f"\n开始远程传输文件到: {args.remote_user}@{args.remote_host}:{args.target_path}")

        # 建立SSH连接
        ssh_manager = SSHConnectionManager(args.remote_host, args.remote_user)

        if not ssh_manager.establish_connection():
            print("无法建立SSH连接，退出")
            sys.exit(1)

        # 注册退出时关闭连接
        atexit.register(ssh_manager.close_connection)

        # 传输文件
        successful, failed = transfer_files_with_control(
            unique_paths,
            ssh_manager,
            args.target_path,
            use_rsync=not args.use_scp
        )

        # 关闭SSH连接
        ssh_manager.close_connection()
    
    # 打印结果
    print(f"\n传输完成!")
    print(f"成功: {len(successful)} 个文件")
    print(f"失败: {len(failed)} 个文件")

    # 如果是本地传输，显示跳过的文件信息
    if args.local and skipped:
        print(f"跳过: {len(skipped)} 个文件")
        print("\n跳过的文件（已存在）:")
        for path in skipped:
            print(f"  {path}")
    elif args.local:
        print(f"跳过: 0 个文件")

    if failed:
        print("\n失败的文件:")
        for path in failed:
            print(f"  {path}")

    # 保存传输日志
    log_file = "transfer_log.txt"
    with open(log_file, 'w') as f:
        f.write(f"从no_missing_samples提取的文件传输日志\n")
        f.write(f"{'='*50}\n")
        f.write(f"成功传输的文件 ({len(successful)}):\n")
        for path in successful:
            f.write(f"{path}\n")
        f.write(f"\n失败的文件 ({len(failed)}):\n")
        for path in failed:
            f.write(f"{path}\n")

        # 如果是本地传输，记录跳过的文件
        if args.local:
            f.write(f"\n跳过的文件 ({len(skipped)}):\n")
            for path in skipped:
                f.write(f"{path}\n")
    print(f"\n传输日志已保存到: {log_file}")

if __name__ == "__main__":
    main()