"""
Advanced Indexing and Sample Balancing for GLAD Water Body Spatiotemporal Data

This module implements:
1. Efficient indexing of GLAD NetCDF tile data with multi-dimensional binning
2. Stratified sampling across water coverage, missing data, and spatiotemporal features
3. Statistical analysis and reporting of sample distributions
4. Reservoir sampling for balanced dataset construction
5. Recording and tracking of no-missing samples for ground truth generation
6. Collection of missing samples with uniform missing ratio distribution
7. GLAD data format support: 0-100 probability values, 255 for missing data, >50 for water
"""

import os
import gc  # 添加内存管理
import numpy as np
import pandas as pd
import xarray as xr
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from tqdm import tqdm
import logging
import json
import csv
from dataclasses import dataclass, asdict
from collections import defaultdict
import warnings
import random
import time
import concurrent.futures
# import threading
import math  # 新增：用于批次数计算

# 添加项目根目录到Python路径
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

# 添加配置导入
from configs import get_config, Config

# GLAD数据格式常量
GLAD_NODATA = 255  # GLAD无数据值
WATER_THRESHOLD = 50  # 水体阈值（大于50表示水体）
GLAD_WATER_MIN = 0   # GLAD水体值范围最小值
GLAD_WATER_MAX = 100   # GLAD水体值范围最大值

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


def _extract_samples_from_single_file(nc_file, config_params):
    """
    Extract sample information from a single NetCDF file - module level function for multiprocessing
    Optimized for fast scanning of entire dataset without artificial limits

    Args:
        nc_file: Path to NetCDF file
        config_params: Dictionary with necessary configuration parameters

    Returns:
        Tuple of (nc_file, (no_missing_samples, missing_samples))
    """
    try:
        # 设置进程级别的随机种子，确保可重现的随机性
        file_seed = hash(str(nc_file)) % (2**32)
        random.seed(file_seed)
        np.random.seed(file_seed)

        # 预先检查文件类型，快速跳过有问题的文件
        skip_reason = _quick_file_check(nc_file)
        if skip_reason:
            logger.debug(f"Skipping {nc_file}: {skip_reason}")
            return nc_file, ([], [])

        # 直接进行样本提取，不创建IndexBuilder实例
        no_missing_samples, missing_samples = _extract_samples_simple(nc_file, config_params)

        # 内存管理：显式触发垃圾回收
        gc.collect()

        return nc_file, (no_missing_samples, missing_samples)

    except Exception as e:
        logger.error(f"Failed to process {nc_file}: {e}")
        gc.collect()
        return nc_file, ([], [])


def _quick_file_check(nc_file: Path) -> Optional[str]:
    """
    快速检查文件是否可用，只检查mean_water_frequency是否全为空

    Args:
        nc_file: NetCDF文件路径

    Returns:
        如果文件可用返回None，否则返回跳过原因
    """
    try:
        # 使用netcdf4引擎打开文件
        ds = xr.open_dataset(nc_file, engine='netcdf4')

        with ds:
            # 检查是否存在mean_water_frequency变量
            if 'mean_water_frequency' not in ds.data_vars:
                return "Missing mean_water_frequency variable"

            # 检查mean_water_frequency是否全为NaN
            try:
                mean_freq_data = ds.mean_water_frequency.values
                if np.all(np.isnan(mean_freq_data)):
                    return "All mean_water_frequency values are NaN"
            except Exception:
                return "Cannot read mean_water_frequency data"

            # 如果通过所有检查，返回None表示文件可用
            return None

    except Exception as e:
        return f"File check error: {str(e)}"




def _index_file_locations(nc_file: Path, cfg: Dict) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """Extract location candidates and optional missing samples from a single NetCDF file.
    Uses existing variables: water_proportion (x,y,t), missing_proportion (x,y,t),
    mean_water_frequency (x,y), tile_lon (x,y), tile_lat (x,y), time (t).

    Returns:
        (location_candidates, missing_samples) where location_candidates is a list of dicts:
        {
            'source_file': str, 'idx_x': int, 'idx_y': int,
            'freq_bin': int, 'wp_mean_bin': int, 'lat_bin': int, 'lon_bin': int,
            'mean_water_frequency': float, 'water_prop_mean': float,
            'tile_lon': float, 'tile_lat': float, 'no_miss_count': int,
            'time_bins': Dict[(int,int), List[int]]
        }
        missing_samples is currently an empty list (placeholder for compatibility).
    """
    try:
        ds = xr.open_dataset(nc_file, engine='netcdf4')
    except Exception:
        try:
            ds = xr.open_dataset(nc_file, engine='h5netcdf')
        except Exception as e:
            logger.warning(f"Open failed for {nc_file}: {e}")
            return [], []

    with ds:
        required_vars = ['water_proportion', 'missing_proportion', 'mean_water_frequency', 'tile_lon', 'tile_lat', 'time']
        for v in required_vars:
            if v not in ds and v not in ds.data_vars and v not in ds.coords:
                logger.warning(f"File {nc_file} missing {v}")
                return [], []

        try:
            # 计算 no-missing 时间步计数与 water_proportion 平均值（仅在 no-missing 时间上）
            miss = ds['missing_proportion']  # (x,y,t)
            wp = ds['water_proportion']      # (x,y,t)
            no_miss_mask = (miss == 0)
            no_miss_count = no_miss_mask.sum(dim='time')  # (x,y)
            wp_mean = wp.where(no_miss_mask).mean(dim='time', skipna=True)  # (x,y)

            mwf = ds['mean_water_frequency']  # (x,y)
            lon = ds['tile_lon']
            lat = ds['tile_lat']

            # materialize small 2D arrays
            no_miss_count_v = no_miss_count.values
            wp_mean_v = wp_mean.values
            mwf_v = mwf.values
            lon_v = lon.values
            lat_v = lat.values

            # 有效位置过滤
            min_loc = int(cfg['min_samples_per_location'])
            valid = (no_miss_count_v >= min_loc) & ~np.isnan(wp_mean_v) \
                    & ~np.isnan(mwf_v) & (mwf_v > 0.05) & (mwf_v < 0.95) \
                    & ~np.isnan(lon_v) & ~np.isnan(lat_v)
            
            if not np.any(valid):
                return [], []

            # 时间 -> 年/月 -> 分箱
            time_vals = pd.to_datetime(ds['time'].values)
            years = np.array([t.year for t in time_vals])
            months = np.array([t.month for t in time_vals])
            year_edges = cfg['year_edges']
            month_edges = cfg['month_edges']
            year_bins = np.clip(np.digitize(years, year_edges) - 1, 0, len(year_edges) - 2)
            month_bins = np.clip(np.digitize(months, month_edges) - 1, 0, len(month_edges) - 2)

            # 位置层特征分箱（freq, wp_mean, lat, lon）
            freq_edges = cfg['frequency_edges']
            wp_edges = cfg['water_edges']
            lat_edges = cfg['lat_edges']
            lon_edges = cfg['lon_edges']

            x_size = mwf.sizes.get('idx_x', mwf.shape[0])
            y_size = mwf.sizes.get('idx_y', mwf.shape[1])

            candidates: List[Dict[str, Any]] = []

            # 遍历有效位置，构建 time_bins（仅 no-missing 时间）
            valid_idx = np.argwhere(valid)
            for ix, iy in valid_idx:
                mwf_val = float(mwf_v[ix, iy])
                lon_val = float(lon_v[ix, iy])
                lat_val = float(lat_v[ix, iy])
                wp_mean_val = float(wp_mean_v[ix, iy])
                no_cnt = int(no_miss_count_v[ix, iy])

                fb = int(np.clip(np.digitize(mwf_val, freq_edges) - 1, 0, len(freq_edges) - 2))
                wb = int(np.clip(np.digitize(wp_mean_val, wp_edges) - 1, 0, len(wp_edges) - 2))
                latb = int(np.clip(np.digitize(lat_val, lat_edges) - 1, 0, len(lat_edges) - 2))
                lonb = int(np.clip(np.digitize(lon_val, lon_edges) - 1, 0, len(lon_edges) - 2))

                nm_vec = no_miss_mask.isel(idx_x=int(ix), idx_y=int(iy)).values  # 1D bool
                if not np.any(nm_vec):
                    continue
                t_idx = np.where(nm_vec)[0]
                # group by (year_bin, month_bin)
                tbins: Dict[Tuple[int, int], List[int]] = {}
                yb = year_bins[t_idx]
                mb = month_bins[t_idx]
                for k in range(len(t_idx)):
                    key = (int(yb[k]), int(mb[k]))
                    tbins.setdefault(key, []).append(int(t_idx[k]))

                candidates.append({
                    'source_file': str(nc_file), 'idx_x': int(ix), 'idx_y': int(iy),
                    'freq_bin': fb, 'wp_mean_bin': wb, 'lat_bin': latb, 'lon_bin': lonb,
                    'mean_water_frequency': mwf_val, 'water_prop_mean': wp_mean_val,
                    'tile_lon': lon_val, 'tile_lat': lat_val,
                    'no_miss_count': no_cnt, 'time_bins': tbins,
                })

            # 缺失样本收集：为减少爆炸性增长，每个位置最多采样少量缺失时间步
            file_missing: List[Dict[str, Any]] = []
            try:
                miss_v = miss.values  # (x,y,t)
                # 每个位置最多采样的缺失时间数
                max_miss_per_loc = int(cfg.get('max_missing_per_loc', 2))
                # 遍历所有位置（向量化筛位置 + 小循环取时间索引）
                xsz, ysz, tsz = miss_v.shape
                # 生成位置索引
                for ix in range(xsz):
                    for iy in range(ysz):
                        mvec = miss_v[ix, iy, :]
                        # 跳过全无缺失
                        if not np.any(mvec > 0):
                            continue
                        miss_t_idx = np.where(mvec > 0)[0]
                        if miss_t_idx.size == 0:
                            continue
                        # 随机选择少量时间步，避免暴增
                        sel = miss_t_idx
                        if sel.size > max_miss_per_loc:
                            sel = np.random.choice(sel, size=max_miss_per_loc, replace=False)
                        mwf_val = float(mwf_v[ix, iy]) if not np.isnan(mwf_v[ix, iy]) else float('nan')
                        lon_val = float(lon_v[ix, iy]) if not np.isnan(lon_v[ix, iy]) else float('nan')
                        lat_val = float(lat_v[ix, iy]) if not np.isnan(lat_v[ix, iy]) else float('nan')
                        for ti in sel:
                            file_missing.append({
                                'file_path': str(nc_file),
                                'idx_x': int(ix), 'idx_y': int(iy), 'time_idx': int(ti),
                                'missing_proportion': float(miss_v[ix, iy, ti]),
                                'mean_water_frequency': mwf_val,
                                'tile_lon': lon_val, 'tile_lat': lat_val,
                                'year': int(years[ti]), 'month': int(months[ti]),
                            })
            except Exception as _:
                pass

            return candidates, file_missing
        except Exception as e:
            logger.error(f"Error processing {nc_file}: {e}")
            return [], []



def _sample_times_for_location(loc: Dict[str, Any], max_samples: int) -> List[Dict[str, Any]]:
    """Select time indices for one location with year priority then month, with a hard cap.
    Strategy:
      - For each available year_bin, select 1 sample per non-empty month_bin.
      - If the total number of samples exceeds max_samples, randomly downsample to the limit.
    """
    # Build year -> month -> list of time_idx
    year_to_month: Dict[int, Dict[int, List[int]]] = {}
    for (yb, mb), t_list in loc['time_bins'].items():
        if not t_list:
            continue
        yd = year_to_month.setdefault(int(yb), {})
        yd.setdefault(int(mb), []).extend(t_list)

    # 使用基于位置信息的确定性种子，确保结果可重现
    seed = hash(loc['source_file']) + loc['idx_x'] * 1000 + loc['idx_y']
    rng = random.Random(seed)

    samples: List[Dict[str, Any]] = []
    for yb, month_map in year_to_month.items():
        if not month_map:
            continue
        # strict month balancing: take one per non-empty month within this year
        for mb, t_list in month_map.items():
            t_idx = rng.choice(t_list)
            samples.append({
                'file_path': loc['source_file'],
                'idx_x': loc['idx_x'],
                'idx_y': loc['idx_y'],
                'time_idx': int(t_idx),
                'mean_water_frequency': loc['mean_water_frequency'],
                'tile_lon': loc['tile_lon'],
                'tile_lat': loc['tile_lat'],
            })

    # --- 新增：如果样本数超过上限，则进行随机下采样 ---
    if len(samples) > max_samples:
        samples = rng.sample(samples, max_samples)

    return samples


def _fill_sample_values(samples_by_file: Dict[str, List[Dict[str, Any]]]) -> None:
    """Populate water_proportion, missing_proportion, year, month for selected samples.
    Read scalars efficiently by opening each file once.
    """
    for file_path, lst in samples_by_file.items():
        if not lst:
            continue
        try:
            ds = xr.open_dataset(file_path, engine='netcdf4')
        except Exception:
            try:
                ds = xr.open_dataset(file_path, engine='h5netcdf')
            except Exception as e:
                logger.warning(f"Open failed for {file_path} during fill: {e}")
                continue
        with ds:
            t_values = pd.to_datetime(ds['time'].values)
            for s in lst:
                ix = int(s['idx_x']); iy = int(s['idx_y']); ti = int(s['time_idx'])
                try:
                    s['water_proportion'] = float(ds['water_proportion'].isel(idx_x=ix, idx_y=iy, time=ti).item())
                    s['missing_proportion'] = float(ds['missing_proportion'].isel(idx_x=ix, idx_y=iy, time=ti).item())
                except Exception:
                    # fallback to NaN values if read fails
                    s['water_proportion'] = float('nan')
                    s['missing_proportion'] = float('nan')
                try:
                    t = t_values[ti]
                    s['year'] = int(pd.Timestamp(t).year)
                    s['month'] = int(pd.Timestamp(t).month)
                except Exception:
                    s['year'] = -1; s['month'] = -1

def _extract_samples_simple(nc_file: Path, config_params: Dict) -> Tuple[List[Dict], List[Dict]]:
    """
    从单个NetCDF文件中快速提取样本信息，无数量和时间限制
    使用高效的随机采样策略扫描整个数据集

    Args:
        nc_file: NetCDF文件路径
        config_params: 包含必要配置参数的字典

    Returns:
        Tuple of (no_missing_samples, missing_samples)
    """
    no_missing_samples = []
    missing_samples = []

    ds = None
    try:
        # 优化: 使用更大的chunks以提高读取效率
        ds = xr.open_dataset(nc_file,
                            chunks={'time': 50, 'idx_x': 50, 'idx_y': 50},
                            engine='netcdf4')
    except Exception as e:
        logger.error(f"Failed to open {nc_file} with netcdf4 engine: {e}")
        try:
            # 如果netcdf4引擎失败，尝试h5netcdf引擎
            ds = xr.open_dataset(nc_file, engine='h5netcdf')
        except:
            pass

    if ds is None:
        logger.error(f"Failed to open {nc_file} with any available engine")
        return [], []

    try:
        with ds:
            # Check required variables
            required_vars = ['water_proportion', 'missing_proportion',
                           'mean_water_frequency', 'tile_lon', 'tile_lat']

            missing_vars = []
            available_vars = list(ds.data_vars) + list(ds.coords)

            for var in required_vars:
                if var not in available_vars:
                    missing_vars.append(var)

            if missing_vars:
                logger.warning(f"File {nc_file} missing required variables: {missing_vars}")
                return [], []

            # Check dimensions
            try:
                x_size = ds.dims.get('idx_x', 0)
                y_size = ds.dims.get('idx_y', 0)
                t_size = ds.dims.get('time', 0)
            except Exception as e:
                logger.error(f"Failed to read dimensions from {nc_file}: {e}")
                return [], []

            if x_size == 0 or y_size == 0 or t_size == 0:
                logger.warning(f"File {nc_file} has zero-sized dimensions: x={x_size}, y={y_size}, t={t_size}")
                return [], []

            # 优化: 使用更高效的空间采样策略
            # 增加空间采样密度，确保覆盖更多区域
            spatial_sample_factor = config_params.get('spatial_sample_factor', 0.8)  # 提高到80%
            total_spatial_locations = x_size * y_size
            max_spatial_samples = int(total_spatial_locations * spatial_sample_factor)

            # 生成随机空间坐标 - 使用numpy提高效率
            spatial_indices_x = np.random.randint(0, x_size, size=max_spatial_samples)
            spatial_indices_y = np.random.randint(0, y_size, size=max_spatial_samples)
            spatial_coords = list(zip(spatial_indices_x, spatial_indices_y))

            # 去重但保持随机性
            spatial_coords = list(set(spatial_coords))
            random.shuffle(spatial_coords)

            # 优化: 使用更高效的时间采样
            time_indices = list(range(t_size))

            # Pre-load time information
            try:
                time_coords_all = pd.to_datetime(ds.time.values)
                years = np.array([time_coords_all[i].year for i in time_indices])
                months = np.array([time_coords_all[i].month for i in time_indices])
            except Exception as e:
                logger.error(f"Failed to parse time coordinates from {nc_file}: {e}")
                return [], []

            # 批量读取空间数据
            spatial_data_cache = {}
            x_coords = [coord[0] for coord in spatial_coords]
            y_coords = [coord[1] for coord in spatial_coords]

            try:
                # 优化: 使用更大的批次读取空间数据
                batch_size = 1000
                for i in range(0, len(spatial_coords), batch_size):
                    end_i = min(i + batch_size, len(spatial_coords))
                    batch_x_coords = x_coords[i:end_i]
                    batch_y_coords = y_coords[i:end_i]

                    mean_freq_sample = ds.mean_water_frequency[batch_x_coords, batch_y_coords].values
                    tile_lon_sample = ds.tile_lon[batch_x_coords, batch_y_coords].values
                    tile_lat_sample = ds.tile_lat[batch_x_coords, batch_y_coords].values

                    # 构建快速查找字典
                    for j in range(len(batch_x_coords)):
                        idx_x, idx_y = spatial_coords[i+j]
                        spatial_data_cache[(idx_x, idx_y)] = {
                            'mean_freq': float(mean_freq_sample[j]),
                            'tile_lon': float(tile_lon_sample[j]),
                            'tile_lat': float(tile_lat_sample[j])
                        }

                    del mean_freq_sample, tile_lon_sample, tile_lat_sample

            except Exception as batch_error:
                # 如果批量读取失败，回退到逐个读取
                logger.debug(f"Batch spatial reading failed for {nc_file}: {batch_error}, falling back to individual reads")
                for idx_x, idx_y in spatial_coords:
                    try:
                        spatial_data_cache[(idx_x, idx_y)] = {
                            'mean_freq': float(ds.mean_water_frequency[idx_x, idx_y].values),
                            'tile_lon': float(ds.tile_lon[idx_x, idx_y].values),
                            'tile_lat': float(ds.tile_lat[idx_x, idx_y].values)
                        }
                    except Exception:
                        continue

            # 优化: 使用更大的批处理大小提高效率
            chunk_size = 100
            processed_samples = 0

            for chunk_start in range(0, len(time_indices), chunk_size):
                chunk_end = min(chunk_start + chunk_size, len(time_indices))
                chunk_time_indices = time_indices[chunk_start:chunk_end]

                try:
                    # 批量读取GLAD原始数据
                    glad_data_chunks = {}

                    # 优化: 使用向量化读取
                    for idx_x, idx_y in spatial_coords:
                        try:
                            # 读取GLAD原始数据 (time, y, x) -> (time, 256, 256)
                            glad_data = ds.data[idx_x, idx_y, chunk_time_indices, :, :].values
                            glad_data_chunks[(idx_x, idx_y)] = glad_data
                        except Exception:
                            continue
                except Exception as chunk_error:
                    logger.warning(f"Failed to read data chunk {chunk_start}:{chunk_end} from {nc_file}: {chunk_error}")
                    continue

                # 处理每个空间位置
                for spatial_idx, (idx_x, idx_y) in enumerate(spatial_coords):
                    spatial_key = (idx_x, idx_y)
                    if spatial_key not in spatial_data_cache:
                        continue

                    spatial_data = spatial_data_cache[spatial_key]
                    mean_freq = spatial_data['mean_freq']
                    tile_lon = spatial_data['tile_lon']
                    tile_lat = spatial_data['tile_lat']

                    if np.isnan(mean_freq) or np.isnan(tile_lon) or np.isnan(tile_lat):
                        continue

                    # 处理这个位置的时间序列
                    try:
                        if spatial_key not in glad_data_chunks:
                            continue
                        glad_data_series = glad_data_chunks[spatial_key]  # (time, 256, 256)
                    except Exception:
                        continue

                    # 处理每个时间步
                    for chunk_idx in range(len(chunk_time_indices)):
                        # 直接使用全局时间索引，无需再次线性搜索
                        abs_time_idx = chunk_time_indices[chunk_idx]

                        # 获取当前时间步的GLAD数据 (256, 256)
                        glad_data_tile = glad_data_series[chunk_idx]

                        # 计算水体比例和缺失比例
                        total_pixels = glad_data_tile.size
                        water_pixels = np.count_nonzero((glad_data_tile > WATER_THRESHOLD) & (glad_data_tile <= GLAD_WATER_MAX))
                        nodata_pixels = np.count_nonzero(glad_data_tile == GLAD_NODATA)

                        water_prop = water_pixels / total_pixels if total_pixels > 0 else 0.0
                        missing_prop = nodata_pixels / total_pixels if total_pixels > 0 else 0.0

                        sample = {
                            'file_path': str(nc_file),
                            'idx_x': idx_x,
                            'idx_y': idx_y,
                            'time_idx': abs_time_idx,
                            'water_proportion': water_prop,
                            'missing_proportion': missing_prop,
                            'mean_water_frequency': mean_freq,
                            'tile_lon': tile_lon,
                            'tile_lat': tile_lat,
                            'year': int(years[abs_time_idx]),
                            'month': int(months[abs_time_idx]),
                        }

                        processed_samples += 1

                        # 按缺失比例分类收集 - 无数量限制
                        if missing_prop == 0.0:
                            no_missing_samples.append(sample)
                        else:
                            missing_samples.append(sample)

                # 清理内存
                try:
                    if 'glad_data_chunks' in locals():
                        del glad_data_chunks
                except:
                    pass

                # 定期垃圾回收
                if chunk_start % (chunk_size * 5) == 0:
                    gc.collect()

    except Exception as e:
        logger.error(f"Error processing file {nc_file}: {e}")
        gc.collect()
        return [], []

    # 记录处理结果
    if logger.level <= logging.DEBUG:
        logger.debug(f"File {nc_file}: collected {len(no_missing_samples)} no-missing samples, "
                    f"{len(missing_samples)} missing samples from "
                    f"{len(spatial_coords)} spatial locations and {len(time_indices)} time points")

    # 清理内存
    try:
        del spatial_data_cache, spatial_coords, time_indices, years, months, x_coords, y_coords
        gc.collect()
    except:
        pass

    return no_missing_samples, missing_samples


def _process_single_file(nc_file, config_params):
    """
    Process a single NetCDF file - module level function for multiprocessing

    Args:
        nc_file: Path to NetCDF file
        config_params: Dictionary with necessary configuration parameters

    Returns:
        Tuple of (nc_file, (no_missing_samples, missing_samples))
    """
    return _extract_samples_from_single_file(nc_file, config_params)


@dataclass
class SampleInfo:
    """Information about a single sample (tile at specific time)"""
    file_path: str
    idx_x: int
    idx_y: int
    time_idx: int

    # Feature values
    water_proportion: float
    missing_proportion: float
    mean_water_frequency: float
    tile_lon: float
    tile_lat: float
    year: int
    month: int

    # Bin assignments
    water_bin: int
    missing_bin: int
    frequency_bin: int
    lat_bin: int
    lon_bin: int
    year_bin: int
    month_bin: int

    # Combined bin key for stratified sampling
    bin_key: Tuple[int, ...]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


@dataclass
class LocationCandidate:
    """Aggregated location candidate used for two-stage sampling.
    A location is uniquely identified by (source_file, idx_x, idx_y).
    time_bins stores only no-missing time indices grouped by (year_bin, month_bin).
    """
    source_file: str
    idx_x: int
    idx_y: int
    # location-level stratification bins
    freq_bin: int
    wp_mean_bin: int
    lat_bin: int
    lon_bin: int
    # scalar features for output
    mean_water_frequency: float
    water_prop_mean: float
    tile_lon: float
    tile_lat: float
    no_miss_count: int
    # mapping (year_bin, month_bin) -> List[time_idx]
    time_bins: Dict[Tuple[int, int], List[int]]
    # per-time water proportion and year/month cache (only for no-missing time_idx)
    wp_by_time: Dict[int, float]
    ym_by_time: Dict[int, Tuple[int, int]]


class ReservoirSampler:
    """Reservoir sampling implementation for uniform random sampling"""

    def __init__(self, capacity: int):
        """
        Initialize reservoir sampler

        Args:
            capacity: Maximum number of samples to keep
        """
        self.capacity = capacity
        self.samples = []
        self.count = 0

    def add(self, sample: Any) -> None:
        """Add a sample to the reservoir"""
        self.count += 1

        if len(self.samples) < self.capacity:
            # Reservoir not full yet
            self.samples.append(sample)
        else:
            # Randomly replace existing sample
            j = random.randint(0, self.count - 1)
            if j < self.capacity:
                self.samples[j] = sample

    def get_samples(self) -> List[Any]:
        """Get all samples in the reservoir"""
        return self.samples.copy()

    def clear(self) -> None:
        """Clear the reservoir to free memory"""
        self.samples.clear()
        self.count = 0


class FeatureBinner:
    """Multi-dimensional feature binning for stratified sampling"""

    def __init__(self, config: Optional[Dict] = None, verbose: bool = True):
        """
        Initialize feature binner

        Args:
            config: Configuration dictionary from cfg().sampling.feature_bins
            verbose: Whether to print initialization logs
        """
        # Default bin edges (matching config.yaml)
        default_bins = {
            'water_proportion_bins': [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
            'missing_proportion_bins': [0.05, 0.2, 0.4, 0.6, 0.8, 1.0],
            'mean_water_frequency_bins': [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
            'lon_bins': [-180, -160, -140, -120, -100, -80, -60, -40, -20, 0, 20, 40, 60, 80, 100, 120, 140, 160, 180],
            'lat_bins': [-90, -70, -50, -30, -10, 10, 30, 50, 70, 90],
            'year_bins': [1984, 1995, 2005, 2015, 2025],
            'month_bins': [1, 4, 7, 10, 13]
        }

        # Use config bins if provided, otherwise use defaults
        if config is None:
            config = default_bins

        # Set bin edges from config
        self.water_edges = np.array(config.get('water_proportion_bins', default_bins['water_proportion_bins']))
        self.missing_edges = np.array(config.get('missing_proportion_bins', default_bins['missing_proportion_bins']))
        self.frequency_edges = np.array(config.get('mean_water_frequency_bins', default_bins['mean_water_frequency_bins']))
        self.lon_edges = np.array(config.get('lon_bins', default_bins['lon_bins']))
        self.lat_edges = np.array(config.get('lat_bins', default_bins['lat_bins']))
        self.year_edges = np.array(config.get('year_bins', default_bins['year_bins']))
        self.month_edges = np.array(config.get('month_bins', default_bins['month_bins']))

        # Calculate number of bins
        self.water_bins = len(self.water_edges) - 1
        self.missing_bins = len(self.missing_edges) - 1
        self.frequency_bins = len(self.frequency_edges) - 1
        self.lon_bins = len(self.lon_edges) - 1
        self.lat_bins = len(self.lat_edges) - 1
        self.year_bins = len(self.year_edges) - 1
        self.month_bins = len(self.month_edges) - 1

        # 只在需要时打印初始化信息，避免多进程重复输出
        if verbose:
            bin_nums = self.water_bins * self.missing_bins * self.frequency_bins * self.lon_bins * self.lat_bins * self.year_bins * self.month_bins
            logger.info(f"Initialized FeatureBinner with {bin_nums} bins")
            logger.info(f"Water edges: {self.water_edges}")
            logger.info(f"Missing edges: {self.missing_edges}")
            logger.info(f"Frequency edges: {self.frequency_edges}")
            logger.info(f"Lat edges: {self.lat_edges}")
            logger.info(f"Lon edges: {self.lon_edges}")
            logger.info(f"Year edges: {self.year_edges}")
            logger.info(f"Month edges: {self.month_edges}")

    def fit_bin_edges(self, samples: List[Dict[str, Any]]) -> None:
        """
        Bin edges are pre-defined from config, no need to fit from data
        This method is kept for compatibility but doesn't change the edges
        """
        logger.info("Using pre-defined bin edges from configuration")
        logger.info(f"Bin edges: water={self.water_edges}, "
                   f"missing={self.missing_edges}, "
                   f"frequency={self.frequency_edges}")

    def assign_bins(self, sample: Dict[str, Any]) -> Tuple[int, int, int, int, int, int, int]:
        """
        Assign bin indices for a sample using pre-defined bin edges

        Args:
            sample: Sample dictionary with feature values

        Returns:
            Tuple of (water_bin, missing_bin, frequency_bin, lat_bin, lon_bin, year_bin, month_bin)
        """
        # Water proportion bin (include the end right value)
        water_bin = np.digitize(sample['water_proportion'], self.water_edges) - 1
        water_bin = np.clip(water_bin, 0, self.water_bins - 1)

        # Missing proportion bin (include the end right value)
        missing_bin = np.digitize(sample['missing_proportion'], self.missing_edges) - 1
        missing_bin = np.clip(missing_bin, 0, self.missing_bins - 1)

        # Water frequency bin (include the end right value)
        frequency_bin = np.digitize(sample['mean_water_frequency'], self.frequency_edges) - 1
        frequency_bin = np.clip(frequency_bin, 0, self.frequency_bins - 1)

        # Latitude bin (include the end right value)
        lat_bin = np.digitize(sample['tile_lat'], self.lat_edges) - 1
        lat_bin = np.clip(lat_bin, 0, self.lat_bins - 1)

        # Longitude bin (include the end right value)
        lon_bin = np.digitize(sample['tile_lon'], self.lon_edges) - 1
        lon_bin = np.clip(lon_bin, 0, self.lon_bins - 1)

        # Year bin (include the end right value)
        year_bin = np.digitize(sample['year'], self.year_edges) - 1
        year_bin = np.clip(year_bin, 0, self.year_bins - 1)

        # Month bin (include the end right value)
        month_bin = np.digitize(sample['month'], self.month_edges) - 1
        month_bin = np.clip(month_bin, 0, self.month_bins - 1)

        return water_bin, missing_bin, frequency_bin, lat_bin, lon_bin, year_bin, month_bin

    def create_bin_key(self, sample: Dict[str, Any]) -> Tuple[int, ...]:
        """Create combined bin key for stratified sampling"""
        return self.assign_bins(sample)


class IndexBuilder:
    """Main class for building dataset index with balanced sampling"""

    def __init__(self, config: Optional[Config] = None, verbose: bool = True):
        """
        Initialize index builder

        Args:
            config: 统一配置对象，如果None则使用全局配置
            verbose: Whether to print initialization logs
        """
        self.config = config or get_config()

        # 从统一配置获取参数
        self.feature_binner = FeatureBinner(self.config.sampling.get('feature_bins', {}), verbose=verbose)

        # Sampling parameters - 统一使用基于bin的参数
        self.min_samples_per_bin = self.config.sampling.get('min_samples_per_bin', 10)
        self.max_samples_per_bin = self.config.sampling.get('max_samples_per_bin', 1000)
        self.max_missing_samples_per_bin = self.config.sampling.get('max_missing_samples_per_bin', 2000)  # 降低默认值，减少内存压力
        self.sampling_strategy = self.config.sampling.get('sampling_strategy', 'reservoir')

        # 新增：位置层过滤与容量
        self.min_samples_per_location = int(self.config.sampling.get('min_samples_per_location', 2))
        self.max_samples_per_location = int(self.config.sampling.get('max_samples_per_location', 10)) # 默认值设为12，例如一年12个月
        self.max_locations_per_bin = int(self.config.sampling.get('max_locations_per_bin', 1000))
        # 缺失样本：每个位置最多采样的缺失时间步数（控制增长）
        self.max_missing_per_loc = int(self.config.sampling.get('max_missing_per_loc', 5))

        # Use missing proportion bins from config instead of equal intervals
        missing_bins = self.config.sampling.get('feature_bins', {}).get('missing_proportion_bins',
                                                                       [0.05, 0.2, 0.4, 0.6, 0.8, 1.0])
        self.missing_ratio_bins = len(missing_bins) - 1  # Number of bins
        self.missing_ratio_edges = np.array(missing_bins)  # Bin edges

        # 只在需要时打印初始化信息，避免多进程重复输出
        if verbose:
            logger.info(f"IndexBuilder initialized with {self.sampling_strategy} sampling strategy")
            logger.info(f"Sampling limits: max_samples_per_bin={self.max_samples_per_bin}, "
                       f"max_missing_samples_per_bin={self.max_missing_samples_per_bin}")
            logger.info(f"Missing ratio bins: {self.missing_ratio_bins} bins with edges {self.missing_ratio_edges}")
            # Explicitly log the bin indices we'll be using
            logger.info(f"Missing ratio bin indices: {list(range(self.missing_ratio_bins))}")

    def _export_distribution_statistics(self, balanced_no_missing: Dict[str, List[Dict]],
                                       missing_samples: Dict[int, List[Dict]],
                                       csv_path: Union[str, Path]) -> None:
        """
        Export detailed distribution statistics for all no-missing samples across variables

        Args:
            balanced_no_missing: Dictionary of balanced no-missing samples by file
            missing_samples: Dictionary of missing samples by ratio bin (not used)
            csv_path: Output path for CSV file
        """
        # 创建输出目录
        Path(csv_path).parent.mkdir(parents=True, exist_ok=True)

        # Flatten only no-missing samples
        all_no_missing = []
        for file_samples in balanced_no_missing.values():
            all_no_missing.extend(file_samples)

        # Only use no-missing samples for statistics
        all_samples = all_no_missing

        # 计算每个特征的bin边界
        water_edges = self.feature_binner.water_edges
        missing_edges = self.feature_binner.missing_edges
        frequency_edges = self.feature_binner.frequency_edges
        lat_edges = self.feature_binner.lat_edges
        lon_edges = self.feature_binner.lon_edges
        year_edges = self.feature_binner.year_edges
        month_edges = self.feature_binner.month_edges

        # 计算每个维度的bin标签
        water_labels = [f"{water_edges[i]:.1f}-{water_edges[i+1]:.1f}" for i in range(len(water_edges)-1)]
        missing_labels = [f"{missing_edges[i]:.1f}-{missing_edges[i+1]:.1f}" for i in range(len(missing_edges)-1)]
        frequency_labels = [f"{frequency_edges[i]:.1f}-{frequency_edges[i+1]:.1f}" for i in range(len(frequency_edges)-1)]
        lat_labels = [f"{lat_edges[i]:.1f}-{lat_edges[i+1]:.1f}" for i in range(len(lat_edges)-1)]
        lon_labels = [f"{lon_edges[i]:.1f}-{lon_edges[i+1]:.1f}" for i in range(len(lon_edges)-1)]
        year_labels = [f"{int(year_edges[i])}-{int(year_edges[i+1])}" for i in range(len(year_edges)-1)]
        month_labels = [f"{int(month_edges[i])}-{int(month_edges[i+1])}" for i in range(len(month_edges)-1)]

        # 按每个维度分别计数
        counts = {
            "water_proportion": defaultdict(int),
            "missing_proportion": defaultdict(int),
            "mean_water_frequency": defaultdict(int),
            "lat": defaultdict(int),
            "lon": defaultdict(int),
            "year": defaultdict(int),
            "month": defaultdict(int),
            "sample_type": {"no_missing": len(all_no_missing)},
            "total": len(all_samples)
        }

        # 按水体比例分箱计数
        for sample in all_samples:
            # Water proportion bin
            water_bin = np.digitize(sample['water_proportion'], water_edges) - 1
            water_bin = np.clip(water_bin, 0, len(water_labels)-1)
            counts["water_proportion"][water_labels[water_bin]] += 1

            # Missing proportion bin
            missing_bin = np.digitize(sample['missing_proportion'], missing_edges) - 1
            missing_bin = np.clip(missing_bin, 0, len(missing_labels)-1)
            counts["missing_proportion"][missing_labels[missing_bin]] += 1

            # Water frequency bin
            frequency_bin = np.digitize(sample['mean_water_frequency'], frequency_edges) - 1
            frequency_bin = np.clip(frequency_bin, 0, len(frequency_labels)-1)
            counts["mean_water_frequency"][frequency_labels[frequency_bin]] += 1

            # Latitude bin
            lat_bin = np.digitize(sample['tile_lat'], lat_edges) - 1
            lat_bin = np.clip(lat_bin, 0, len(lat_labels)-1)
            counts["lat"][lat_labels[lat_bin]] += 1

            # Longitude bin
            lon_bin = np.digitize(sample['tile_lon'], lon_edges) - 1
            lon_bin = np.clip(lon_bin, 0, len(lon_labels)-1)
            counts["lon"][lon_labels[lon_bin]] += 1

            # Year bin
            year_bin = np.digitize(sample['year'], year_edges) - 1
            year_bin = np.clip(year_bin, 0, len(year_labels)-1)
            counts["year"][year_labels[year_bin]] += 1

            # Month bin
            month_bin = np.digitize(sample['month'], month_edges) - 1
            month_bin = np.clip(month_bin, 0, len(month_labels)-1)
            counts["month"][month_labels[month_bin]] += 1

        # 将数据写入CSV文件
        with open(csv_path, 'w', newline='') as f:
            writer = csv.writer(f)

            # 写入基本信息
            writer.writerow(['No-Missing Dataset Distribution Statistics'])
            writer.writerow(['Total No-Missing Samples', counts["total"]])
            writer.writerow([])

            # 写入各维度分布
            for feature, label in [
                ("water_proportion", "Water Proportion"),
                ("missing_proportion", "Missing Proportion"),
                ("mean_water_frequency", "Mean Water Frequency"),
                ("lat", "Latitude"),
                ("lon", "Longitude"),
                ("year", "Year"),
                ("month", "Month")
            ]:
                writer.writerow([label, 'Count', 'Percentage (%)'])
                feature_counts = counts[feature]

                # 对键进行排序以保持顺序一致
                def safe_sort_key(x):
                    try:
                        if x and '-' in x:
                            first_part = x.split('-')[0]
                            if feature != "month" and feature != "year":
                                return float(first_part)
                            else:
                                return int(first_part)
                        return 0  # 对于无效的bin标签返回0
                    except (ValueError, IndexError):
                        return 0  # 转换失败时返回0

                sorted_bins = sorted(feature_counts.keys(), key=safe_sort_key)

                total_count = sum(feature_counts.values())
                for bin_label in sorted_bins:
                    bin_count = feature_counts[bin_label]
                    percentage = (bin_count / total_count) * 100 if total_count > 0 else 0
                    writer.writerow([bin_label, bin_count, f"{percentage:.2f}"])

                writer.writerow([])

        logger.info(f"No-missing samples distribution statistics exported to {csv_path}")

    def build_index(self,
                   data_dir: Union[str, Path],
                   output_path: Optional[Union[str, Path]] = None,
                   max_workers: int = 16,
                   batch_size: int = 100,
                   save_freq: int = 5) -> Tuple[Dict[str, List[Dict]], Dict[int, List[Dict]]]:
        """
        Build balanced dataset index from NetCDF files

        Args:
            data_dir: Directory containing NetCDF files
            output_path: Path to save index (optional)
            max_workers: Number of parallel workers
            batch_size: Number of files to process in each batch
            save_freq: Save interim results every N batches (0 to disable)

        Returns:
            A tuple containing:
            1. Dictionary of balanced no-missing samples by file
            2. Dictionary of missing samples by missing ratio bin
        """
        data_dir = Path(data_dir)
        logger.info(f"Building index from {data_dir}")

        # Find NetCDF files
        nc_files = list(data_dir.rglob("*.nc"))
        if not nc_files:
            raise ValueError(f"No NetCDF files found in {data_dir}")

        logger.info(f"Found {len(nc_files)} NetCDF files")

        # 4D 位置层总 bin 数（用于日志）
        total_location_bins = (self.feature_binner.frequency_bins * self.feature_binner.water_bins *
                               self.feature_binner.lat_bins * self.feature_binner.lon_bins)
        logger.info(f"Total location bins (freq×water_mean×lat×lon): {total_location_bins}")

        # 为每个 missing ratioq bin 创建 reservoir sampler
        missing_samplers_global = {
            i: ReservoirSampler(self.max_missing_samples_per_bin)
            for i in range(self.missing_ratio_bins+1)
        }

        # 位置层 reservoir（freq_bin, wp_mean_bin, lat_bin, lon_bin）
        location_reservoirs = {}  # key: 4D bin, value: ReservoirSampler for LocationCandidates

        logger.info(f"Initialized reservoir samplers: "
                   f"missing capacity per bin={self.max_missing_samples_per_bin}, "
                   f"no-missing capacity per multi-dim bin={self.max_samples_per_bin}")

        # 使用用户指定的内存总量或默认使用传入的工作进程数
        user_memory_gb = getattr(self.config, 'system', {}).get('memory_gb')
        if user_memory_gb:
            # 假设每个工作进程需要2GB内存，并保留25%的内存作为系统和主进程使用
            available_memory_for_workers = user_memory_gb * 0.75  # 75%的内存用于工作进程
            workers_by_memory = max(1, int(available_memory_for_workers / 2))  # 每个工作进程约2GB
            actual_workers = min(max_workers, workers_by_memory)
            logger.info(f"Available memory: {user_memory_gb:.1f} GB, allocating {actual_workers} workers")
            max_workers = actual_workers
        else:
            logger.info(f"Using specified worker count: {max_workers}")

        # 配置进程池参数
        mp_context = None
        try:
            # 使用spawn方法可以减少内存占用
            import multiprocessing as mp
            mp_context = mp.get_context('spawn')
            logger.info("Using 'spawn' context for multiprocessing")
        except Exception as e:
            logger.warning(f"Failed to get spawn context: {e}, using default context")

        # v2 pipeline uses explicit cfg built per batch; legacy params removed

        processed_count = 0
        # 计算批次数（至少 1 批）
        num_batches = max(1, math.ceil(len(nc_files) / batch_size))

        logger.info(f"Processing {len(nc_files)} files with {max_workers} workers across {num_batches} batches (batch_size={batch_size})")

        # 用于记录失败的文件
        failed_files = []

        # 创建单个进程池，用于所有批次处理
        executor_kwargs = {'max_workers': max_workers}
        if mp_context:
            executor_kwargs['mp_context'] = mp_context

        with ProcessPoolExecutor(**executor_kwargs) as executor:
            # 分批处理文件
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(nc_files))
                batch_files = nc_files[start_idx:end_idx]

                logger.info(f"Processing batch {batch_idx + 1}/{num_batches}, files {start_idx}-{end_idx-1}")

                # 为这个批次准备临时结果存储
                batch_results = []

                try:
                    # Submit batch tasks
                    futures = []
                    # 构造简化配置传给子进程，避免不可序列化对象
                    cfg = {
                        'min_samples_per_location': self.min_samples_per_location,
                        'year_edges': self.feature_binner.year_edges,
                        'month_edges': self.feature_binner.month_edges,
                        'frequency_edges': self.feature_binner.frequency_edges,
                        'water_edges': self.feature_binner.water_edges,
                        'lat_edges': self.feature_binner.lat_edges,
                        'lon_edges': self.feature_binner.lon_edges,
                        'max_missing_per_loc': self.max_missing_per_loc,
                    }
                    for nc_file in batch_files:
                        future = executor.submit(_index_file_locations, nc_file, cfg)
                        futures.append((future, nc_file))

                    # Collect results with progress bar
                    for future, nc_file in tqdm(futures, desc=f"Batch {batch_idx + 1}/{num_batches}"):
                        try:
                            result = future.result()
                            batch_results.append(result)
                        except Exception as e:
                            logger.warning(f"Error processing {nc_file}: {str(e)}")
                            failed_files.append((str(nc_file), str(e)))

                except Exception as batch_error:
                    # 处理整个批次失败的情况
                    logger.error(f"Batch processing failed: {batch_error}")
                    # 以单线程方式重试这个批次中的文件
                    logger.info(f"Retrying batch {batch_idx + 1} files in single-threaded mode")
                    for nc_file in tqdm(batch_files, desc=f"Batch {batch_idx + 1} retry"):
                        try:
                            cfg = {
                                'min_samples_per_location': self.min_samples_per_location,
                                'year_edges': self.feature_binner.year_edges,
                                'month_edges': self.feature_binner.month_edges,
                                'frequency_edges': self.feature_binner.frequency_edges,
                                'water_edges': self.feature_binner.water_edges,
                                'lat_edges': self.feature_binner.lat_edges,
                                'lon_edges': self.feature_binner.lon_edges,
                            }
                            result = _index_file_locations(nc_file, cfg)
                            batch_results.append(result)
                        except Exception as e:
                            logger.warning(f"Single-thread processing failed for {nc_file}: {str(e)}")
                            failed_files.append((str(nc_file), str(e)))


                # 添加一个处理单个结果的函数，用于线程池
                def process_single_result(result):
                    # 存储处理结果的容器，每个线程独立处理
                    local_no_missing_samplers = {}
                    local_missing_samples = [[] for _ in range(self.missing_ratio_bins+1)]

                    try:
                        loc_candidates, file_missing = result

                        # 将 location 候选加入位置层 reservoir（按 4D 位置键）
                        for loc in loc_candidates:
                            key4 = (loc['freq_bin'], loc['wp_mean_bin'], loc['lat_bin'], loc['lon_bin'])
                            if key4 not in local_no_missing_samplers:
                                local_no_missing_samplers[key4] = []
                            local_no_missing_samplers[key4].append(loc)

                        # 添加 missing 样本到对应的 bin
                        for sample in file_missing:
                            sample['source_file'] = str(nc_file)
                            # 计算缺失比例所属的bin索引
                            ratio_bin = np.digitize(sample['missing_proportion'], self.missing_ratio_edges) - 1
                            ratio_bin = int(np.clip(ratio_bin, 0, self.missing_ratio_bins))

                            local_missing_samples[ratio_bin].append(sample)

                        return (True, nc_file, local_no_missing_samplers, local_missing_samples)
                    except Exception as e:
                        logger.warning(f"Failed to process result: {e}")
                        return (False, None, {}, [])

                processed_count_local = 0

                # 使用线程池并行处理结果
                with ThreadPoolExecutor(max_workers=min(32, os.cpu_count() * 4)) as thread_executor:
                    futures = []
                    for result in batch_results:
                        futures.append(thread_executor.submit(process_single_result, result))

                    # 收集并整合结果
                    for future in tqdm(as_completed(futures), total=len(futures), desc=f"Processing results"):
                        try:
                            success, _, local_no_missing_samplers, local_missing_samples = future.result()

                            if success:
                                # 整合位置候选（4D 位置键）
                                for key4, locs in local_no_missing_samplers.items():
                                    if key4 not in location_reservoirs:
                                        location_reservoirs[key4] = ReservoirSampler(self.max_locations_per_bin)
                                    for loc in locs:
                                        location_reservoirs[key4].add(loc)

                                # 整合missing样本
                                for ratio_bin, samples in enumerate(local_missing_samples):
                                    for sample in samples:
                                        missing_samplers_global[ratio_bin].add(sample)

                                processed_count_local += 1
                        except Exception as e:
                            logger.warning(f"Failed to process thread result: {e}")

                # 更新总处理计数
                processed_count += processed_count_local

                # 每批次处理完毕后，强制进行垃圾回收
                logger.info(f"Batch {batch_idx + 1}/{num_batches} completed, performing garbage collection...")
                gc.collect()
                time.sleep(2)

        # 优化: 两阶段抽样 - 位置层后时间层
        logger.info("Selecting locations (4D bins) and then sampling times (year priority)...")
        
        # 位置层：统计各 4D 位置 bin 的候选数量
        bin_loc_counts = {k: v.count for k, v in location_reservoirs.items()}
        if not bin_loc_counts:
            logger.warning("No location candidates found in any 4D bin – skipping.")
            balanced_no_missing_samples_by_file = defaultdict(list)
        else:
            median_non_zero = np.percentile([c for c in bin_loc_counts.values() if c > 0], 50)
            target_per_locbin = int(max(1, round(float(median_non_zero)))) if median_non_zero > 0 else 1
            logger.info(f"Location balancing target per 4D bin: {target_per_locbin} (median_non_zero={median_non_zero})")

            # 抽取位置
            selected_locations: List[Dict[str, Any]] = []
            rng = random.Random(42)
            for key4, sampler in location_reservoirs.items():
                locs = sampler.get_samples()
                if len(locs) > target_per_locbin:
                    selected_locations.extend(rng.sample(locs, target_per_locbin))
                else:
                    selected_locations.extend(locs)

            logger.info(f"Selected {len(selected_locations)} locations across {len(location_reservoirs)} 4D bins")

            # 时间层：按“先年后月”对每个位置抽样
            balanced_no_missing_samples_by_file = defaultdict(list)
            for loc in selected_locations:
                # 将 self.max_samples_per_location 传递给函数
                tsamples = _sample_times_for_location(loc, self.max_samples_per_location)
                if tsamples:
                    balanced_no_missing_samples_by_file[loc['source_file']].extend(tsamples)

        # 填充样本的水体/缺失比例与年月
        _fill_sample_values(balanced_no_missing_samples_by_file)


        # 第四阶段：平衡missing样本
        balanced_missing_samples_by_ratio_bin = {}
        missing_bin_counts = []

        # 收集所有missing样本
        for bin_idx, sampler in missing_samplers_global.items():
            samples = sampler.get_samples()
            if samples:
                balanced_missing_samples_by_ratio_bin[bin_idx] = samples
                missing_bin_counts.append(len(samples))

        # 如果有样本，平衡各缺失比例bin中的样本数
        if missing_bin_counts:
            # 使用中位数作为平衡目标
            missing_balance_target = max(1, int(np.median(missing_bin_counts)))

            logger.info(f"Setting missing balance target to {missing_balance_target} samples per bin")

            # 平衡各bin中的样本数
            for bin_idx, samples in balanced_missing_samples_by_ratio_bin.items():
                if len(samples) > missing_balance_target:
                    # 随机抽取目标数量的样本
                    balanced_missing_samples_by_ratio_bin[bin_idx] = random.sample(samples, missing_balance_target)

            # 记录最终分布
            final_missing_counts = [len(balanced_missing_samples_by_ratio_bin.get(i, []))
                                  for i in range(self.missing_ratio_bins+1)]
            logger.info(f"Final missing distribution: {final_missing_counts}")

        # 清理 reservoir samplers
        for sampler in location_reservoirs.values():
            sampler.clear()
        for sampler in missing_samplers_global.values():
            sampler.clear()
        del location_reservoirs, missing_samplers_global
        gc.collect()

        logger.info(f"Balanced sampling results: "
                   f"no-missing={sum(len(samples) for samples in balanced_no_missing_samples_by_file.values())} samples "
                   f"from {len(balanced_no_missing_samples_by_file)} files, "
                   f"missing={sum(len(samples) for samples in balanced_missing_samples_by_ratio_bin.values())} samples "
                   f"across {len(balanced_missing_samples_by_ratio_bin)} bins")

        # 记录处理失败的文件总数
        if failed_files:
            logger.warning(f"Total failed files: {len(failed_files)}")
            if output_path:
                failed_path = str(Path(output_path).with_suffix('')) + "_failed_files.json"
                with open(failed_path, 'w') as f:
                    json.dump(failed_files, f, indent=2)
                logger.warning(f"Complete failed files list saved to {failed_path}")

        # Export statistics if output path is provided
        if output_path:
            # 保存主索引JSON文件
            self._save_index(balanced_no_missing_samples_by_file, balanced_missing_samples_by_ratio_bin, output_path)

            # 保存详细分布统计信息
            dist_path = Path(str(output_path).replace('.json', '_distribution.csv'))
            self._export_distribution_statistics(balanced_no_missing_samples_by_file, balanced_missing_samples_by_ratio_bin, dist_path)

        return balanced_no_missing_samples_by_file, dict(balanced_missing_samples_by_ratio_bin)


    def _save_index(self, balanced_no_missing: Dict[str, List[Dict]],
                   missing_by_ratio: Dict[int, List[Dict]],
                   output_path: Union[str, Path]) -> None:
        """
        Save index data to file

        Args:
            balanced_no_missing: Dictionary of balanced no-missing samples by file
            missing_by_ratio: Dictionary of missing samples by ratio bin
            output_path: Output path for index file
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Prepare index data
        index_data = {
            'no_missing_samples': balanced_no_missing,
            'missing_samples_by_ratio': {
                str(bin_idx): samples for bin_idx, samples in missing_by_ratio.items()
            },
            'bin_edges': {
                'water_edges': self.feature_binner.water_edges.tolist(),
                'missing_edges': self.feature_binner.missing_edges.tolist(),
                'frequency_edges': self.feature_binner.frequency_edges.tolist(),
                'lat_edges': self.feature_binner.lat_edges.tolist(),
                'lon_edges': self.feature_binner.lon_edges.tolist(),
                'year_edges': self.feature_binner.year_edges.tolist(),
                'month_edges': self.feature_binner.month_edges.tolist(),
            },
            'metadata': {
                'total_no_missing_samples': sum(len(samples) for samples in balanced_no_missing.values()),
                'total_missing_samples': sum(len(samples) for samples in missing_by_ratio.values()),
                'num_no_missing_files': len(balanced_no_missing),
                'num_missing_bins': len(missing_by_ratio),
                'sampling_strategy': self.sampling_strategy,
            }
        }

        # Save as JSON
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"Index saved to {output_path}")
        logger.info(f"Saved {index_data['metadata']['total_no_missing_samples']} balanced no-missing samples "
                   f"from {index_data['metadata']['num_no_missing_files']} files")
        logger.info(f"Saved {index_data['metadata']['total_missing_samples']} missing samples "
                   f"across {index_data['metadata']['num_missing_bins']} missing ratio bins")


def main():
    """Main function for command-line usage"""
    import argparse

    parser = argparse.ArgumentParser(description="Build balanced dataset index from NetCDF files")
    parser.add_argument("data_dir", help="Directory containing NetCDF files")
    parser.add_argument("--output", "-o", help="Output path for index file")
    parser.add_argument("--workers", "-w", type=int, default=16, help="Number of parallel workers")
    parser.add_argument("--batch-size", "-b", type=int, default=100, help="Number of files to process in each batch")
    parser.add_argument("--spatial-sample-factor", "-s", type=float, default=0.8,
                      help="Spatial sampling factor (0.0-1.0), higher values sample more locations")
    parser.add_argument("--config", "-c", help="Path to config file (optional)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--limit", "-l", type=int, help="Limit the number of files to process (for testing)")
    parser.add_argument("--save-frequency", "-f", type=int, default=5,
                      help="Save interim results every N batches (0 to disable)")
    parser.add_argument("--memory-gb", type=float, help="Total memory available in GB")
    parser.add_argument("--min-samples-per-bin", type=int, default=10,
                      help="Minimum samples required per feature bin")
    parser.add_argument("--target-samples-per-bin", type=int, default=50,
                      help="Target samples per feature bin for balancing")

    args = parser.parse_args()

    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Initialize configuration
    if args.config:
        config = get_config(args.config)
    else:
        config = get_config()  # Use default config

    # Apply optimization settings from command line
    if not hasattr(config, 'sampling'):
        config.sampling = {}

    # Update sampling parameters
    if args.spatial_sample_factor:
        config.sampling['spatial_sample_factor'] = args.spatial_sample_factor

    if args.min_samples_per_bin:
        config.sampling['min_samples_per_bin'] = args.min_samples_per_bin

    # Add memory information to config
    if not hasattr(config, 'system'):
        config.system = {}

    # Explicitly set memory if provided
    if args.memory_gb:
        config.system['memory_gb'] = args.memory_gb
        logger.info(f"Using user-specified memory: {args.memory_gb} GB")

    # Initialize index builder with configuration
    builder = IndexBuilder(config, verbose=True)

    # Build index
    output_path = args.output or (Path(args.data_dir) / "dataset_index.json")

    logger.info("=" * 80)
    logger.info("Starting Dataset Index Building")
    logger.info("=" * 80)

    try:
        # 如果指定了数量限制，只处理部分文件
        if args.limit:
            logger.info(f"Limiting processing to {args.limit} files (for testing)")
            data_files = list(Path(args.data_dir).rglob("*.nc"))
            if len(data_files) > args.limit:
                import random
                random.shuffle(data_files)
                data_files = data_files[:args.limit]
                # 创建临时目录来存放这些文件的符号链接
                temp_dir = Path(args.data_dir) / "temp_limited_dataset"
                temp_dir.mkdir(exist_ok=True)
                # 创建符号链接
                for file in data_files:
                    try:
                        link_path = temp_dir / file.name
                        if not link_path.exists():
                            os.symlink(file, link_path)
                    except Exception as e:
                        logger.error(f"Failed to create symlink for {file}: {e}")
                # 使用这个临时目录进行处理
                data_dir = temp_dir
            else:
                data_dir = args.data_dir
        else:
            data_dir = args.data_dir

        # 更新批处理大小参数
        if args.batch_size:
            logger.info(f"Using batch size of {args.batch_size} files")

        # 构建索引
        balanced_no_missing, missing_by_ratio = builder.build_index(
            data_dir,
            output_path,
            args.workers,
            batch_size=args.batch_size,
            save_freq=args.save_frequency
        )

        # Print processing summary
        logger.info("=" * 80)
        logger.info("Index Building Completed Successfully!")
        logger.info("=" * 80)

        total_no_missing = sum(len(samples) for samples in balanced_no_missing.values())
        total_missing = sum(len(samples) for samples in missing_by_ratio.values())

        logger.info(f"📊 Processing Summary:")
        logger.info(f"   • No-missing samples: {total_no_missing:,} from {len(balanced_no_missing)} files")
        logger.info(f"   • Missing samples: {total_missing:,} across {len(missing_by_ratio)} bins")
        logger.info(f"   • Total samples: {total_no_missing + total_missing:,}")

        # Print feature distribution summary
        logger.info(f"📈 Feature Distribution Summary:")
        logger.info(f"   • Spatial sampling factor: {config.sampling.get('spatial_sample_factor', 0.8):.1f}")
        logger.info(f"   • Minimum samples per bin: {config.sampling.get('min_samples_per_bin', 10)}")

        logger.info(f"📁 Output Files:")
        logger.info(f"   • Index: {output_path}")
        logger.info(f"   • Statistics: {Path(output_path).with_suffix('.csv')}")

        logger.info(f"⚙️  Configuration: {config.config_path}")

        # 清理大对象
        try:
            del balanced_no_missing, missing_by_ratio, builder
            gc.collect()
        except:
            pass  # 忽略删除失败

    except Exception as e:
        logger.error(f"❌ Index building failed: {e}")
        logger.error("Please check the logs above for detailed error information")
        # 异常情况下也要清理内存
        gc.collect()
        raise


if __name__ == "__main__":
    main()