#!/usr/bin/env python3
"""
将水体比例时间序列的变化率写回 samples.json
变化率定义: (max(water_proportion) - min(water_proportion)) / mean(water_proportion)
分组键: (file_path, idx_x, idx_y)
"""
from __future__ import annotations
import json
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple, Any
import argparse
import numpy as np


def load_samples(json_path: Path) -> Dict[str, Any]:
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def group_by_location(no_missing_samples: Dict[str, List[Dict[str, Any]]]) -> Dict[Tuple[str, int, int], List[float]]:
    groups: Dict[Tuple[str, int, int], List[float]] = defaultdict(list)
    total = 0
    for file_path, samples in no_missing_samples.items():
        for s in samples:
            try:
                idx_x = int(s['idx_x'])
                idx_y = int(s['idx_y'])
                wp = float(s['water_proportion'])
            except Exception:
                # 跳过缺字段或非数值
                continue
            key = (file_path, idx_x, idx_y)
            groups[key].append(wp)
            total += 1
    print(f"共计样本条目: {total}; 唯一位置: {len(groups)}")
    return groups


def compute_variation_rates(groups: Dict[Tuple[str, int, int], List[float]], min_samples: int = 2) -> Dict[Tuple[str, int, int], float]:
    rates: Dict[Tuple[str, int, int], float] = {}
    for key, seq in groups.items():
        if len(seq) < min_samples:
            continue
        arr = np.asarray(seq, dtype=np.float32)
        mean_val = float(np.mean(arr))
        if mean_val <= 0:
            continue
        rate = float((np.max(arr) - np.min(arr)) / mean_val)
        rates[key] = rate
    print(f"计算得到变化率的位置数: {len(rates)}")
    return rates


def write_back(json_path: Path, data: Dict[str, Any], rates: Dict[Tuple[str, int, int], float]) -> None:
    no_missing = data.get('no_missing_samples', {})
    updated, missing = 0, 0
    for file_path, samples in no_missing.items():
        for s in samples:
            try:
                key = (file_path, int(s['idx_x']), int(s['idx_y']))
            except Exception:
                missing += 1
                continue
            rate = rates.get(key)
            if rate is not None:
                s['variation_rate'] = float(rate)
                updated += 1
            else:
                # 没有足够样本或均值为0，置为None以便后续可筛除
                s['variation_rate'] = None
                missing += 1
    # 回写文件
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"已写回: variation_rate 字段。更新 {updated} 条，缺失 {missing} 条。")


def main():
    parser = argparse.ArgumentParser(description='将变化率写入 samples.json')
    parser.add_argument('--input', required=True, help='samples.json 的路径')
    parser.add_argument('--min-samples', type=int, default=2, help='计算变化率所需的最小样本数')
    args = parser.parse_args()

    json_path = Path(args.input)
    if not json_path.exists():
        raise FileNotFoundError(f"未找到文件: {json_path}")

    data = load_samples(json_path)
    no_missing = data.get('no_missing_samples', {})
    if not no_missing:
        raise ValueError('samples.json 中缺少 no_missing_samples')

    groups = group_by_location(no_missing)
    rates = compute_variation_rates(groups, min_samples=args.min_samples)
    write_back(json_path, data, rates)


if __name__ == '__main__':
    main()

