"""
Historical Missing Pattern Database for Water Body Data Augmentation

This module implements:
1. Collection of real missing patterns from NetCDF files
2. Generation of synthetic missing patterns
3. Pattern categorization and organization by missing ratio
4. Efficient storage and retrieval for training data augmentation
"""

import os
import numpy as np
import xarray as xr
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
import random
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import pickle
from dataclasses import dataclass
from sklearn.cluster import DBSCAN
from scipy.ndimage import binary_erosion, binary_dilation
from skimage.measure import label, regionprops
import warnings
import json

try:
    import matplotlib.pyplot as plt
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


@dataclass
class MissingPattern:
    """Represents a missing data pattern"""
    pattern: np.ndarray  # Boolean mask where True = missing
    missing_ratio: float
    pattern_type: str  # 'real', 'random', 'clustered', 'streaky', 'seasonal'
    shape: Tuple[int, int]
    metadata: Dict[str, Any]  # Additional information
    
    def apply_to_data(self, data: np.ndarray) -> np.ndarray:
        """Apply this missing pattern to data"""
        if data.shape[-2:] != self.shape:
            raise ValueError(f"Data shape {data.shape} incompatible with pattern shape {self.shape}")
        
        masked_data = data.copy()
        # Apply pattern to last two dimensions (spatial)
        if len(data.shape) == 2:
            masked_data[self.pattern] = -1  # Use -1 to indicate missing
        elif len(data.shape) == 3:
            masked_data[:, self.pattern] = -1
        else:
            raise ValueError(f"Unsupported data dimensionality: {data.shape}")
        
        return masked_data


class PatternGenerator:
    """Generate synthetic missing patterns"""
    
    def __init__(self, tile_size: int = 256):
        """
        Initialize pattern generator
        
        Args:
            tile_size: Size of tiles to generate patterns for
        """
        self.tile_size = tile_size
        
    def generate_random_pattern(self, missing_ratio: float) -> np.ndarray:
        """Generate random missing pattern"""
        pattern = np.random.random((self.tile_size, self.tile_size)) < missing_ratio
        return pattern
    
    def generate_clustered_pattern(self, missing_ratio: float, 
                                 cluster_size_range: Tuple[int, int] = (10, 50),
                                 num_clusters_range: Tuple[int, int] = (1, 10)) -> np.ndarray:
        """Generate clustered missing pattern"""
        pattern = np.zeros((self.tile_size, self.tile_size), dtype=bool)
        
        # Calculate target missing pixels
        target_missing = int(missing_ratio * self.tile_size * self.tile_size)
        current_missing = 0
        
        # Generate clusters
        max_clusters = random.randint(*num_clusters_range)
        attempts = 0
        
        while current_missing < target_missing and attempts < max_clusters * 2:
            # Random cluster center
            center_x = random.randint(0, self.tile_size - 1)
            center_y = random.randint(0, self.tile_size - 1)
            
            # Random cluster size
            cluster_size = random.randint(*cluster_size_range)
            
            # Create circular cluster
            y, x = np.ogrid[:self.tile_size, :self.tile_size]
            distance = (x - center_x)**2 + (y - center_y)**2
            cluster_mask = distance <= cluster_size**2
            
            # Add cluster to pattern
            new_missing = np.sum(cluster_mask & ~pattern)
            if current_missing + new_missing <= target_missing:
                pattern |= cluster_mask
                current_missing += new_missing
            
            attempts += 1
        
        return pattern
    
    def generate_streaky_pattern(self, missing_ratio: float,
                               streak_width_range: Tuple[int, int] = (2, 8),
                               streak_length_range: Tuple[int, int] = (20, 100)) -> np.ndarray:
        """Generate streaky missing pattern (simulates satellite gaps)"""
        pattern = np.zeros((self.tile_size, self.tile_size), dtype=bool)
        
        target_missing = int(missing_ratio * self.tile_size * self.tile_size)
        current_missing = 0
        
        # Generate streaks
        max_attempts = 50
        attempts = 0
        
        while current_missing < target_missing and attempts < max_attempts:
            # Random streak parameters
            width = random.randint(*streak_width_range)
            length = random.randint(*streak_length_range)
            
            # Random orientation (horizontal, vertical, or diagonal)
            orientation = random.choice(['horizontal', 'vertical', 'diagonal'])
            
            if orientation == 'horizontal':
                start_x = random.randint(0, max(1, self.tile_size - length))
                start_y = random.randint(0, max(1, self.tile_size - width))
                streak_mask = np.zeros((self.tile_size, self.tile_size), dtype=bool)
                streak_mask[start_y:start_y+width, start_x:start_x+length] = True
                
            elif orientation == 'vertical':
                start_x = random.randint(0, max(1, self.tile_size - width))
                start_y = random.randint(0, max(1, self.tile_size - length))
                streak_mask = np.zeros((self.tile_size, self.tile_size), dtype=bool)
                streak_mask[start_y:start_y+length, start_x:start_x+width] = True
                
            else:  # diagonal
                start_x = random.randint(0, self.tile_size - 1)
                start_y = random.randint(0, self.tile_size - 1)
                streak_mask = np.zeros((self.tile_size, self.tile_size), dtype=bool)
                
                # Create diagonal line
                for i in range(length):
                    x = start_x + i
                    y = start_y + i
                    if 0 <= x < self.tile_size and 0 <= y < self.tile_size:
                        for w in range(width):
                            if 0 <= x + w < self.tile_size:
                                streak_mask[y, x + w] = True
            
            # Add streak to pattern
            new_missing = np.sum(streak_mask & ~pattern)
            if current_missing + new_missing <= target_missing:
                pattern |= streak_mask
                current_missing += new_missing
            
            attempts += 1
        
        return pattern
    
    def generate_seasonal_pattern(self, missing_ratio: float) -> np.ndarray:
        """Generate seasonal missing pattern (cloud/weather related)"""
        pattern = np.zeros((self.tile_size, self.tile_size), dtype=bool)
        
        # Create weather-front like patterns
        # Generate smooth random field
        x, y = np.meshgrid(np.linspace(0, 4*np.pi, self.tile_size), 
                          np.linspace(0, 4*np.pi, self.tile_size))
        
        # Multiple frequency components
        field = (np.sin(x + random.uniform(0, 2*np.pi)) * 
                np.cos(y + random.uniform(0, 2*np.pi)) +
                0.5 * np.sin(2*x + random.uniform(0, 2*np.pi)) * 
                np.cos(2*y + random.uniform(0, 2*np.pi)))
        
        # Add noise
        field += 0.3 * np.random.normal(0, 1, (self.tile_size, self.tile_size))
        
        # Convert to missing pattern
        threshold = np.percentile(field, (1 - missing_ratio) * 100)
        pattern = field > threshold
        
        return pattern


class MissingPatternDatabase:
    """Database for storing and managing missing patterns"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize missing pattern database
        
        Args:
            config: Configuration dictionary
        """
        # Default configuration
        default_config = {
            'min_missing_ratio': 0.05,
            'max_missing_ratio': 1.0,
            'num_bins': 20,
            'patterns_per_bin': 100,
            'collect_real_patterns': True,
            'generate_synthetic_patterns': False,
            'pattern_augmentation': True
        }
        
        self.config = config or default_config
        
        # Pattern storage organized by missing ratio bins

        # ===============
        # Here the bins need to be consistent with the missing bins in configs.
        # ===============
        self.min_missing = self.config.get('min_missing_ratio', 0.05)
        self.max_missing = self.config.get('max_missing_ratio', 1.0)
        self.num_bins = self.config.get('num_bins', 20)
        self.patterns_per_bin = self.config.get('patterns_per_bin', 100)
        
        # Create missing ratio bins
        self.missing_ratio_bins = np.linspace(self.min_missing, self.max_missing, self.num_bins + 1)
        
        # Pattern storage: {bin_idx: List[MissingPattern]}
        self.patterns: Dict[int, List[MissingPattern]] = defaultdict(list)
        
        # Pattern generator
        self.pattern_generator = PatternGenerator()
        
        logger.info(f"Initialized MissingPatternDatabase with {self.num_bins} bins")
        logger.info(f"Missing ratio range: [{self.min_missing:.3f}, {self.max_missing:.3f}]")
    
    def _get_missing_ratio_bin(self, missing_ratio: float) -> int:
        """Get bin index for a missing ratio"""
        bin_idx = np.digitize(missing_ratio, self.missing_ratio_bins) - 1
        return np.clip(bin_idx, 0, self.num_bins - 1)
    
    def collect_patterns_from_index(self, missing_samples_by_ratio: Dict[int, List[Dict]], max_workers: int = 8) -> None:
        """
        Collect patterns directly from missing samples by ratio dictionary returned by build_index
        
        Args:
            missing_samples_by_ratio: Dictionary of missing samples by ratio bin from build_index
            max_workers: Number of parallel workers
        """
        logger.info(f"Collecting patterns from {len(missing_samples_by_ratio)} missing ratio bins")
        
        # Flatten samples and organize by file path to optimize file access
        file_to_samples = defaultdict(list)
        total_samples = 0
        
        for bin_idx, bin_samples in missing_samples_by_ratio.items():
            for sample in bin_samples:
                # Newer IndexBuilder versions may use 'source_file' instead of 'file_path'
                file_path = sample.get('file_path') or sample.get('source_file')
                if file_path is None:
                    logger.debug("Sample missing file reference, skipping")
                    continue
                # Ensure the canonical key is present for downstream code
                sample['file_path'] = file_path
                file_to_samples[file_path].append(sample)
                total_samples += 1
                
        logger.info(f"Processing {total_samples} missing samples from {len(file_to_samples)} files")
        
        # Using ProcessPoolExecutor for robust parallelism with libraries that are not thread-safe (like HDF5)
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(self._extract_patterns_from_file_samples, file_path, samples): file_path
                for file_path, samples in file_to_samples.items()
            }
            
            total_patterns = 0
            for future in tqdm(as_completed(future_to_file), total=len(future_to_file), desc="Extracting patterns"):
                try:
                    patterns = future.result()
                    for pattern in patterns:
                        self.add_pattern(pattern)
                    total_patterns += len(patterns)
                except Exception as e:
                    file_path = future_to_file[future]
                    logger.error(f"Failed to extract patterns from {file_path}: {e}")
        
        # Log patterns distribution
        stats = self.get_pattern_statistics()
        logger.info(f"Collected {total_patterns} patterns across {stats['bins_filled']} missing ratio bins")
        logger.info(f"Pattern distribution: {stats['patterns_per_bin']}")

    def _extract_patterns_from_file_samples(self, file_path: str, samples: List[Dict]) -> List[MissingPattern]:
        """
        Extract missing patterns from samples in a specific file
        
        Args:
            file_path: Path to NetCDF file
            samples: List of sample dictionaries from the file
            
        Returns:
            List of extracted MissingPattern objects
        """
        patterns = []
        try:
            # Fix path prefix: replace old path with new path
            # if file_path.startswith('/fossfs/xiaozhen/Clip/JRC4/'):
            #     file_path = file_path.replace('/fossfs/xiaozhen/Clip/JRC4/', '/scr/u/xiaoz/Clip/')
            
            # Each file is opened in a separate process, avoiding thread-safety issues with HDF5.
            with xr.open_dataset(file_path) as ds:
                for sample in samples:
                    idx_x = sample['idx_x']
                    idx_y = sample['idx_y']
                    time_idx = sample['time_idx']
                    
                    try:
                        # Extract data tile and create missing mask
                        tile_data = ds.data[idx_x, idx_y, time_idx].values
                        missing_mask = (tile_data == 255) | (tile_data == 0)  # JRC no_data or no_observation values
                        missing_ratio = float(np.mean(missing_mask))
                        
                        if self.min_missing <= missing_ratio <= self.max_missing:
                            pattern = MissingPattern(
                                pattern=missing_mask,
                                missing_ratio=missing_ratio,
                                pattern_type='real',
                                shape=missing_mask.shape,
                                metadata={
                                    'file_path': file_path,
                                    'idx_x': idx_x,
                                    'idx_y': idx_y,
                                    'time_idx': time_idx,
                                    'water_proportion': sample.get('water_proportion', 0.0),
                                    'missing_proportion': sample.get('missing_proportion', 0.0),
                                    'mean_water_frequency': sample.get('mean_water_frequency', None)
                                }
                            )
                            patterns.append(pattern)
                            
                    except Exception as e:
                        logger.debug(f"Error processing sample ({idx_x},{idx_y},{time_idx}): {e}")
        
        except Exception as e:
            logger.error(f"Error opening or processing {file_path}: {e}")
            
        return patterns
    
    def generate_synthetic_patterns(self, tile_size: int = 256, pattern_types_to_generate: Optional[List[str]] = None) -> None:
        """Generate synthetic missing patterns to fill database"""
        logger.info("Generating synthetic missing patterns")
        
        if pattern_types_to_generate is None:
            # If not specified, generate all types
            pattern_types = ['random', 'clustered', 'streaky', 'seasonal']
            logger.info(f"Generating all synthetic pattern types: {pattern_types}")
        else:
            pattern_types = pattern_types_to_generate
            logger.info(f"Generating specified synthetic pattern types: {pattern_types}")

        if not pattern_types:
            logger.warning("No synthetic pattern types specified or an empty list was provided. Skipping generation.")
            return
        
        for bin_idx in range(self.num_bins):
            # Calculate target missing ratio for this bin
            bin_start = self.missing_ratio_bins[bin_idx]
            bin_end = self.missing_ratio_bins[bin_idx + 1]
            target_ratio = (bin_start + bin_end) / 2
            
            # Count existing patterns in this bin
            existing_count = len(self.patterns[bin_idx])
            needed = max(0, self.patterns_per_bin - existing_count)
            
            if needed == 0:
                continue
            
            logger.info(f"Generating {needed} patterns for bin {bin_idx} (ratio={target_ratio:.3f})")
            
            # Generate patterns of each type
            patterns_per_type = needed // len(pattern_types)
            remainder = needed % len(pattern_types)
            
            for i, pattern_type in enumerate(pattern_types):
                count = patterns_per_type + (1 if i < remainder else 0)
                
                for _ in range(count):
                    try:
                        # Add some randomness to the target ratio
                        ratio_noise = random.uniform(-0.02, 0.02)
                        actual_ratio = np.clip(target_ratio + ratio_noise, 
                                            self.min_missing, self.max_missing)
                        
                        # Generate pattern based on type
                        if pattern_type == 'random':
                            pattern_mask = self.pattern_generator.generate_random_pattern(actual_ratio)
                        elif pattern_type == 'clustered':
                            pattern_mask = self.pattern_generator.generate_clustered_pattern(actual_ratio)
                        elif pattern_type == 'streaky':
                            pattern_mask = self.pattern_generator.generate_streaky_pattern(actual_ratio)
                        elif pattern_type == 'seasonal':
                            pattern_mask = self.pattern_generator.generate_seasonal_pattern(actual_ratio)
                        else:
                            continue
                        
                        # Create pattern object
                        pattern = MissingPattern(
                            pattern=pattern_mask,
                            missing_ratio=np.mean(pattern_mask),
                            pattern_type=pattern_type,
                            shape=(tile_size, tile_size),
                            metadata={'generated': True}
                        )
                        
                        self.add_pattern(pattern)
                        
                    except Exception as e:
                        logger.error(f"Error generating {pattern_type} pattern: {e}")
        
        logger.info("Synthetic pattern generation completed")
    
    def add_pattern(self, pattern: MissingPattern) -> None:
        """Add a pattern to the database"""
        bin_idx = self._get_missing_ratio_bin(pattern.missing_ratio)
        
        # Add to bin if not full
        if len(self.patterns[bin_idx]) < self.patterns_per_bin:
            self.patterns[bin_idx].append(pattern)
        else:
            # Replace random pattern (reservoir sampling)
            replace_idx = random.randint(0, self.patterns_per_bin - 1)
            self.patterns[bin_idx][replace_idx] = pattern
    
    def get_representative_patterns(self, num_per_bin: int = 1) -> Dict[int, List[MissingPattern]]:
        """
        Get representative patterns from each filled bin.
        
        Args:
            num_per_bin: Number of patterns to retrieve from each bin.
            
        Returns:
            Dictionary with bin index as key and list of MissingPattern as value.
        """
        representatives = {}
        # Sorting to ensure consistent output for visualization if needed
        sorted_bins = sorted(self.patterns.keys())
        for bin_idx in sorted_bins:
            patterns = self.patterns[bin_idx]
            if patterns:
                num_to_sample = min(num_per_bin, len(patterns))
                representatives[bin_idx] = random.sample(patterns, num_to_sample)
        return representatives

    def get_random_pattern(self, target_missing_ratio: Optional[float] = None,
                          pattern_type: Optional[str] = None) -> Optional[MissingPattern]:
        """
        Get a random pattern from the database
        
        Args:
            target_missing_ratio: Target missing ratio (if None, random bin)
            pattern_type: Specific pattern type to retrieve
            
        Returns:
            Random MissingPattern or None if not available
        """
        if target_missing_ratio is not None:
            # Get from specific bin
            bin_idx = self._get_missing_ratio_bin(target_missing_ratio)
            candidates = self.patterns[bin_idx]
        else:
            # Get from all bins
            candidates = []
            for patterns in self.patterns.values():
                candidates.extend(patterns)
        
        # Filter by pattern type if specified
        if pattern_type is not None:
            candidates = [p for p in candidates if p.pattern_type == pattern_type]
        
        if not candidates:
            return None
        
        return random.choice(candidates)
    
    def get_pattern_statistics(self) -> Dict[str, Any]:
        """Get statistics about the pattern database"""
        total_patterns = sum(len(patterns) for patterns in self.patterns.values())
        
        stats = {
            'total_patterns': total_patterns,
            'bins_filled': len([bin_idx for bin_idx, patterns in self.patterns.items() if patterns]),
            'patterns_per_bin': {
                str(bin_idx): len(patterns) for bin_idx, patterns in self.patterns.items()
            }
        }
        
        # Pattern type distribution
        type_counts = defaultdict(int)
        ratio_distribution = []
        
        for patterns in self.patterns.values():
            for pattern in patterns:
                type_counts[pattern.pattern_type] += 1
                ratio_distribution.append(pattern.missing_ratio)
        
        stats['pattern_types'] = dict(type_counts)
        
        if ratio_distribution:
            stats['missing_ratio_distribution'] = {
                'mean': float(np.mean(ratio_distribution)),
                'std': float(np.std(ratio_distribution)),
                'min': float(np.min(ratio_distribution)),
                'max': float(np.max(ratio_distribution))
            }
        
        return stats
    
    def save(self, filepath: Union[str, Path]) -> None:
        """Save pattern database to file"""
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert to serializable format
        data = {
            'config': self.config,
            'missing_ratio_bins': self.missing_ratio_bins.tolist(),
            'patterns': {}
        }
        
        for bin_idx, patterns in self.patterns.items():
            data['patterns'][str(bin_idx)] = [
                {
                    'pattern': pattern.pattern.astype(np.uint8),  # Save space
                    'missing_ratio': pattern.missing_ratio,
                    'pattern_type': pattern.pattern_type,
                    'shape': pattern.shape,
                    'metadata': pattern.metadata
                }
                for pattern in patterns
            ]
        
        # Save using numpy format for efficiency
        np.savez_compressed(filepath, **data)
        
        logger.info(f"Pattern database saved to {filepath}")
        
        # Print statistics
        stats = self.get_pattern_statistics()
        logger.info(f"Saved {stats['total_patterns']} patterns across {stats['bins_filled']} bins")
    
    def load(self, filepath: Union[str, Path]) -> None:
        """Load pattern database from file"""
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"Pattern database file not found: {filepath}")
        
        # Load data
        data = np.load(filepath, allow_pickle=True)
        
        # Restore configuration
        self.config = data['config'].item()
        self.missing_ratio_bins = data['missing_ratio_bins']
        
        # Restore patterns
        self.patterns = defaultdict(list)
        patterns_data = data['patterns'].item()
        
        for bin_idx_str, patterns_list in patterns_data.items():
            bin_idx = int(bin_idx_str)
            
            for pattern_data in patterns_list:
                pattern = MissingPattern(
                    pattern=pattern_data['pattern'].astype(bool),
                    missing_ratio=pattern_data['missing_ratio'],
                    pattern_type=pattern_data['pattern_type'],
                    shape=pattern_data['shape'],
                    metadata=pattern_data['metadata']
                )
                self.patterns[bin_idx].append(pattern)
        
        logger.info(f"Pattern database loaded from {filepath}")
        
        # Print statistics
        stats = self.get_pattern_statistics()
        logger.info(f"Loaded {stats['total_patterns']} patterns across {stats['bins_filled']} bins")


def build_missing_database(missing_samples_by_ratio: Dict[int, List[Dict]],
                          output_path: Union[str, Path],
                          config: Optional[Dict] = None,
                          max_workers: int = 8) -> MissingPatternDatabase:
    """
    Build missing pattern database from missing samples by ratio dictionary
    
    Args:
        missing_samples_by_ratio: Dictionary of missing samples by ratio bin from build_index
        output_path: Output path for database file
        config: Configuration dictionary
        max_workers: Number of parallel workers
        
    Returns:
        Built database
    """
    logger.info(f"Building missing pattern database with {sum(len(samples) for samples in missing_samples_by_ratio.values())} samples")
    
    # Initialize database
    db = MissingPatternDatabase(config)
    
    # Collect real patterns from missing samples
    if db.config.get('collect_real_patterns', True):
        db.collect_patterns_from_index(missing_samples_by_ratio, max_workers)
    
    # Generate synthetic patterns if enabled and needed
    if db.config.get('generate_synthetic_patterns', False):
        synthetic_types = db.config.get('synthetic_types')
        db.generate_synthetic_patterns(pattern_types_to_generate=synthetic_types)
    
    # Save database
    db.save(output_path)
    
    return db


def visualize_patterns(db: MissingPatternDatabase, num_per_bin: int = 4, output_file: str = "missing_patterns_visualization.png"):
    """
    Visualizes representative missing patterns from the database.
    
    Args:
        db: The MissingPatternDatabase instance.
        num_per_bin: Number of patterns to show for each bin.
        output_file: Path to save the visualization image.
    """
    if not MATPLOTLIB_AVAILABLE:
        logger.error("Matplotlib is not installed. Please install it to use visualization feature: pip install matplotlib")
        return

    representatives = db.get_representative_patterns(num_per_bin=num_per_bin)
    
    if not representatives:
        logger.info("No patterns to visualize.")
        return
        
    # Sort bins by index
    sorted_bins = sorted(representatives.keys())
    
    num_bins_with_patterns = len(sorted_bins)
    
    fig, axes = plt.subplots(
        num_bins_with_patterns, 
        num_per_bin, 
        figsize=(num_per_bin * 3, num_bins_with_patterns * 3.2),
        squeeze=False
    )
    
    fig.suptitle("Representative Missing Patterns per Missing Ratio Bin", fontsize=16)
    
    for i, bin_idx in enumerate(sorted_bins):
        patterns = representatives[bin_idx]
        bin_start = db.missing_ratio_bins[bin_idx]
        bin_end = db.missing_ratio_bins[bin_idx + 1]
        
        for j, pattern in enumerate(patterns):
            if j < len(patterns):
                ax = axes[i, j]
                ax.imshow(pattern.pattern, cmap='gray', interpolation='nearest')
                
                if j == 0:
                    ax.set_ylabel(f"Bin {bin_idx}\n({bin_start:.2f}-{bin_end:.2f})", rotation=0, size='large', labelpad=50)

                title = f"Type: {pattern.pattern_type}\nRatio: {pattern.missing_ratio:.3f}"
                ax.set_title(title)
                ax.set_xticks([])
                ax.set_yticks([])
            else:
                # Hide unused axes
                axes[i, j].axis('off')

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(output_file)
    logger.info(f"Saved pattern visualization to {output_file}")
    plt.close(fig)


def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build or inspect a missing pattern database.")
    parser.add_argument("--index-file", help="Path to index file containing missing samples. Use to build a new DB.")
    parser.add_argument("--load-db", help="Path to an existing pattern database file to load and inspect.")
    parser.add_argument("--output", "-o", help="Output path for database file. When modifying an existing DB, this specifies a new save location (if omitted, the original file is overwritten).")
    parser.add_argument("--workers", "-w", type=int, default=8, help="Number of parallel workers for building.")
    parser.add_argument("--min-ratio", type=float, default=0.05, help="Minimum missing ratio for bins.")
    parser.add_argument("--max-ratio", type=float, default=1.0, help="Maximum missing ratio for bins.")
    parser.add_argument("--bins", type=int, default=20, help="Number of missing ratio bins.")
    parser.add_argument("--patterns-per-bin", type=int, default=100, help="Target number of patterns per bin.")
    parser.add_argument("--generate-synthetic", action="store_true", help="Generate synthetic patterns to fill gaps. Use with --index-file to create a new DB with synthetic patterns, or with --load-db to add them to an existing DB.")
    parser.add_argument("--synthetic-types", nargs='+', default=None, choices=['random', 'clustered', 'streaky', 'seasonal'], help="Specify which types of synthetic patterns to generate. If not provided, all types are generated.")
    parser.add_argument("--visualize", action="store_true", help="Generate a visualization of patterns from the database.")
    parser.add_argument("--visualize-output", type=str, default="missing_patterns.png", help="Output file for pattern visualization.")
    parser.add_argument("--visualize-samples", type=int, default=4, help="Number of sample patterns to visualize per bin.")
    
    args = parser.parse_args()

    # Create configuration from args
    config = {
        'min_missing_ratio': args.min_ratio,
        'max_missing_ratio': args.max_ratio,
        'num_bins': args.bins,
        'patterns_per_bin': args.patterns_per_bin,
        'collect_real_patterns': True,
        'generate_synthetic_patterns': args.generate_synthetic,
        'pattern_augmentation': True,
        'synthetic_types': args.synthetic_types
    }
    
    db = None

    if args.index_file:
        if not args.output:
            parser.error("--output is required when building from --index-file")
        
        # Load index file
        with open(args.index_file, 'r') as f:
            index_data = json.load(f)
        
        # Extract missing samples by ratio bins
        missing_samples_by_ratio = {}
        if 'missing_samples_by_ratio' in index_data:
            for bin_idx_str, samples in index_data['missing_samples_by_ratio'].items():
                missing_samples_by_ratio[int(bin_idx_str)] = samples
        
        # Build database
        db = build_missing_database(missing_samples_by_ratio, args.output, config, args.workers)
    
        print(f"\nMissing pattern database built successfully!")
        print(f"Output saved to: {args.output}")

    elif args.load_db:
        db = MissingPatternDatabase(config)
        try:
            db.load(args.load_db)
        except FileNotFoundError:
            logger.error(f"Database file not found at {args.load_db}")
            return

        if args.generate_synthetic:
            print(f"Adding synthetic patterns to loaded database: {args.load_db}")
            synthetic_types = config.get('synthetic_types')
            db.generate_synthetic_patterns(pattern_types_to_generate=synthetic_types)
            
            output_path = args.output if args.output else args.load_db
            if args.output:
                print(f"Saving updated database to new file: {output_path}")
            else:
                print(f"Overwriting original database file: {output_path}")

            db.save(output_path)
    else:
        parser.error("Either --index-file (to build) or --load-db (to inspect) must be provided.")

    if db and args.visualize:
        if not MATPLOTLIB_AVAILABLE:
            logger.error("Cannot generate visualization because matplotlib is not installed.")
            logger.error("Please install it: pip install matplotlib")
        else:
            print(f"Generating pattern visualization with {args.visualize_samples} samples per bin...")
            visualize_patterns(db, num_per_bin=args.visualize_samples, output_file=args.visualize_output)
            print(f"Visualization saved to {args.visualize_output}")

    if db:
        # Print final statistics
        stats = db.get_pattern_statistics()
        print(f"\nDatabase Statistics:")
        print(f"Total patterns: {stats['total_patterns']}")
        print(f"Pattern types: {stats.get('pattern_types', 'N/A')}")
        if 'missing_ratio_distribution' in stats:
            dist = stats['missing_ratio_distribution']
            print(f"Missing ratio distribution: mean={dist['mean']:.3f}, std={dist['std']:.3f}")

if __name__ == "__main__":
    main()