#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=16
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_gpu
#SBATCH --qos=gpu
#SBATCH --mem-per-cpu=10G
#SBATCH --gres=gpu:1
#SBATCH --time=6:00:00
#SBATCH --output=logs/train_v20_glad.out
#SBATCH --mail-type=END
#SBATCH --mail-user=<EMAIL>

# Optimized Training Script for Water Body Reconstruction
# Initialize Conda
source /home/<USER>/miniconda/bin/activate
# Activate the geospatial environment
conda activate water

#%Module load cuda/11.8

nvidia-smi

# Detect available GPUs and set NUM_GPUS accordingly
AVAILABLE_GPUS=$(nvidia-smi --list-gpus | wc -l)
echo "Available GPUs: ${AVAILABLE_GPUS}"

# Data paths (modify as needed)
INDEX_FILE="/fossfs/xiaozhen/Sample/GLAD/samples.json"
MISSING_DB="/fossfs/xiaozhen/Sample/GLAD/missing_db.npz"

# Configuration file
CONFIG="configs/config.yaml"

# Launch distributed training using torchrun
echo "Config: ${CONFIG}"

# PyTorch 2.0+ distributed launch (multi-GPU)
NUM_GPUS=${NUM_GPUS:-${AVAILABLE_GPUS}}  # Use available GPUs if not set
torchrun \
    --nproc_per_node=${NUM_GPUS} \
    --nnodes=1 \
    --node_rank=0 \
    --master_addr=localhost \
    --master_port=29500 \
    model_glad/train_glad.py \
    --config ${CONFIG} \
    --index_file ${INDEX_FILE} \
    --missing_db ${MISSING_DB} \
    # --resume /scr/u/xiaoz/Results/waternet/best.pt

echo "Training completed!"