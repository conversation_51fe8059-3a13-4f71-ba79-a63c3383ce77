# TensorBoard 使用指南

## 1. 快速启动

### 方法一：使用提供的脚本
```bash
# 使用默认设置启动
./start_tensorboard.sh

# 指定日志目录和端口
./start_tensorboard.sh Results/logs 6007
```

### 方法二：手动启动
```bash
# 激活conda环境
source /mnt/storage/xiaozhen/miniconda3/envs/geoai/bin/activate

# 启动TensorBoard
tensorboard --logdir=Results/logs --port=6006 --host=0.0.0.0
```

## 2. 访问TensorBoard

启动后，在浏览器中访问：
- 本地：`http://localhost:6006`
- 远程：`http://服务器IP:6006`

## 3. 界面功能详解

### Scalars 标签页（最重要）
显示训练过程中的数值指标：

#### 训练指标
- `train/loss`: 训练损失曲线
- `lr/learning_rate`: 学习率变化

#### 验证指标  
- `val/loss`: 验证损失
- `val/accuracy_0_4_0_6`: 验证准确率（0.4-0.6范围）
- `val/f1_score_0_4_0_6`: 验证F1分数
- `val/iou_0_4_0_6`: 验证IoU

### 图表操作技巧
- **缩放**：鼠标滚轮
- **平移**：拖拽图表
- **重置视图**：双击图表
- **平滑曲线**：调整左侧Smoothing滑块（推荐0.3-0.6）
- **选择指标**：左侧复选框选择要显示的曲线

## 4. 实用功能

### 比较不同实验
- 在logdir中放置多个实验的日志
- TensorBoard会自动显示所有实验的对比

### 下载数据
- 点击左下角的下载按钮
- 可以下载CSV格式的数据进行进一步分析

### 实时更新
- TensorBoard会自动刷新显示最新数据
- 默认每30秒更新一次

## 5. 常见问题

### 问题1：端口被占用
```bash
# 查看端口占用
lsof -i :6006

# 使用其他端口
tensorboard --logdir=Results/logs --port=6007
```

### 问题2：无法访问（远程服务器）
确保启动时使用了 `--host=0.0.0.0`：
```bash
tensorboard --logdir=Results/logs --port=6006 --host=0.0.0.0
```

### 问题3：日志目录为空
- 确保训练已经开始
- 检查配置文件中 `use_tensorboard: true`
- 查看训练日志是否有错误

## 6. 高级用法

### 指定多个日志目录
```bash
tensorboard --logdir=name1:path1,name2:path2
```

### 设置更新频率
```bash
tensorboard --logdir=Results/logs --reload_interval=1
```

### 后台运行
```bash
nohup tensorboard --logdir=Results/logs --port=6006 --host=0.0.0.0 > tensorboard.log 2>&1 &
```

## 7. 监控建议

### 关键指标监控
1. **训练损失**：应该稳定下降
2. **验证损失**：不应该持续上升（过拟合信号）
3. **学习率**：确认调度器正常工作
4. **验证指标**：IoU和F1分数的提升趋势

### 异常情况识别
- 损失突然跳跃：可能是学习率过大
- 验证损失上升：可能过拟合，需要早停
- 指标不收敛：检查数据或模型配置

## 8. 与训练脚本的集成

你的训练脚本已经正确集成了TensorBoard：
- 每5000步记录一次指标
- 自动保存到 `Results/logs/实验名称/` 目录
- 记录训练损失、验证损失、验证指标和学习率
