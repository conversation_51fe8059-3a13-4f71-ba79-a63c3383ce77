import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import logging
from model.loss import compute_dynamic_degree

logger = logging.getLogger(__name__)

class MemoryEfficientDecoder(nn.Module):
    """
    Memory-efficient decoder for temporal inpainting with classification learning
    Uses progressive upsampling and skip connections
    Outputs classification logits (2 channels: background and water)
    """
    
    def __init__(self, 
                 in_channels,
                 img_size=256,
                 patch_size=8,
                 num_classes=2,  # Number of classes (background and water)
                 use_checkpoint=True):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.use_checkpoint = use_checkpoint
        
        # Progressive upsampling with skip connections
        self.upsample_blocks = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_channels, in_channels // 2, 3, padding=1),
                nn.GroupNorm(16, in_channels // 2),
                nn.GELU(),
                nn.ConvTranspose2d(in_channels // 2, in_channels // 2, 4, 2, 1),
                nn.GroupNorm(16, in_channels // 2),
                nn.GELU()
            ),
            nn.Sequential(
                nn.Conv2d(in_channels // 2, in_channels // 4, 3, padding=1),
                nn.GroupNorm(8, in_channels // 4),
                nn.GELU(),
                nn.ConvTranspose2d(in_channels // 4, in_channels // 4, 4, 2, 1),
                nn.GroupNorm(8, in_channels // 4),
                nn.GELU()
            ),
            nn.Sequential(
                nn.Conv2d(in_channels // 4, in_channels // 8, 3, padding=1),
                nn.GroupNorm(4, in_channels // 8),
                nn.GELU(),
                nn.ConvTranspose2d(in_channels // 8, in_channels // 8, 4, 2, 1),
                nn.GroupNorm(4, in_channels // 8),
                nn.GELU()
            )
        ])
        
        # Skip connection gates for dynamic region attention
        self.skip_gates = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_channels // (2 ** (i+1)), in_channels // (2 ** (i+1)), 3, padding=1),
                nn.GroupNorm(8 // (2 ** i), in_channels // (2 ** (i+1))),
                nn.GELU(),
                nn.Conv2d(in_channels // (2 ** (i+1)), 1, 1),
                nn.Sigmoid()
            )
            for i in range(3)
        ])
        
        # Channel adaptation layers for skip connections
        self.skip_channel_adapters = nn.ModuleList([
            nn.Conv2d(in_channels // (2 ** i), in_channels // (2 ** (i+1)), 1)
            for i in range(3)
        ])
        
        # Final prediction layers - output classification logits (2 channels: background and water)
        self.final_conv = nn.Sequential(
            nn.Conv2d(in_channels // 8, in_channels // 16, 3, padding=1),
            nn.GroupNorm(4, in_channels // 16),
            nn.GELU(),
            nn.Conv2d(in_channels // 16, num_classes, 1)  # Output num_classes channels for classification logits
        )
        
        # Confidence estimation (raw logits; Sigmoid removed – will be applied in loss when needed)
        self.confidence = nn.Sequential(
            nn.Conv2d(in_channels // 8, in_channels // 16, 3, padding=1),
            nn.GroupNorm(4, in_channels // 16),
            nn.GELU(),
            nn.Conv2d(in_channels // 16, 1, 1)
        )
    
    def _forward_impl(self, x, features=None, target_t_ratio=0.5, water_frequency=None):
        """Implementation of forward pass with dynamic skip connections using temporal max pooling"""
        # Note: target_t_ratio is kept for API compatibility but no longer used
        # Temporal information is now aggregated via max pooling across time dimension
            
        # Process upsampling blocks
        for i, upsample in enumerate(self.upsample_blocks):
            x = upsample(x)
            
            # Add skip connections if available
            if features is not None and i < len(features) and i < len(self.skip_gates):
                skip = features[-(i+1)]
                B_s, L_s, C_s = skip.shape
                
                # Calculate patch grid size
                H_p = W_p = self.img_size // self.patch_size
                num_patches = H_p * W_p
                
                # Quick path check: if dimensions are clearly incompatible, skip directly
                if num_patches <= 0 or L_s < num_patches:
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug(f"Skip connection at stage {i}: incompatible dimensions (L_s={L_s}, num_patches={num_patches})")
                    continue
                
                # Normalize skip feature length to ensure divisibility
                T_s = max(1, L_s // num_patches)
                L_s_exact = T_s * num_patches
                
                # If length doesn't match, crop to integer multiple
                if L_s != L_s_exact:
                    skip = skip[:, :L_s_exact, :]
                    
                # Reshape to spatiotemporal sequence and extract temporal max features
                skip_reshaped = skip.reshape(B_s, T_s, H_p, W_p, C_s)
                # Use max pooling across temporal dimension to aggregate temporal information
                skip_spatial = skip_reshaped.max(dim=1)[0]  # (B, H_p, W_p, C_s) - max across time
                skip_spatial = skip_spatial.permute(0, 3, 1, 2)  # (B, C_s, H_p, W_p)
                
                # Upsample to current feature size
                target_size = x.shape[2:]
                skip_upsampled = F.interpolate(
                    skip_spatial, 
                    size=target_size, 
                    mode='bilinear', 
                    align_corners=False
                )
                
                # Only continue processing if channel dimensions match
                if skip_upsampled.shape[1] == x.shape[1]:
                    logger.debug(f"Skip connection at stage {i}: channel dimensions match ({skip_upsampled.shape[1]} == {x.shape[1]})")
                    # Apply dynamic weighting
                    skip_gate = self.skip_gates[i](skip_upsampled)  # (B, 1, H, W)
                    
                    # If water frequency information is provided, use it to adjust gating
                    if water_frequency is not None:
                        # Resize water frequency to current resolution and convert to single-channel dynamic weight
                        freq_resized = F.interpolate(
                            water_frequency.unsqueeze(1),  # (B, 1, H, W)
                            size=target_size,
                            mode='bilinear',
                            align_corners=False
                        )
                        dynamic_score = compute_dynamic_degree(freq_resized.squeeze(1))  # (B, H, W)
                        # Apply frequency-based modulation to the skip gate
                        skip_gate = skip_gate * (0.5 + 0.5 * dynamic_score)
                    
                    # Apply weighted skip connection
                    x = x + skip_gate * skip_upsampled
        
        # Final predictions
        logits = self.final_conv(x)
        confidence = self.confidence(x)
        
        # Numerical stability safeguard: clamp logits to a reasonable range
        # to avoid overflow in subsequent softmax / loss computations.
        # This prevents sporadic NaN/Inf that were observed during training.
        logits = torch.nan_to_num(logits, nan=0.0, posinf=10.0, neginf=-10.0)
        logits = torch.clamp(logits, min=-10.0, max=10.0)
        
        # Ensure output size matches target size
        if logits.shape[-1] != self.img_size:
            logits = F.interpolate(logits, size=(self.img_size, self.img_size),
                                 mode='bilinear', align_corners=False)
            confidence = F.interpolate(confidence, size=(self.img_size, self.img_size),
                                     mode='bilinear', align_corners=False)
        
        return logits, confidence
    
    def forward(self, x, features=None, target_t_ratio=0.5, water_frequency=None):
        """Forward with optional gradient checkpointing"""
        if self.use_checkpoint and self.training:
            # Use non-reentrant checkpointing to avoid nested backward issues
            return torch.utils.checkpoint.checkpoint(
                self._forward_impl, x, features, target_t_ratio, water_frequency, use_reentrant=False
            )
        else:
            return self._forward_impl(x, features, target_t_ratio, water_frequency)

class PerfectUNetDecoder(nn.Module):
    """
    Perfect U-Net style decoder with guaranteed channel and spatial matching
    Each decoder layer perfectly matches with corresponding encoder layer
    """
    
    def __init__(self, 
                 in_channels,
                 img_size=256,
                 patch_size=8,
                 num_classes=2,
                 use_checkpoint=True):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.use_checkpoint = use_checkpoint
        
        # Calculate channel dimensions for each layer based on encoder output
        # Encoder produces: [128, 256, 512, 1024] from shallow to deep
        # Decoder needs to process: [1024, 512, 256, 128] from deep to shallow
        self.channel_dims = [in_channels // (2**i) for i in range(4)]  # [1024, 512, 256, 128]
                
        # Perfect U-Net style decoder blocks with spatial upsampling
        # Each block processes one level and upsamples to match the next skip connection
        self.decoder_blocks = nn.ModuleList([
            # Decoder Block 0: 1024 -> 512 (4x4 -> 8x8, matches encoder layer 2: 512 at 8x8)
            nn.Sequential(
                nn.Conv2d(self.channel_dims[0], self.channel_dims[1], 3, padding=1),  # 1024 -> 512
                nn.GroupNorm(16, self.channel_dims[1]),
                nn.GELU(),
                nn.ConvTranspose2d(self.channel_dims[1], self.channel_dims[1], 4, 2, 1),  # Spatial upsampling
                nn.GroupNorm(16, self.channel_dims[1]),
                nn.GELU(),
                nn.Conv2d(self.channel_dims[1] * 2, self.channel_dims[1], 3, padding=1),  # *2 for skip connection
                nn.GroupNorm(16, self.channel_dims[1]),
                nn.GELU()
            ),
            # Decoder Block 1: 512 -> 256 (8x8 -> 16x16, matches encoder layer 1: 256 at 16x16)
            nn.Sequential(
                nn.Conv2d(self.channel_dims[1], self.channel_dims[2], 3, padding=1),  # 512 -> 256
                nn.GroupNorm(8, self.channel_dims[2]),
                nn.GELU(),
                nn.ConvTranspose2d(self.channel_dims[2], self.channel_dims[2], 4, 2, 1),  # Spatial upsampling
                nn.GroupNorm(8, self.channel_dims[2]),
                nn.GELU(),
                nn.Conv2d(self.channel_dims[2] * 2, self.channel_dims[2], 3, padding=1),  # *2 for skip connection
                nn.GroupNorm(8, self.channel_dims[2]),
                nn.GELU()
            ),
            # Decoder Block 2: 256 -> 128 (16x16 -> 32x32, matches encoder layer 0: 128 at 32x32)
            nn.Sequential(
                nn.Conv2d(self.channel_dims[2], self.channel_dims[3], 3, padding=1),  # 256 -> 128
                nn.GroupNorm(4, self.channel_dims[3]),
                nn.GELU(),
                nn.ConvTranspose2d(self.channel_dims[3], self.channel_dims[3], 4, 2, 1),  # Spatial upsampling
                nn.GroupNorm(4, self.channel_dims[3]),
                nn.GELU(),
                nn.Conv2d(self.channel_dims[3] * 2, self.channel_dims[3], 3, padding=1),  # *2 for skip connection
                nn.GroupNorm(4, self.channel_dims[3]),
                nn.GELU()
            )
        ])
        
        # Dynamic skip connection gates - match the expected skip feature channels
        # Skip features come in order: [128, 256, 512] (shallow to deep)
        # But we process them in reverse order: [512, 256, 128] (deep to shallow)
        self.skip_gates = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(self.channel_dims[i+1], self.channel_dims[i+1], 3, padding=1),
                nn.GroupNorm(8 // (2 ** i), self.channel_dims[i+1]),
                nn.GELU(),
                nn.Conv2d(self.channel_dims[i+1], 1, 1),
                nn.Sigmoid()
            )
            for i in range(3)
        ])
        
        # Progressive upsampling layers with residual connections
        # 32x32 -> 64x64 -> 128x128 -> 256x256
        self.progressive_upsample = nn.ModuleList([
            # 32x32 -> 64x64
            nn.Sequential(
                nn.ConvTranspose2d(self.channel_dims[3], 64, 4, 2, 1),  # 32x32 -> 64x64
                nn.GroupNorm(8, 64),
                nn.GELU(),
                nn.Conv2d(64, 64, 3, padding=1),
                nn.GroupNorm(8, 64),
                nn.GELU()
            ),
            # 64x64 -> 128x128
            nn.Sequential(
                nn.ConvTranspose2d(64, 32, 4, 2, 1),  # 64x64 -> 128x128
                nn.GroupNorm(4, 32),
                nn.GELU(),
                nn.Conv2d(32, 32, 3, padding=1),
                nn.GroupNorm(4, 32),
                nn.GELU()
            ),
            # 128x128 -> 256x256
            nn.Sequential(
                nn.ConvTranspose2d(32, 16, 4, 2, 1),  # 128x128 -> 256x256
                nn.GroupNorm(2, 16),
                nn.GELU(),
                nn.Conv2d(16, 16, 3, padding=1),
                nn.GroupNorm(2, 16),
                nn.GELU()
            )
        ])
        
        # Residual connection layers for each upsampling stage
        self.residual_layers = nn.ModuleList([
            nn.Conv2d(self.channel_dims[3], 64, 1),  # 32x32 -> 64x64 residual
            nn.Conv2d(64, 32, 1),                    # 64x64 -> 128x128 residual
            nn.Conv2d(32, 16, 1)                     # 128x128 -> 256x256 residual
        ])
        
        # Final prediction layers
        self.final_conv = nn.Sequential(
            nn.Conv2d(16, 8, 3, padding=1),
            nn.GroupNorm(4, 8),
            nn.GELU(),
            nn.Conv2d(8, num_classes, 1)
        )
        
        self.confidence = nn.Sequential(
            nn.Conv2d(16, 8, 3, padding=1),
            nn.GroupNorm(4, 8),
            nn.GELU(),
            nn.Conv2d(8, 1, 1)
        )
    
    def _forward_impl(self, x, features=None, target_t_ratio=0.5, water_frequency=None):
        """
        Perfect U-Net style forward pass with guaranteed matching
        Args:
            x: (B, C, H, W) - input feature from encoder bottleneck (preprocessed)
            features: List of preprocessed encoder features [(B, C, H, W), ...] from shallow to deep
            target_t_ratio: Kept for API compatibility (not used in this implementation)
            water_frequency: Water frequency map for dynamic gating (not used in this implementation)
        """
        # Note: target_t_ratio and water_frequency are kept for API compatibility but not used
        # All features are already preprocessed to spatial format (B, C, H, W)
        # x is the bottleneck feature (deepest layer)
        # features are skip features from shallow to deep layers
                
        # Process each decoder block
        for i, decoder_block in enumerate(self.decoder_blocks):
            layers = list(decoder_block.children())
            # Channel reduction和空间上采样
            x = layers[0](x)
            x = layers[1](x)
            x = layers[2](x)
            x = layers[3](x)
            x = layers[4](x)
            x = layers[5](x)
            
            # Get corresponding encoder feature for skip connection
            # features come in order [shallow, ..., deep], but we need them in reverse order
            # So we access features[-(i+1)] to get the matching feature
            if features is not None and i < len(features):
                skip_feature = features[-(i+1)]  # Reverse order: deep -> shallow
                
                # 不再需要插值，直接用
                expected_channels = self.channel_dims[i + 1]

                assert x.shape[1] == skip_feature.shape[1] == expected_channels, \
                    f"Channel mismatch at block {i}: x={x.shape[1]}, skip={skip_feature.shape[1]}, expected={expected_channels}"
                
                # Apply dynamic weighting
                skip_gate = self.skip_gates[i](skip_feature)
                
                # Apply weighted skip connection
                x = x + skip_gate * skip_feature
                
                # Concatenate for U-Net style fusion
                x = torch.cat([x, skip_feature], dim=1)
                
                # Process concatenated features
                x = layers[6](x)
                x = layers[7](x)
                x = layers[8](x)
            else:
                # No features provided, skip the concatenation layers
                # layers[6], [7], [8] are designed for concatenated features
                # When no skip features, we keep the current x as is
                pass
        
        # Progressive upsampling with residual connections
        # Start from 32x32 (x shape after decoder blocks)
        current_x = x
        
        for i, (upsample_layer, residual_layer) in enumerate(zip(self.progressive_upsample, self.residual_layers)):
            # Store input for residual connection
            residual_input = current_x
            
            # Apply upsampling
            current_x = upsample_layer(current_x)
            
            # Prepare residual connection (upsample the residual input to match current size)
            residual_upsampled = F.interpolate(residual_input, size=current_x.shape[2:], 
                                             mode='bilinear', align_corners=False)
            residual_processed = residual_layer(residual_upsampled)
            
            # Add residual connection
            current_x = current_x + residual_processed
        
        # Final predictions
        logits = self.final_conv(current_x)
        confidence = self.confidence(current_x)
                
        # Numerical stability
        logits = torch.nan_to_num(logits, nan=0.0, posinf=10.0, neginf=-10.0)
        logits = torch.clamp(logits, min=-10.0, max=10.0)
        
        return logits, confidence
    
    def forward(self, x, features=None, target_t_ratio=0.5, water_frequency=None):
        """Forward with optional gradient checkpointing"""
        if self.use_checkpoint and self.training:
            return torch.utils.checkpoint.checkpoint(
                self._forward_impl, x, features, target_t_ratio, water_frequency, use_reentrant=False
            )
        else:
            return self._forward_impl(x, features, target_t_ratio, water_frequency)
