import torch
import torch.nn as nn
from model.loss import compute_dynamic_degree

class AttentionGuidedPyramidSampling(nn.Module):
    """
    Simplified attention-guided pyramid temporal sampling - learnable frame selection
    
    Features:
    1. Fixed key frame counts: directly specify frame count for each scale
    2. Learnable frame selection: learn weights for three variables through affine transform layers
    3. Simplified feature extraction: only use original input, no frequency channel concatenation
    4. Maintain pyramid structure: multi-scale sampling
    """
    
    def __init__(self, num_frames=120, num_scales=3, frame_counts=[24, 12, 8], feature_dim=32):
        """
        Args:
            num_frames: Original frame count
            num_scales: Number of pyramid scales (default 3: short-term, medium-term, long-term)
            frame_counts: Fixed frame count list for each scale, default [24, 12, 8]
            feature_dim: Feature dimension, default 32
        """
        super().__init__()
        self.num_frames = num_frames
        self.num_scales = num_scales
        
        # Use fixed frame counts instead of sampling rates
        self.frame_counts = frame_counts
        if len(self.frame_counts) < num_scales:
            # If provided frame counts are insufficient, extend with last value
            self.frame_counts.extend([self.frame_counts[-1]] * (num_scales - len(self.frame_counts)))
        
        # Define temporal window strategy: short-term, medium-term, long-term
        # Short-term: frames near target frame (local details)
        # Medium-term: medium temporal range frames (medium changes)
        # Long-term: key frames from entire sequence (global trends)
        self.time_windows = {
            0: {'name': 'short_term', 'window_size': 0.2, 'focus': 'local'},    # Short-term: 20% of sequence length
            1: {'name': 'medium_term', 'window_size': 0.5, 'focus': 'medium'},  # Medium-term: 50% of sequence length
            2: {'name': 'long_term', 'window_size': 1.0, 'focus': 'global'}     # Long-term: 100% of sequence length
        }
        
        # Learnable affine transform layers - learn weights for three variables
        # Input: [content similarity, temporal distance, dynamic degree mean]
        self.affine_transform = nn.Sequential(
            nn.Linear(3, 16),
            nn.ReLU(),
            nn.Linear(16, 8),
            nn.ReLU(),
            nn.Linear(8, 1),
            nn.Sigmoid()  # Ensure output in [0,1] range
        )
    
    def compute_importance_scores(self, x, center_frame_idx, water_frequency=None):
        """
        Compute importance scores for each frame based on attention and water frequency
        """
        B, T, C, H, W = x.shape
        device = x.device
        
        # 确保输入为float32
        x = x.float()
        
        # 1. Temporal attention scores
        # Compute frame differences from center frame - vectorized version
        batch_indices = torch.arange(B, device=device)
        center_frame = x[batch_indices, center_frame_idx].unsqueeze(1)  # (B, 1, C, H, W)
        frame_diffs = torch.abs(x - center_frame)  # (B, T, C, H, W)
        
        # Average across channels and spatial dimensions
        temporal_scores = frame_diffs.mean(dim=(2, 3, 4))  # (B, T)
        
        # 2. Spatial attention scores (edge detection) - vectorized version
        # Compute edge detection for all frames at once
        # Compute x-direction gradient: (B, T, C, H, W-1)
        grad_x = x[:, :, :, :, 1:] - x[:, :, :, :, :-1]
        # Compute y-direction gradient: (B, T, C, H-1, W)
        grad_y = x[:, :, :, 1:, :] - x[:, :, :, :-1, :]
        # Compute edge magnitude: (B, T, C, H-1, W-1)
        edge_magnitude = torch.sqrt(grad_x[:, :, :, :-1, :]**2 + grad_y[:, :, :, :, :-1]**2 + 1e-8)
        # Average across all dimensions: (B, T)
        spatial_scores = edge_magnitude.mean(dim=(2, 3, 4))
        
        # 3. Water frequency-based dynamic scores
        if water_frequency is not None:
            # 确保water_frequency为float32
            water_frequency = water_frequency.float()
            # Compute dynamic degree - use function from loss.py
            dynamic_degree = compute_dynamic_degree(water_frequency)  # (B, H, W)
            # Take mean as scalar score
            dynamic_scores = dynamic_degree.mean(dim=(1, 2))  # (B,)
            # Expand to match temporal dimension
            dynamic_scores = dynamic_scores.unsqueeze(1).expand(B, T)  # (B, T)
        else:
            # If no frequency information, use neutral value
            dynamic_scores = torch.ones((B, T), device=device, dtype=torch.float32) * 0.5
        
        # 4. Ensure target frame gets highest score
        target_mask = torch.zeros_like(temporal_scores)
        target_mask.scatter_(1, center_frame_idx.unsqueeze(1), 1.0)
        temporal_scores = torch.max(temporal_scores, target_mask)
        spatial_scores = torch.max(spatial_scores, target_mask)
        dynamic_scores = torch.max(dynamic_scores, target_mask)
        
        # 5. Combine three scores through learnable affine transform layer
        # Concatenate three scores: (B, T, 3)
        combined_features = torch.stack([temporal_scores, spatial_scores, dynamic_scores], dim=2)
        
        # Pass through affine transform layer
        importance_scores = self.affine_transform(combined_features).squeeze(2)  # (B, T)
        
        return importance_scores
    
    def compute_scale_specific_scores(self, x, center_frame_idx, water_frequency=None):
        """
        Compute scale-specific scores - vectorized version
        
        Args:
            x: (B, T, C, H, W) - input sequence
            center_frame_idx: (B,) - target frame index
            water_frequency: (B, H, W) - water frequency map (optional)
            
        Returns:
            scale_scores: Dict[int, torch.Tensor] - scores for each scale (B, T)
        """
        B, T, C, H, W = x.shape
        device = x.device
        
        # Compute base importance scores
        base_scores = self.compute_importance_scores(x, center_frame_idx, water_frequency)  # (B, T)
        
        # Batch compute scores for all scales - vectorized version
        scale_scores = {}
        
        # Batch get configurations for all scales
        scale_configs = []
        for scale_idx in range(self.num_scales):
            window_config = self.time_windows.get(scale_idx, self.time_windows[0])
            scale_configs.append({
                'window_size': window_config['window_size'],
                'focus': window_config['focus']
            })
        
        # Batch compute temporal distance weights
        time_indices = torch.arange(T, device=device).unsqueeze(0)  # (1, T)
        center_indices = center_frame_idx.unsqueeze(1)  # (B, 1)
        time_distances = torch.abs(time_indices - center_indices)  # (B, T)
        
        # Batch compute weights for all scales
        for scale_idx in range(self.num_scales):
            config = scale_configs[scale_idx]
            window_size = config['window_size']
            focus = config['focus']
            
            # Compute temporal window
            window_frames = int(T * window_size)
            half_window = window_frames // 2
            
            if focus == 'local':
                # Short-term: emphasize frames near target frame - vectorized version
                sigma = window_frames / 6.0  # Gaussian distribution standard deviation
                time_weights = torch.exp(-(time_distances.float() ** 2) / (2 * sigma ** 2))
                
            elif focus == 'medium':
                # Medium-term: balance local and global information - vectorized version
                max_distance = window_frames // 2
                time_weights = torch.clamp(1.0 - time_distances.float() / max_distance, 0.0, 1.0)
                
            else:  # focus == 'global'
                # Long-term: focus on key frames from entire sequence - vectorized version
                time_weights = torch.ones_like(time_distances.float())
                # Give slightly higher weight to frames near target frame
                target_bonus = torch.exp(-time_distances.float() / (T * 0.1))
                time_weights = time_weights + 0.3 * target_bonus
            
            # Apply temporal weights to base scores
            weighted_scores = base_scores * time_weights
            
            # Ensure target frame always gets highest score - vectorized version
            target_mask = torch.zeros_like(weighted_scores)
            target_mask.scatter_(1, center_frame_idx.unsqueeze(1), 1.0)
            weighted_scores = torch.max(weighted_scores, target_mask)
            
            scale_scores[scale_idx] = weighted_scores
        
        return scale_scores
    
    def generate_indices(self, x, center_frame_idx, water_frequency=None):
        """
        Generate sampling indices based on hierarchical temporal strategy, avoiding duplicate sampling
        
        Args:
            x: (B, T, C, H, W) - input sequence
            center_frame_idx: (B,) - target frame index
            water_frequency: (B, H, W) - water frequency map (optional)
            
        Returns:
            List of sampling indices for each scale, each element shape (B, T_scale)
        """
        B, T, C, H, W = x.shape
        device = x.device
        
        # Compute scale-specific scores
        scale_scores = self.compute_scale_specific_scores(x, center_frame_idx, water_frequency)
        
        # Generate sampling indices for each scale
        scale_indices = []
        used_indices = set()  # Track used indices to avoid duplicate sampling
        
        for scale_idx in range(self.num_scales):
            # Use fixed frame count for current scale
            num_samples = min(self.frame_counts[scale_idx], T)
            
            if num_samples == T:
                # If all frames are needed, add all indices directly
                scale_indices.append(torch.arange(T, device=device).unsqueeze(0).expand(B, -1))
                continue
            
            # Get current scale scores
            current_scores = scale_scores[scale_idx]
            
            # Create batch target frame mask: (B, T)
            target_mask = torch.zeros((B, T), dtype=torch.bool, device=device)
            target_mask.scatter_(1, center_frame_idx.unsqueeze(1), True)
            
            # Create result tensor, initialized to -1
            result_indices = torch.full((B, num_samples), -1, device=device, dtype=torch.long)
            
            # First store target frame index at first position
            result_indices[:, 0] = center_frame_idx
            
            # Select remaining frames
            if num_samples > 1:
                # Mask target frame and used frame scores
                remaining_scores = current_scores.clone()
                remaining_scores.masked_fill_(target_mask, -1.0)
                
                # Mask frames already selected by previous scales - vectorized version
                if scale_idx > 0:
                    # Create used mask for all previous scales at once
                    used_mask = torch.zeros_like(remaining_scores, dtype=torch.bool)
                    
                    for prev_scale_idx in range(scale_idx):
                        if prev_scale_idx < len(scale_indices):
                            prev_indices = scale_indices[prev_scale_idx]  # (B, T_prev)
                            
                            # Vectorized creation of used frame mask
                            # Use scatter_ operation to batch set mask
                            batch_indices = torch.arange(B, device=device).unsqueeze(1).expand(-1, prev_indices.shape[1])
                            used_mask.scatter_(1, prev_indices, True)
                    
                    remaining_scores.masked_fill_(used_mask, -1.0)
                
                # Select different sampling strategies based on scale type
                window_config = self.time_windows.get(scale_idx, self.time_windows[0])
                focus = window_config['focus']
                
                if focus == 'local':
                    # Short-term: dense sampling near target frame - vectorized version
                    # Compute temporal window
                    window_size = int(T * 0.2)  # 20% of sequence length
                    half_window = window_size // 2
                    
                    # Create local window mask - vectorized version
                    # Create temporal index tensor: (B, T)
                    time_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, -1)
                    
                    # Compute window boundaries for each batch: (B,)
                    start_indices = torch.clamp(center_frame_idx - half_window, 0, T-1)
                    end_indices = torch.clamp(center_frame_idx + half_window, 0, T-1)
                    
                    # Create window mask: (B, T)
                    local_mask = (time_indices >= start_indices.unsqueeze(1)) & (time_indices <= end_indices.unsqueeze(1))
                    
                    # Only sample within local window
                    remaining_scores.masked_fill_(~local_mask, -1.0)
                    
                    # Use TopK sampling
                    _, topk_indices = torch.topk(remaining_scores, k=min(num_samples-1, T-1), dim=1)
                    result_indices[:, 1:num_samples] = topk_indices[:, :num_samples-1]
                    
                elif focus == 'medium':
                    # Medium-term: sample in medium temporal range, avoid overlap with short-term - vectorized version
                    # Compute medium temporal window
                    window_size = int(T * 0.5)  # 50% of sequence length
                    half_window = window_size // 2
                    
                    # Create medium window mask, exclude short-term window - vectorized version
                    # Create temporal index tensor: (B, T)
                    time_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, -1)
                    
                    # Compute window boundaries for each batch: (B,)
                    start_indices = torch.clamp(center_frame_idx - half_window, 0, T-1)
                    end_indices = torch.clamp(center_frame_idx + half_window, 0, T-1)
                    
                    # Create medium window mask: (B, T)
                    medium_mask = (time_indices >= start_indices.unsqueeze(1)) & (time_indices <= end_indices.unsqueeze(1))
                    
                    # Exclude short-term window (if exists) - vectorized version
                    if scale_idx > 0:
                        short_window_size = int(T * 0.1)  # Half of short-term window
                        short_start_indices = torch.clamp(center_frame_idx - short_window_size, 0, T-1)
                        short_end_indices = torch.clamp(center_frame_idx + short_window_size, 0, T-1)
                        
                        # Create short-term window mask and exclude
                        short_mask = (time_indices >= short_start_indices.unsqueeze(1)) & (time_indices <= short_end_indices.unsqueeze(1))
                        medium_mask = medium_mask & ~short_mask
                    
                    # Only sample within medium window
                    remaining_scores.masked_fill_(~medium_mask, -1.0)
                    
                    # Use TopK sampling
                    _, topk_indices = torch.topk(remaining_scores, k=min(num_samples-1, T-1), dim=1)
                    result_indices[:, 1:num_samples] = topk_indices[:, :num_samples-1]
                    
                else:  # focus == 'global'
                    # Long-term: select key frames from entire sequence, avoid overlap with first two scales - vectorized version
                    # Create global mask, exclude short-term and medium-term windows
                    global_mask = torch.ones_like(remaining_scores, dtype=torch.bool)
                    
                    # Exclude short-term and medium-term windows - vectorized version
                    # Create temporal index tensor: (B, T)
                    time_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, -1)
                    
                    # Exclude short-term window
                    short_window_size = int(T * 0.2)
                    short_start_indices = torch.clamp(center_frame_idx - short_window_size, 0, T-1)
                    short_end_indices = torch.clamp(center_frame_idx + short_window_size, 0, T-1)
                    short_mask = (time_indices >= short_start_indices.unsqueeze(1)) & (time_indices <= short_end_indices.unsqueeze(1))
                    global_mask = global_mask & ~short_mask
                    
                    # Exclude medium-term window
                    medium_window_size = int(T * 0.5)
                    medium_start_indices = torch.clamp(center_frame_idx - medium_window_size, 0, T-1)
                    medium_end_indices = torch.clamp(center_frame_idx + medium_window_size, 0, T-1)
                    medium_mask = (time_indices >= medium_start_indices.unsqueeze(1)) & (time_indices <= medium_end_indices.unsqueeze(1))
                    global_mask = global_mask & ~medium_mask
                    
                    # Only sample within global window
                    remaining_scores.masked_fill_(~global_mask, -1.0)
                    
                    # Use TopK sampling
                    _, topk_indices = torch.topk(remaining_scores, k=min(num_samples-1, T-1), dim=1)
                    result_indices[:, 1:num_samples] = topk_indices[:, :num_samples-1]
                    
            # Sort indices for each batch
            sorted_indices, _ = torch.sort(result_indices, dim=1)
            
            # Handle possible invalid indices - vectorized version
            invalid_mask = sorted_indices == -1
            if invalid_mask.any():
                # Find last valid index for each batch
                valid_mask = sorted_indices >= 0
                gather_indices = valid_mask.sum(dim=1) - 1
                gather_indices = torch.clamp(gather_indices, 0, num_samples-1)
                
                batch_indices = torch.arange(B, device=device)
                last_valid_idx = sorted_indices[batch_indices, gather_indices]
                
                no_valid_mask = ~valid_mask.any(dim=1)
                last_valid_idx[no_valid_mask] = center_frame_idx[no_valid_mask]
                
                sorted_indices[invalid_mask] = last_valid_idx.unsqueeze(1).expand(-1, num_samples)[invalid_mask]
            
            scale_indices.append(sorted_indices)
        
        return scale_indices
    
    def forward(self, x, center_frame_idx, water_frequency=None):
        """
        Perform multi-scale attention-guided sampling on input sequence
        
        Args:
            x: (B, T, C, H, W) video sequence
            center_frame_idx: (B,) target frame index
            water_frequency: (B, H, W) water frequency map (optional)
            
        Returns:
            List of sampled multi-scale sequences and their temporal indices
        """
        B, T, C, H, W = x.shape
        device = x.device
        
        # 确保输入为float32
        x = x.float()
        
        # Ensure center_frame_idx is tensor of length B
        if center_frame_idx is None:
            center_frame_idx = torch.full((B,), T // 2, device=device, dtype=torch.long)
        elif isinstance(center_frame_idx, (int, float)):
            center_frame_idx = torch.tensor([center_frame_idx] * B, device=device, dtype=torch.long)
        elif center_frame_idx.numel() == 1 and B > 1:
            center_frame_idx = center_frame_idx.expand(B)
        
        # Generate sampling indices
        scale_indices = self.generate_indices(x, center_frame_idx, water_frequency)
        
        # Sample by indices - vectorized version
        multi_scale_x = []
        multi_scale_indices = []  # Store temporal indices for each scale
        
        for indices in scale_indices:
            # indices: (B, T')
            # Handle out-of-range indices
            valid_idx = torch.clamp(indices, 0, T-1)
            
            # Use advanced indexing for batch sampling
            batch_indices = torch.arange(B, device=device).unsqueeze(1).expand(-1, valid_idx.shape[1])
            scale_x = x[batch_indices, valid_idx]  # (B, T', C, H, W)
            
            multi_scale_x.append(scale_x)
            multi_scale_indices.append(valid_idx)  # Store the temporal indices
        
        return multi_scale_x, multi_scale_indices
    
    def visualize_sampling_strategy(self, x, center_frame_idx, water_frequency=None):
        """
        Visualize sampling strategy for different scales
        
        Args:
            x: (B, T, C, H, W) - input sequence
            center_frame_idx: (B,) - target frame index
            water_frequency: (B, H, W) - water frequency map (optional)
            
        Returns:
            visualization_info: dictionary containing sampling strategy information
        """
        B, T, C, H, W = x.shape
        device = x.device
        
        # Compute scores for each scale
        scale_scores = self.compute_scale_specific_scores(x, center_frame_idx, water_frequency)
        
        # Generate sampling indices
        scale_indices = self.generate_indices(x, center_frame_idx, water_frequency)
        
        # Collect visualization information
        viz_info = {
            'sequence_length': T,
            'center_frame_idx': center_frame_idx.cpu().numpy(),
            'scales': {}
        }
        
        for scale_idx in range(self.num_scales):
            window_config = self.time_windows.get(scale_idx, self.time_windows[0])
            
            scale_info = {
                'name': window_config['name'],
                'focus': window_config['focus'],
                'window_size': window_config['window_size'],
                'frame_count': self.frame_counts[scale_idx],
                'scores': scale_scores[scale_idx].cpu().numpy(),
                'selected_indices': scale_indices[scale_idx].cpu().numpy(),
                'time_windows': {}
            }
            
            # Compute temporal window information - vectorized version
            window_frames = int(T * window_config['window_size'])
            half_window = window_frames // 2
            
            # Only process first 3 batches
            num_viz_batches = min(3, B)
            viz_center_indices = center_frame_idx[:num_viz_batches]
            
            # Vectorized computation of window boundaries
            start_indices = torch.clamp(viz_center_indices - half_window, 0, T-1)
            end_indices = torch.clamp(viz_center_indices + half_window, 0, T-1)
            
            for b in range(num_viz_batches):
                scale_info['time_windows'][f'batch_{b}'] = {
                    'center_frame': viz_center_indices[b].item(),
                    'window_start': start_indices[b].item(),
                    'window_end': end_indices[b].item(),
                    'window_size': (end_indices[b] - start_indices[b]).item()
                }
            
            viz_info['scales'][f'scale_{scale_idx}'] = scale_info
        
        return viz_info
