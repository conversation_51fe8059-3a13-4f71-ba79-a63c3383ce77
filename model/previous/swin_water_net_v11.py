"""
SwinWaterNetV11: 融合成熟视频修复技术的水体时序重建网络

基于E2FGVI和FuseFormer等成熟代码，专门针对水体图像的时序修复任务设计。

核心特点：
1. 流引导特征融合 (Flow-Guided Feature Fusion) - 借鉴E2FGVI
2. 细粒度信息融合 (Fine-Grained Information Fusion) - 借鉴FuseFormer
3. 水体感知的上下文聚合 (Water-Aware Context Aggregation)
4. 渐进式特征重建 (Progressive Feature Reconstruction)
5. 地理-时间条件调制 (Geo-Temporal Conditional Modulation)

适配现有的dataset.py和train_v8.py接口。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging
from typing import List, Tuple, Optional, Dict, Any
from einops import rearrange, repeat

from model.decoder_v8 import SwinUNetDecoder

logger = logging.getLogger(__name__)


class FlowGuidedFeatureFusion(nn.Module):
    """
    流引导特征融合模块 - 借鉴E2FGVI的核心思想
    通过光流信息引导特征对齐和融合
    """
    
    def __init__(self, embed_dim: int, num_heads: int = 8):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        # 流估计网络 (简化版本)
        self.flow_estimator = nn.Sequential(
            nn.Conv2d(embed_dim * 2, embed_dim, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(embed_dim, embed_dim // 2, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(embed_dim // 2, 2, 3, 1, 1)  # 输出2通道光流
        )
        
        # 特征对齐网络
        self.feature_align = nn.Sequential(
            nn.Conv2d(embed_dim, embed_dim, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(embed_dim, embed_dim, 3, 1, 1)
        )
        
        # 融合注意力
        self.fusion_attention = nn.MultiheadAttention(
            embed_dim, num_heads, batch_first=True
        )
        
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, target_features: torch.Tensor, context_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            target_features: (B, N, D) 目标帧特征
            context_features: (B, T-1, N, D) 上下文帧特征
        Returns:
            fused_features: (B, N, D) 融合后的特征
        """
        B, N, D = target_features.shape
        _, T_ctx, _, _ = context_features.shape
        
        # 重塑为空间格式进行流估计
        H = W = int(math.sqrt(N))
        target_spatial = target_features.reshape(B, H, W, D).permute(0, 3, 1, 2)  # (B, D, H, W)
        
        aligned_features = []
        for t in range(T_ctx):
            ctx_spatial = context_features[:, t].reshape(B, H, W, D).permute(0, 3, 1, 2)  # (B, D, H, W)
            
            # 估计光流
            flow_input = torch.cat([target_spatial, ctx_spatial], dim=1)  # (B, 2D, H, W)
            flow = self.flow_estimator(flow_input)  # (B, 2, H, W)
            
            # 使用光流对齐特征
            aligned_ctx = self.warp_features(ctx_spatial, flow)
            aligned_ctx = self.feature_align(aligned_ctx)
            
            # 转回patch格式
            aligned_patch = aligned_ctx.permute(0, 2, 3, 1).reshape(B, N, D)
            aligned_features.append(aligned_patch)
        
        # 堆叠对齐的特征
        aligned_context = torch.stack(aligned_features, dim=1)  # (B, T-1, N, D)
        
        # 使用注意力融合
        target_expanded = target_features.unsqueeze(1)  # (B, 1, N, D)
        all_features = torch.cat([target_expanded, aligned_context], dim=1)  # (B, T, N, D)
        
        # 重塑为注意力输入格式
        all_features_flat = all_features.reshape(B, -1, D)  # (B, T*N, D)
        target_flat = target_features  # (B, N, D)
        
        # 注意力融合
        fused, _ = self.fusion_attention(target_flat, all_features_flat, all_features_flat)
        fused = self.norm(fused + target_features)  # 残差连接
        
        return fused
    
    def warp_features(self, features: torch.Tensor, flow: torch.Tensor) -> torch.Tensor:
        """使用光流对特征进行变形"""
        B, C, H, W = features.shape
        
        # 创建采样网格
        grid_y, grid_x = torch.meshgrid(
            torch.arange(H, device=features.device, dtype=torch.float32),
            torch.arange(W, device=features.device, dtype=torch.float32),
            indexing='ij'
        )
        grid = torch.stack([grid_x, grid_y], dim=0).unsqueeze(0).repeat(B, 1, 1, 1)  # (B, 2, H, W)
        
        # 应用光流
        warped_grid = grid + flow
        
        # 归一化到[-1, 1]
        warped_grid[:, 0] = 2.0 * warped_grid[:, 0] / (W - 1) - 1.0
        warped_grid[:, 1] = 2.0 * warped_grid[:, 1] / (H - 1) - 1.0
        
        # 重排维度为grid_sample期望的格式
        warped_grid = warped_grid.permute(0, 2, 3, 1)  # (B, H, W, 2)
        
        # 双线性插值采样
        warped_features = F.grid_sample(
            features, warped_grid, mode='bilinear', padding_mode='border', align_corners=True
        )
        
        return warped_features


class FineGrainedInformationFusion(nn.Module):
    """
    细粒度信息融合模块 - 借鉴FuseFormer的核心思想
    在多个尺度上融合细粒度信息
    """
    
    def __init__(self, embed_dim: int, num_scales: int = 3, num_heads: int = 8):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_scales = num_scales
        self.num_heads = num_heads
        
        # 多尺度特征提取
        self.scale_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(embed_dim, embed_dim, kernel_size=3, stride=2**i, padding=2**i//2),
                nn.ReLU(inplace=True),
                nn.Conv2d(embed_dim, embed_dim, kernel_size=3, padding=1),
                nn.ReLU(inplace=True)
            ) for i in range(num_scales)
        ])
        
        # 尺度间注意力
        self.cross_scale_attention = nn.ModuleList([
            nn.MultiheadAttention(embed_dim, num_heads, batch_first=True)
            for _ in range(num_scales)
        ])
        
        # 特征融合
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(embed_dim * num_scales, embed_dim, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(embed_dim, embed_dim, 3, 1, 1)
        )
        
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            features: (B, N, D) 输入特征
        Returns:
            fused_features: (B, N, D) 融合后的特征
        """
        B, N, D = features.shape
        H = W = int(math.sqrt(N))
        
        # 转换为空间格式
        spatial_features = features.reshape(B, H, W, D).permute(0, 3, 1, 2)  # (B, D, H, W)
        
        # 多尺度特征提取
        scale_features = []
        for i, scale_conv in enumerate(self.scale_convs):
            scale_feat = scale_conv(spatial_features)  # (B, D, H/2^i, W/2^i)
            
            # 上采样到原始尺寸
            if i > 0:
                scale_feat = F.interpolate(scale_feat, size=(H, W), mode='bilinear', align_corners=False)
            
            scale_features.append(scale_feat)
        
        # 转换为patch格式进行注意力计算
        scale_patches = []
        for scale_feat in scale_features:
            # 确保特征尺寸正确
            _, _, feat_h, feat_w = scale_feat.shape
            scale_patch = scale_feat.permute(0, 2, 3, 1).reshape(B, feat_h * feat_w, D)
            # 如果尺寸不匹配，进行插值调整
            if feat_h * feat_w != N:
                scale_patch = F.interpolate(
                    scale_feat, size=(H, W), mode='bilinear', align_corners=False
                ).permute(0, 2, 3, 1).reshape(B, N, D)
            scale_patches.append(scale_patch)
        
        # 尺度间注意力融合
        attended_features = []
        for i, (scale_patch, attention) in enumerate(zip(scale_patches, self.cross_scale_attention)):
            # 使用其他尺度作为key和value
            other_scales = [scale_patches[j] for j in range(len(scale_patches)) if j != i]
            if other_scales:
                other_scales_cat = torch.cat(other_scales, dim=1)  # (B, (num_scales-1)*N, D)
                attended, _ = attention(scale_patch, other_scales_cat, other_scales_cat)
                attended = self.norm(attended + scale_patch)  # 残差连接
            else:
                attended = scale_patch
            attended_features.append(attended)
        
        # 转回空间格式进行卷积融合
        attended_spatial = []
        for attended in attended_features:
            spatial = attended.reshape(B, H, W, D).permute(0, 3, 1, 2)
            attended_spatial.append(spatial)
        
        # 拼接并融合
        concat_features = torch.cat(attended_spatial, dim=1)  # (B, num_scales*D, H, W)
        fused_spatial = self.fusion_conv(concat_features)  # (B, D, H, W)
        
        # 转回patch格式
        fused_features = fused_spatial.permute(0, 2, 3, 1).reshape(B, N, D)
        
        return fused_features


class WaterAwareContextAggregator(nn.Module):
    """
    水体感知的上下文聚合器
    结合水体频率信息进行智能的上下文特征聚合
    """
    
    def __init__(self, embed_dim: int, num_heads: int = 8, num_layers: int = 2):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        
        # 水体频率编码器
        self.water_freq_encoder = nn.Sequential(
            nn.Linear(1, embed_dim // 4),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 4, embed_dim)
        )
        
        # 流引导特征融合
        self.flow_fusion = FlowGuidedFeatureFusion(embed_dim, num_heads)
        
        # 细粒度信息融合
        self.fine_fusion = FineGrainedInformationFusion(embed_dim, num_scales=3, num_heads=num_heads)
        
        # 时间注意力层
        self.temporal_layers = nn.ModuleList([
            nn.MultiheadAttention(embed_dim, num_heads, batch_first=True)
            for _ in range(num_layers)
        ])
        
        self.norms = nn.ModuleList([
            nn.LayerNorm(embed_dim) for _ in range(num_layers)
        ])
        
        # 最终融合
        self.final_fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim, embed_dim)
        )
        
    def forward(self, center_features: torch.Tensor, context_features: torch.Tensor, 
                water_frequency: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            center_features: (B, N, D) 中心帧特征
            context_features: (B, T-1, N, D) 上下文帧特征  
            water_frequency: (B, H, W) 水体频率图
        Returns:
            aggregated_features: (B, N, D) 聚合后的特征
        """
        B, N, D = center_features.shape
        
        # 1. 流引导特征融合
        flow_fused = self.flow_fusion(center_features, context_features)
        
        # 2. 细粒度信息融合
        fine_fused = self.fine_fusion(flow_fused)
        
        # 3. 水体频率调制
        if water_frequency is not None:
            H = W = int(math.sqrt(N))
            # 确保水体频率图与特征尺寸匹配
            if water_frequency.shape[-2:] != (H, W):
                water_freq_resized = F.interpolate(
                    water_frequency.unsqueeze(1), size=(H, W), mode='bilinear', align_corners=False
                ).squeeze(1)
            else:
                water_freq_resized = water_frequency

            water_freq_flat = water_freq_resized.reshape(B, -1, 1)  # (B, N, 1)
            water_encoding = self.water_freq_encoder(water_freq_flat)  # (B, N, D)
            fine_fused = fine_fused + water_encoding
        
        # 4. 时间注意力处理
        attended = fine_fused
        for layer, norm in zip(self.temporal_layers, self.norms):
            # 使用上下文特征作为key和value
            context_flat = context_features.reshape(B, -1, D)  # (B, (T-1)*N, D)
            attended_new, _ = layer(attended, context_flat, context_flat)
            attended = norm(attended_new + attended)  # 残差连接
        
        # 5. 最终融合
        combined = torch.cat([center_features, attended], dim=-1)  # (B, N, 2D)
        aggregated = self.final_fusion(combined)  # (B, N, D)
        
        return aggregated


class PatchEmbed(nn.Module):
    """轻量级Patch Embedding"""

    def __init__(self, img_size: int = 256, patch_size: int = 4, in_chans: int = 2, embed_dim: int = 96):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.grid_size = img_size // patch_size
        self.num_patches = self.grid_size ** 2

        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: (B, T, C, H, W) 或 (B, C, H, W)
        Returns:
            patches: (B, T, N, D) 或 (B, N, D)
        """
        if x.dim() == 5:  # 视频输入
            B, T, C, H, W = x.shape
            x = x.reshape(B * T, C, H, W)
            x = self.proj(x)  # (B*T, D, H', W')
            _, D, H_p, W_p = x.shape
            x = x.flatten(2).transpose(1, 2)  # (B*T, N, D)
            x = self.norm(x)
            x = x.reshape(B, T, H_p * W_p, D)
            return x
        else:  # 图像输入
            B, C, H, W = x.shape
            x = self.proj(x)  # (B, D, H', W')
            x = x.flatten(2).transpose(1, 2)  # (B, N, D)
            x = self.norm(x)
            return x





class GeographicTemporalEncoder(nn.Module):
    """地理-时间条件编码器"""

    def __init__(self, embed_dim: int, num_freq_bands: int = 8):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_freq_bands = num_freq_bands

        # 地理位置Fourier特征
        self.freq_bands = nn.Parameter(
            torch.logspace(0, math.log10(1000), num_freq_bands), requires_grad=False
        )

        # 地理编码器
        geo_input_dim = num_freq_bands * 4  # sin/cos for lon/lat
        self.geo_encoder = nn.Sequential(
            nn.Linear(geo_input_dim, embed_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 2, embed_dim // 2)
        )

        # 时间编码器
        self.year_embed = nn.Embedding(200, embed_dim // 4)  # 1900-2099
        self.month_embed = nn.Embedding(12, embed_dim // 4)

        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim, embed_dim)
        )

    def forward(self, lon: torch.Tensor, lat: torch.Tensor,
                year: torch.Tensor, month: torch.Tensor) -> torch.Tensor:
        """
        Args:
            lon: (B,) 经度
            lat: (B,) 纬度
            year: (B,) 年份
            month: (B,) 月份
        Returns:
            encoding: (B, D) 地理-时间编码
        """
        B = lon.shape[0]
        device = lon.device

        # 地理Fourier特征
        lon_scaled = lon / 180.0 * math.pi
        lat_scaled = lat / 90.0 * math.pi

        geo_features = []
        for freq in self.freq_bands:
            geo_features.extend([
                torch.sin(freq * lon_scaled),
                torch.cos(freq * lon_scaled),
                torch.sin(freq * lat_scaled),
                torch.cos(freq * lat_scaled)
            ])
        geo_features = torch.stack(geo_features, dim=-1)  # (B, num_freq_bands*4)
        geo_encoding = self.geo_encoder(geo_features)  # (B, D//2)

        # 时间特征
        year_normalized = torch.clamp(year - 1900, 0, 199).long()
        month_normalized = torch.clamp(month - 1, 0, 11).long()

        year_encoding = self.year_embed(year_normalized)  # (B, D//4)
        month_encoding = self.month_embed(month_normalized)  # (B, D//4)
        temporal_encoding = torch.cat([year_encoding, month_encoding], dim=-1)  # (B, D//2)

        # 融合地理和时间特征
        combined = torch.cat([geo_encoding, temporal_encoding], dim=-1)  # (B, D)
        final_encoding = self.fusion(combined)

        return final_encoding


class SwinTransformerBlock(nn.Module):
    """标准Swin Transformer块"""

    def __init__(self, dim: int, num_heads: int, window_size: int = 8,
                 shift_size: int = 0, mlp_ratio: float = 4.0, drop_path: float = 0.):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio

        self.norm1 = nn.LayerNorm(dim)
        self.attn = WindowAttention(
            dim, window_size=window_size, num_heads=num_heads
        )

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = nn.LayerNorm(dim)

        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = MLP(in_features=dim, hidden_features=mlp_hidden_dim, drop=0.)

    def forward(self, x: torch.Tensor, H: int, W: int) -> torch.Tensor:
        """
        Args:
            x: (B, H*W, C)
            H, W: 空间维度
        Returns:
            x: (B, H*W, C)
        """
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"

        shortcut = x
        x = self.norm1(x)
        x = x.view(B, H, W, C)

        # 循环移位
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        # 窗口注意力
        x_windows = window_partition(shifted_x, self.window_size)  # (B*num_windows, window_size, window_size, C)
        x_windows = x_windows.view(-1, self.window_size * self.window_size, C)  # (B*num_windows, window_size*window_size, C)

        attn_windows = self.attn(x_windows)  # (B*num_windows, window_size*window_size, C)

        # 合并窗口
        attn_windows = attn_windows.view(-1, self.window_size, self.window_size, C)
        shifted_x = window_reverse(attn_windows, self.window_size, H, W)  # (B, H, W, C)

        # 反向循环移位
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x
        x = x.view(B, H * W, C)

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class WindowAttention(nn.Module):
    """窗口注意力机制"""

    def __init__(self, dim: int, window_size: int, num_heads: int):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3)
        self.proj = nn.Linear(dim, dim)

        # 相对位置偏置
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )

        coords_h = torch.arange(self.window_size)
        coords_w = torch.arange(self.window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size - 1
        relative_coords[:, :, 1] += self.window_size - 1
        relative_coords[:, :, 0] *= 2 * self.window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: (B*num_windows, N, C)
        Returns:
            x: (B*num_windows, N, C)
        """
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)

        attn = F.softmax(attn, dim=-1)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        return x


class MLP(nn.Module):
    """多层感知机"""

    def __init__(self, in_features: int, hidden_features: int, out_features: int = None, drop: float = 0.):
        super().__init__()
        out_features = out_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = nn.GELU()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample"""

    def __init__(self, drop_prob: float = None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()
        output = x.div(keep_prob) * random_tensor
        return output


def window_partition(x: torch.Tensor, window_size: int) -> torch.Tensor:
    """将特征图分割为窗口"""
    B, H, W, C = x.shape
    x = x.view(B, H // window_size, window_size, W // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows: torch.Tensor, window_size: int, H: int, W: int) -> torch.Tensor:
    """将窗口合并回特征图"""
    B = int(windows.shape[0] / (H * W / window_size / window_size))
    x = windows.view(B, H // window_size, W // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return x


class SwinEncoder(nn.Module):
    """基于Swin Transformer的编码器"""

    def __init__(self, embed_dim: int = 96, depths: List[int] = [2, 2, 6, 2],
                 num_heads: List[int] = [3, 6, 12, 24], window_size: int = 8,
                 mlp_ratio: float = 4.0, drop_path_rate: float = 0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_layers = len(depths)

        # 构建每一层
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        self.layers = nn.ModuleList()

        for i_layer in range(self.num_layers):
            layer_blocks = nn.ModuleList()
            for i_block in range(depths[i_layer]):
                block_idx = sum(depths[:i_layer]) + i_block
                layer_blocks.append(
                    SwinTransformerBlock(
                        dim=int(embed_dim * 2 ** i_layer),
                        num_heads=num_heads[i_layer],
                        window_size=window_size,
                        shift_size=0 if (i_block % 2 == 0) else window_size // 2,
                        mlp_ratio=mlp_ratio,
                        drop_path=dpr[block_idx]
                    )
                )
            self.layers.append(layer_blocks)

        # 下采样层
        self.downsample_layers = nn.ModuleList()
        for i_layer in range(self.num_layers - 1):
            downsample_layer = PatchMerging(
                input_resolution=(64 // (2 ** i_layer), 64 // (2 ** i_layer)),
                dim=int(embed_dim * 2 ** i_layer)
            )
            self.downsample_layers.append(downsample_layer)

    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """
        Args:
            x: (B, N, D) 输入特征
        Returns:
            features: List[(B, C, H, W)] 多尺度特征
        """
        features = []
        B, N, D = x.shape
        H = W = int(math.sqrt(N))

        for i_layer, layer_blocks in enumerate(self.layers):
            # Transformer blocks
            for block in layer_blocks:
                x = block(x, H, W)

            # 转换为卷积格式并保存特征
            x_conv = x.reshape(B, H, W, -1).permute(0, 3, 1, 2)  # (B, C, H, W)
            features.append(x_conv)

            # 下采样（如果不是最后一层）
            if i_layer < self.num_layers - 1:
                downsample = self.downsample_layers[i_layer]
                x = downsample(x)  # (B, H*W, C) -> (B, (H/2)*(W/2), 2*C)
                H, W = H // 2, W // 2

        return features


class PatchMerging(nn.Module):
    """Patch合并层，用于下采样"""

    def __init__(self, input_resolution: Tuple[int, int], dim: int):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = nn.LayerNorm(4 * dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: (B, H*W, C)
        Returns:
            x: (B, (H/2)*(W/2), 2*C)
        """
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        assert H % 2 == 0 and W % 2 == 0, f"x size ({H}*{W}) are not even."

        x = x.view(B, H, W, C)

        x0 = x[:, 0::2, 0::2, :]  # (B, H/2, W/2, C)
        x1 = x[:, 1::2, 0::2, :]  # (B, H/2, W/2, C)
        x2 = x[:, 0::2, 1::2, :]  # (B, H/2, W/2, C)
        x3 = x[:, 1::2, 1::2, :]  # (B, H/2, W/2, C)
        x = torch.cat([x0, x1, x2, x3], -1)  # (B, H/2, W/2, 4*C)
        x = x.view(B, -1, 4 * C)  # (B, H/2*W/2, 4*C)

        x = self.norm(x)
        x = self.reduction(x)

        return x


class SwinWaterNetV11(nn.Module):
    """
    SwinWaterNetV11: 融合成熟视频修复技术的水体时序重建网络

    核心创新：
    1. 流引导特征融合 - 借鉴E2FGVI
    2. 细粒度信息融合 - 借鉴FuseFormer
    3. 水体感知的上下文聚合
    4. 地理-时间条件调制
    """

    def __init__(self, img_size: int = 256, patch_size: int = 4, in_chans: int = 2,
                 out_chans: int = 2, embed_dim: int = 96, depths: List[int] = [2, 2, 6, 2],
                 num_heads: List[int] = [3, 6, 12, 24], window_size: int = 8,
                 num_frames: int = 120, mlp_ratio: float = 4.0, drop_rate: float = 0.,
                 attn_drop_rate: float = 0., drop_path_rate: float = 0.1):
        super().__init__()

        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.num_frames = num_frames

        # Patch embedding
        self.patch_embed = PatchEmbed(
            img_size=img_size, patch_size=patch_size,
            in_chans=in_chans, embed_dim=embed_dim
        )



        # 水体感知的上下文聚合器
        self.context_aggregator = WaterAwareContextAggregator(
            embed_dim=embed_dim, num_heads=num_heads[0], num_layers=2
        )

        # 地理-时间编码器
        self.geo_temporal_encoder = GeographicTemporalEncoder(
            embed_dim=embed_dim, num_freq_bands=8
        )

        # Swin编码器
        self.encoder = SwinEncoder(
            embed_dim=embed_dim, depths=depths, num_heads=num_heads,
            window_size=window_size, mlp_ratio=mlp_ratio, drop_path_rate=drop_path_rate
        )

        # 解码器 (复用现有的SwinUNetDecoder)
        self.decoder = SwinUNetDecoder(
            in_channels=embed_dim * (2 ** (len(depths) - 1)),
            img_size=img_size,
            patch_size=patch_size,
            num_classes=out_chans,
            use_checkpoint=False
        )

    def forward(self, batch: Dict[str, Any]) -> Dict[str, Any]:
        """
        前向传播

        Args:
            batch: 包含以下键的字典
                - 'input_sequence': (B, T, C, H, W) 输入视频序列
                - 'center_frame_idx': (B,) 中心帧索引
                - 'occurrence': (B, H, W) 水体频率图 (可选)
                - 'tile_lon': (B,) 经度
                - 'tile_lat': (B,) 纬度
                - 'year': (B,) 年份
                - 'month': (B,) 月份

        Returns:
            outputs: 包含 'inpaint' 键的字典，其中包含 'logits'
        """
        video = batch['input_sequence']  # (B, T, C, H, W)
        center_frame_idx = batch['center_frame_idx']  # (B,)
        water_frequency = batch.get('occurrence', None)  # (B, H, W)

        B, T, C, H, W = video.shape
        device = video.device

        # 1. 提取中心帧
        batch_indices = torch.arange(B, device=device)
        center_frames = video[batch_indices, center_frame_idx]  # (B, C, H, W)

        # 2. 提取上下文帧 (除中心帧外的所有帧)
        context_frames = []
        for b in range(B):
            center_idx = center_frame_idx[b].item()
            # 获取除中心帧外的所有帧
            context_indices = torch.cat([
                torch.arange(center_idx, device=device),
                torch.arange(center_idx + 1, T, device=device)
            ])
            if len(context_indices) > 0:
                context_frames_b = video[b, context_indices]  # (T-1, C, H, W)
            else:
                # 如果只有一帧，复制中心帧作为上下文
                context_frames_b = center_frames[b:b+1]  # (1, C, H, W)
            context_frames.append(context_frames_b)

        # 统一上下文帧数量 (取最小值或固定数量)
        min_context_frames = min(len(cf) for cf in context_frames)
        min_context_frames = max(min_context_frames, 1)  # 至少1帧

        # 截取或填充到统一长度
        unified_context = []
        for cf in context_frames:
            if len(cf) >= min_context_frames:
                # 均匀采样
                indices = torch.linspace(0, len(cf) - 1, min_context_frames).long()
                unified_context.append(cf[indices])
            else:
                # 重复填充
                repeated = cf.repeat(min_context_frames // len(cf) + 1, 1, 1, 1)
                unified_context.append(repeated[:min_context_frames])

        context_video = torch.stack(unified_context)  # (B, T_context, C, H, W)

        # 3. Patch embedding
        center_patches = self.patch_embed(center_frames)  # (B, N, D)
        context_patches = self.patch_embed(context_video)  # (B, T_context, N, D)

        # 4. 上下文聚合
        aggregated_features = self.context_aggregator(
            center_patches, context_patches, water_frequency
        )

        # 5. 地理-时间条件编码
        geo_temporal_encoding = self.geo_temporal_encoder(
            batch['tile_lon'], batch['tile_lat'],
            batch['year'], batch['month']
        )  # (B, D)

        # 6. 添加地理-时间条件
        B_f, N_f, D_f = aggregated_features.shape
        geo_encoding_expanded = geo_temporal_encoding.unsqueeze(1).expand(B_f, N_f, D_f)
        conditioned_features = aggregated_features + geo_encoding_expanded

        # 7. 编码
        encoder_features = self.encoder(conditioned_features)  # List[(B, C, H, W)]

        # 8. 解码
        logits = self.decoder(encoder_features[::-1])  # (B, out_chans, H, W)

        return {
            'inpaint': {
                'logits': logits
            }
        }


def create_swin_water_net_v11(config) -> SwinWaterNetV11:
    """
    创建SwinWaterNetV11模型

    Args:
        config: 配置对象，包含模型参数

    Returns:
        model: SwinWaterNetV11实例
    """
    # 兼容不同的配置结构
    if hasattr(config, 'model') and hasattr(config.model, 'swin_config'):
        swin_config = config.model.swin_config
    elif hasattr(config, 'swin_config'):
        swin_config = config.swin_config
    else:
        swin_config = config

    # 安全获取配置值的辅助函数
    def safe_get(obj, key, default):
        if hasattr(obj, key):
            return getattr(obj, key)
        elif isinstance(obj, dict) and key in obj:
            return obj[key]
        else:
            return default

    model_params = {
        'img_size': safe_get(swin_config, 'img_size', 256),
        'patch_size': safe_get(swin_config, 'patch_size', 4),
        'in_chans': safe_get(swin_config, 'in_chans', 2),
        'out_chans': safe_get(swin_config, 'out_chans', 2),
        'embed_dim': safe_get(swin_config, 'embed_dim', 96),
        'depths': safe_get(swin_config, 'depths', [2, 2, 6, 2]),
        'num_heads': safe_get(swin_config, 'num_heads', [3, 6, 12, 24]),
        'window_size': safe_get(swin_config, 'window_size', 8),
        'num_frames': safe_get(swin_config, 'num_frames', 120),
        'mlp_ratio': safe_get(swin_config, 'mlp_ratio', 4.0),
        'drop_rate': safe_get(swin_config, 'drop_rate', 0.0),
        'attn_drop_rate': safe_get(swin_config, 'attn_drop_rate', 0.0),
        'drop_path_rate': safe_get(swin_config, 'drop_path_rate', 0.1),
    }

    model = SwinWaterNetV11(**model_params)

    logger.info(f"Created SwinWaterNetV11 with {sum(p.numel() for p in model.parameters()):,} parameters")

    return model


# 为了兼容性，提供别名
def create_swin_water_net(config):
    """兼容性别名，用于与现有训练脚本兼容"""
    return create_swin_water_net_v11(config)


__all__ = [
    'SwinWaterNetV11',
    'create_swin_water_net_v11',
    'create_swin_water_net',
    'FlowGuidedFeatureFusion',
    'FineGrainedInformationFusion',
    'WaterAwareContextAggregator',
    'GeographicTemporalEncoder',
    'SwinEncoder',
    'PatchEmbed'
]
