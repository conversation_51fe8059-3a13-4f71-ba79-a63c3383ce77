"""
3D Swin Transformer Encoder for Video Water Body Detection
Extends 2D Swin Transformer to handle spatiotemporal data
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import math
import logging
from einops import rearrange
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)


class WindowAttention3D(nn.Module):
    """3D window-based multi-head self attention with relative position bias"""
    
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size  # (T, H, W)
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        # Relative position bias table for 3D
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1) * (2 * window_size[2] - 1), num_heads))

        # Generate relative position index for 3D
        coords_t = torch.arange(self.window_size[0])
        coords_h = torch.arange(self.window_size[1])
        coords_w = torch.arange(self.window_size[2])
        coords = torch.stack(torch.meshgrid([coords_t, coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 2] += self.window_size[2] - 1
        relative_coords[:, :, 0] *= (2 * self.window_size[1] - 1) * (2 * self.window_size[2] - 1)
        relative_coords[:, :, 1] *= 2 * self.window_size[2] - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        # QKV projection
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)
        
    def forward(self, x, mask=None):
        """3D attention computation"""
        B_, N, C = x.shape

        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        # Add relative position bias - 确保类型匹配
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1] * self.window_size[2],
            self.window_size[0] * self.window_size[1] * self.window_size[2], -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        # 确保bias与attn类型一致
        relative_position_bias = relative_position_bias.to(attn.dtype)
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            # 确保mask与attn类型一致
            mask = mask.to(attn.dtype)
            attn = attn.reshape(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.reshape(-1, self.num_heads, N, N)

        attn = self.softmax(attn)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        return x


def window_partition_3d(x, window_size):
    """Partition 3D input into non-overlapping windows"""
    B, T, H, W, C = x.shape
    
    pad_t = (window_size[0] - T % window_size[0]) % window_size[0]
    pad_h = (window_size[1] - H % window_size[1]) % window_size[1]
    pad_w = (window_size[2] - W % window_size[2]) % window_size[2]
    
    if pad_t > 0 or pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h, 0, pad_t))
        T_padded, H_padded, W_padded = T + pad_t, H + pad_h, W + pad_w
    else:
        T_padded, H_padded, W_padded = T, H, W
    
    x = x.reshape(B, T_padded // window_size[0], window_size[0], 
                  H_padded // window_size[1], window_size[1],
                  W_padded // window_size[2], window_size[2], C)
    windows = x.permute(0, 1, 3, 5, 2, 4, 6, 7).contiguous().reshape(
        -1, window_size[0], window_size[1], window_size[2], C)
    return windows


def window_reverse_3d(windows, window_size, T, H, W):
    """Reverse 3D window partition"""
    pad_t = (window_size[0] - T % window_size[0]) % window_size[0]
    pad_h = (window_size[1] - H % window_size[1]) % window_size[1]
    pad_w = (window_size[2] - W % window_size[2]) % window_size[2]
    T_padded, H_padded, W_padded = T + pad_t, H + pad_h, W + pad_w
    
    B = int(windows.shape[0] / (T_padded * H_padded * W_padded / window_size[0] / window_size[1] / window_size[2]))
    x = windows.reshape(B, T_padded // window_size[0], H_padded // window_size[1], W_padded // window_size[2],
                       window_size[0], window_size[1], window_size[2], -1)
    x = x.permute(0, 1, 4, 2, 5, 3, 6, 7).contiguous().reshape(B, T_padded, H_padded, W_padded, -1)
    
    if pad_t > 0 or pad_h > 0 or pad_w > 0:
        x = x[:, :T, :H, :W, :]
    
    return x


class SwinTransformerBlock3D(nn.Module):
    """3D Swin Transformer block with window attention"""
    
    def __init__(self, dim, num_heads, window_size=(2, 7, 7), shift_size=(0, 0, 0), mlp_ratio=4., qkv_bias=True, 
                 drop=0., attn_drop=0., drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio
        
        if any(w <= 0 for w in window_size):
            raise ValueError(f"window_size must be positive, got {window_size}")
        
        self.norm1 = norm_layer(dim)
        self.attn = WindowAttention3D(
            dim, window_size=window_size, num_heads=num_heads,
            qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim), act_layer(), nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim), nn.Dropout(drop)
        )

    def calculate_mask_3d(self, T, H, W, device, dtype=None):
        """Calculate 3D attention mask"""
        img_mask = torch.zeros((1, T, H, W, 1), device=device, dtype=dtype or torch.float32)
        t_slices = (slice(0, -self.window_size[0]), slice(-self.window_size[0], -self.shift_size[0]), slice(-self.shift_size[0], None))
        h_slices = (slice(0, -self.window_size[1]), slice(-self.window_size[1], -self.shift_size[1]), slice(-self.shift_size[1], None))
        w_slices = (slice(0, -self.window_size[2]), slice(-self.window_size[2], -self.shift_size[2]), slice(-self.shift_size[2], None))
        cnt = 0
        for t in t_slices:
            for h in h_slices:
                for w in w_slices:
                    img_mask[:, t, h, w, :] = cnt
                    cnt += 1
        mask_windows = window_partition_3d(img_mask, self.window_size)
        mask_windows = mask_windows.reshape(-1, self.window_size[0] * self.window_size[1] * self.window_size[2])
        attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
        attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))
        return attn_mask

    def forward(self, x, T, H, W):
        B, L, C = x.shape
        assert L == T * H * W, f"Input feature has wrong size, expected {T*H*W}, got {L}"

        shortcut = x
        x = self.norm1(x)
        x = x.reshape(B, T, H, W, C)

        # Cyclic shift
        if any(s > 0 for s in self.shift_size):
            shifted_x = torch.roll(x, shifts=(-self.shift_size[0], -self.shift_size[1], -self.shift_size[2]), dims=(1, 2, 3))
        else:
            shifted_x = x

        # Partition windows
        x_windows = window_partition_3d(shifted_x, self.window_size)
        x_windows = x_windows.reshape(-1, self.window_size[0] * self.window_size[1] * self.window_size[2], C)

        # Generate mask
        attn_mask = None
        if any(s > 0 for s in self.shift_size):
            attn_mask = self.calculate_mask_3d(T, H, W, x.device, x.dtype)

        # W-MSA/SW-MSA
        attn_windows = self.attn(x_windows, mask=attn_mask)

        # Merge windows
        attn_windows = attn_windows.reshape(-1, self.window_size[0], self.window_size[1], self.window_size[2], C)
        shifted_x = window_reverse_3d(attn_windows, self.window_size, T, H, W)

        # Reverse cyclic shift
        if any(s > 0 for s in self.shift_size):
            x = torch.roll(shifted_x, shifts=(self.shift_size[0], self.shift_size[1], self.shift_size[2]), dims=(1, 2, 3))
        else:
            x = shifted_x

        x = x.reshape(B, T * H * W, C)

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class PatchMerging3D(nn.Module):
    """3D patch merging layer for downsampling"""
    
    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution  # (T, H, W)
        self.dim = dim
        self.reduction = nn.Linear(8 * dim, 2 * dim, bias=False)  # 8 = 2^3 for 3D
        self.norm = norm_layer(8 * dim)
    
    def forward(self, x):
        """Forward pass with 3D patch merging"""
        T, H, W = self.input_resolution
        B, L, C = x.shape
        assert L == T * H * W, f"Input feature has wrong size, expected {T*H*W}, got {L}"
        assert T % 2 == 0 and H % 2 == 0 and W % 2 == 0, f"x size ({T}*{H}*{W}) are not even."

        x = x.reshape(B, T, H, W, C)

        x0 = x[:, 0::2, 0::2, 0::2, :]  # B T/2 H/2 W/2 C
        x1 = x[:, 1::2, 0::2, 0::2, :]  # B T/2 H/2 W/2 C
        x2 = x[:, 0::2, 1::2, 0::2, :]  # B T/2 H/2 W/2 C
        x3 = x[:, 1::2, 1::2, 0::2, :]  # B T/2 H/2 W/2 C
        x4 = x[:, 0::2, 0::2, 1::2, :]  # B T/2 H/2 W/2 C
        x5 = x[:, 1::2, 0::2, 1::2, :]  # B T/2 H/2 W/2 C
        x6 = x[:, 0::2, 1::2, 1::2, :]  # B T/2 H/2 W/2 C
        x7 = x[:, 1::2, 1::2, 1::2, :]  # B T/2 H/2 W/2 C
        x = torch.cat([x0, x1, x2, x3, x4, x5, x6, x7], -1)  # B T/2 H/2 W/2 8*C
        x = x.view(B, -1, 8 * C)  # B T/2*H/2*W/2 8*C

        x = self.norm(x)
        x = self.reduction(x)

        return x


class SwinTransformerStage3D(nn.Module):
    """A stage of 3D Swin Transformer with multiple blocks"""
    
    def __init__(self, dim, input_resolution, depth, num_heads, window_size, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm,
                 downsample=None, use_checkpoint=False):
        super().__init__()
        self.dim = dim
        self.input_resolution = input_resolution
        self.depth = depth
        self.use_checkpoint = use_checkpoint
        
        # Build blocks
        self.blocks = nn.ModuleList([
            SwinTransformerBlock3D(
                dim=dim,
                num_heads=num_heads, window_size=window_size,
                shift_size=(0, 0, 0) if (i % 2 == 0) else (window_size[0] // 2, window_size[1] // 2, window_size[2] // 2),
                mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
                drop=drop, attn_drop=attn_drop,
                drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path,
                norm_layer=norm_layer)
            for i in range(depth)])
        
        # Patch merging layer
        if downsample is not None:
            self.downsample = downsample(input_resolution, dim=dim, norm_layer=norm_layer)
        else:
            self.downsample = None
    
    def forward(self, x):
        """Forward pass through the stage"""
        for blk in self.blocks:
            if self.use_checkpoint:
                x = torch.utils.checkpoint.checkpoint(blk, x, self.input_resolution[0], self.input_resolution[1], self.input_resolution[2])
            else:
                x = blk(x, self.input_resolution[0], self.input_resolution[1], self.input_resolution[2])
        
        if self.downsample is not None:
            x = self.downsample(x)
        
        return x


class SwinTransformerEncoder3D(nn.Module):
    """3D Swin Transformer encoder with multiple stages"""
    
    def __init__(self, img_size=256, patch_size=4, in_chans=2, embed_dim=96, depths=[2, 2, 6, 2],
                 num_heads=[3, 6, 12, 24], window_size=(2, 7, 7), mlp_ratio=4., qkv_bias=True,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1, norm_layer=nn.LayerNorm,
                 patch_norm=True, use_checkpoint=False, temporal_patch_size=2, initial_temporal_length=48):
        super().__init__()
        
        self.num_layers = len(depths)
        self.embed_dim = embed_dim
        self.patch_norm = patch_norm
        self.num_features = int(embed_dim * 2 ** (self.num_layers - 1))
        self.mlp_ratio = mlp_ratio
        self.temporal_patch_size = temporal_patch_size
        self.initial_temporal_length = initial_temporal_length
        
        # 3D patch embedding
        self.patch_embed = nn.Conv3d(in_chans, embed_dim, kernel_size=(temporal_patch_size, patch_size, patch_size), 
                                    stride=(temporal_patch_size, patch_size, patch_size))
        self.norm = norm_layer(embed_dim)
        
        # Calculate patch resolution
        self.patches_resolution = [
            img_size // patch_size,
            img_size // patch_size
        ]
        
        # Stochastic depth
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        # Build stages
        self.layers = nn.ModuleList()
        for i_layer in range(self.num_layers):
            T_stage = self.initial_temporal_length // (2 ** i_layer)  # Use configurable initial temporal length
            H_stage = self.patches_resolution[0] // (2 ** i_layer)
            W_stage = self.patches_resolution[1] // (2 ** i_layer)
            layer = SwinTransformerStage3D(
                dim=int(embed_dim * 2 ** i_layer),
                input_resolution=(T_stage, H_stage, W_stage),
                depth=depths[i_layer],
                num_heads=num_heads[i_layer],
                window_size=window_size,
                mlp_ratio=self.mlp_ratio,
                qkv_bias=qkv_bias,
                drop=drop_rate, attn_drop=attn_drop_rate,
                drop_path=dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])],
                norm_layer=norm_layer,
                downsample=PatchMerging3D if (i_layer < self.num_layers - 1) else None,
                use_checkpoint=use_checkpoint)
            self.layers.append(layer)
        
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """改进的权重初始化方法"""
        if isinstance(m, nn.Linear):
            # 使用Xavier/Glorot初始化，更适合深度网络
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            # LayerNorm的标准初始化
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.GroupNorm):
            # GroupNorm的标准初始化
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            # 卷积层使用Kaiming初始化
            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.ConvTranspose2d):
            # 转置卷积层使用Kaiming初始化
            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """Forward pass through 3D encoder stages"""
        # x: (B, T, N, D) -> convert to 3D format
        B, T, N, D = x.shape

        # Convert to spatial format for 3D processing
        H = W = int(math.sqrt(N))
        if H * W != N:
            # Handle non-square features - 更精确的处理
            H = int(math.ceil(math.sqrt(N)))
            W = H
            pad_size = H * W - N
            if pad_size > 0:
                x = F.pad(x, (0, 0, 0, pad_size))
                N = H * W

        # Reshape to 3D format: (B, T, H, W, D) -> (B, D, T, H, W)
        x = x.reshape(B, T, H, W, D).permute(0, 4, 1, 2, 3)

        # Apply normalization - 确保数值稳定性
        x_reshaped = x.permute(0, 2, 3, 4, 1)  # (B, T, H, W, D)
        x_norm = self.norm(x_reshaped)

        # 检查归一化后是否有问题
        if torch.isnan(x_norm).any() or torch.isinf(x_norm).any():
            logger.warning("NaN/Inf detected after normalization in 3D encoder")
            x_norm = torch.nan_to_num(x_norm, nan=0.0, posinf=1.0, neginf=-1.0)

        x = x_norm.permute(0, 4, 1, 2, 3)

        # Reshape to (B, T*H*W, D) for transformer processing
        x = x.permute(0, 2, 3, 4, 1)  # (B, T, H, W, D)
        B, T_p, H_p, W_p, D = x.shape
        x = x.reshape(B, T_p * H_p * W_p, D)

        # Pass through all stages with dynamic resolution adjustment
        features = []
        current_T, current_H, current_W = T_p, H_p, W_p

        for i, layer in enumerate(self.layers):
            # Update layer's input resolution to match actual input
            layer.input_resolution = (current_T, current_H, current_W)

            # Update all blocks in the layer
            for block in layer.blocks:
                # No need to update block resolution as it's passed as parameters
                pass

            # Update downsample layer if exists
            if layer.downsample is not None:
                layer.downsample.input_resolution = (current_T, current_H, current_W)

            # 前向传播前检查输入
            if torch.isnan(x).any() or torch.isinf(x).any():
                logger.warning(f"NaN/Inf detected before layer {i}")
                x = torch.nan_to_num(x, nan=0.0, posinf=1.0, neginf=-1.0)

            x = layer(x)

            # 前向传播后检查输出
            if torch.isnan(x).any() or torch.isinf(x).any():
                logger.warning(f"NaN/Inf detected after layer {i}")
                x = torch.nan_to_num(x, nan=0.0, posinf=1.0, neginf=-1.0)

            # Update dimensions for next stage (after downsampling)
            if layer.downsample is not None:
                current_T = max(1, current_T // 2)  # 确保不会变成0
                current_H = max(1, current_H // 2)
                current_W = max(1, current_W // 2)

            # Convert back to spatial format for decoder
            expected_length = current_T * current_H * current_W
            if x.shape[1] == expected_length:
                spatial_feat = x.view(B, current_T, current_H, current_W, -1).permute(0, 4, 1, 2, 3)  # (B, D, T, H, W)
                # Average over temporal dimension for 2D decoder
                spatial_feat = spatial_feat.mean(dim=2)  # (B, D, H, W)
                features.append(spatial_feat)
            else:
                # Handle non-matching dimensions - 更安全的处理
                logger.warning(f"Dimension mismatch at layer {i}: expected {expected_length}, got {x.shape[1]}")
                # 尝试重新整形
                D_feat = x.shape[-1]
                if x.shape[1] > 0:
                    # 使用adaptive pooling来调整尺寸
                    x_reshaped = x.view(B, current_T, -1, D_feat)  # (B, T, H*W, D)
                    x_pooled = F.adaptive_avg_pool2d(x_reshaped.permute(0, 3, 1, 2), (current_H, current_W))  # (B, D, H, W)
                    features.append(x_pooled)
                else:
                    # 创建零特征作为fallback
                    zero_feat = torch.zeros(B, D_feat, current_H, current_W, device=x.device, dtype=x.dtype)
                    features.append(zero_feat)

        return features