"""
Video SwinWaterNet v5.1.1 - Fixed int64 overflow issues
Critical fix for int64_t overflow errors in tensor operations
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import numpy as np
from einops import rearrange, repeat
from einops.layers.torch import Rearrange
from typing import Dict, List, Optional, Tuple, Union
import math
import logging
from model import compute_dynamic_degree

logger = logging.getLogger(__name__)

from model.previous.decoder import MemoryEfficientDecoder


def safe_long_conversion(tensor, min_val=0, max_val=None, fallback_val=0):
    """
    Safely convert tensor to long type with overflow protection
    
    Args:
        tensor: Input tensor
        min_val: Minimum allowed value
        max_val: Maximum allowed value (if None, uses int32 max)
        fallback_val: Fallback value for invalid entries
    
    Returns:
        Tensor converted to long type without overflow
    """
    if not isinstance(tensor, torch.Tensor):
        tensor = torch.tensor(tensor)
    
    # Set reasonable max value to prevent overflow
    if max_val is None:
        max_val = 2147483647  # int32 max, safe for int64 conversion
    
    # Handle different input types
    if tensor.dtype.is_floating_point:
        # For float tensors, check for valid range first
        valid_mask = torch.isfinite(tensor)
        clamped = torch.clamp(tensor, min_val, max_val)
        result = torch.where(valid_mask, clamped, torch.tensor(fallback_val, dtype=tensor.dtype, device=tensor.device))
        return result.long()
    else:
        # For integer tensors, clamp to safe range
        return torch.clamp(tensor, min_val, max_val).long()


class PositionalEncoding3D(nn.Module):
    """Enhanced 3D sinusoidal positional encoding with overflow protection"""
    
    def __init__(self, embed_dim, max_temporal_len=120):
        super().__init__()
        self.embed_dim = embed_dim
        self.max_temporal_len = max_temporal_len
        
        # Ensure embed_dim is divisible by 3 for balanced encoding
        self.time_dim = embed_dim // 3
        
        # Precompute temporal position encodings with better numerical stability
        pe_t = torch.zeros(max_temporal_len, self.time_dim)
        position = torch.arange(0, max_temporal_len, dtype=torch.float32).unsqueeze(1)
        
        # Use more stable division term computation
        div_term = torch.exp(torch.arange(0, self.time_dim, 2, dtype=torch.float32) * 
                           -(math.log(10000.0) / self.time_dim))
        
        pe_t[:, 0::2] = torch.sin(position * div_term)
        if self.time_dim > 1:
            pe_t[:, 1::2] = torch.cos(position * div_term[:pe_t[:, 1::2].shape[1]])
        
        self.register_buffer('pe_t', pe_t)
    
    def forward(self, t_positions):
        """Get positional encoding with safe conversion"""
        # Safe conversion to prevent overflow
        t_positions = safe_long_conversion(t_positions, 0, self.max_temporal_len - 1, 0)
        return self.pe_t[t_positions]


class EfficientPatchEmbed(nn.Module):
    """Enhanced memory-efficient patch embedding with better error handling"""
    
    def __init__(self, 
                 img_size=256, 
                 patch_size=8,
                 in_chans=2,
                 embed_dim=128):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.grid_size = img_size // patch_size
        self.in_chans = in_chans
        self.embed_dim = embed_dim
        
        # Enhanced patch embedding with better initialization
        self.proj = nn.Conv2d(
            in_chans, embed_dim, 
            kernel_size=patch_size, 
            stride=patch_size,
            bias=False  # Improve stability
        )
        
        self.norm = nn.LayerNorm(embed_dim)
        
        # Initialize weights
        nn.init.xavier_uniform_(self.proj.weight)
    
    def forward(self, x):
        """Enhanced forward with robust dimension handling"""
        if len(x.shape) == 4:
            # Single frame: (B, C, H, W)
            B, C, H, W = x.shape
            T = 1
            x = x.unsqueeze(1)  # (B, 1, C, H, W)
        else:
            # Video sequence: (B, T, C, H, W)
            B, T, C, H, W = x.shape
        
        # Validate input dimensions
        if C != self.in_chans:
            raise ValueError(f"Input channels {C} != expected {self.in_chans}")
        
        # Process each frame - flatten to process in batches
        x = x.flatten(0, 1)  # (B*T, C, H, W)
        
        # Project patches with error handling
        try:
            x = self.proj(x)  # (B*T, D, H', W')
        except RuntimeError as e:
            logger.error(f"Patch projection failed: {e}, input shape: {x.shape}")
            raise
        
        # Reshape to patches
        D, H_p, W_p = x.shape[1], x.shape[2], x.shape[3]
        x = x.reshape(B, T, D, H_p, W_p)
        x = x.permute(0, 1, 3, 4, 2)  # (B, T, H', W', D)
        x = x.reshape(B, T, H_p*W_p, D)  # (B, T, N, D)
        
        # Layer normalization with gradient clipping
        x = self.norm(x)
        
        return x


class WindowAttention(nn.Module):
    """Enhanced window attention with improved numerical stability"""
    
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads

        # Ensure head_dim is valid (dim must be divisible by num_heads)
        if dim % num_heads != 0:
            logger.warning(f"dim ({dim}) not divisible by num_heads ({num_heads}), adjusting num_heads")
            # Find the largest divisor of dim that is <= num_heads
            for h in range(num_heads, 0, -1):
                if dim % h == 0:
                    self.num_heads = h
                    break
            logger.info(f"Adjusted num_heads from {num_heads} to {self.num_heads}")

        head_dim = dim // self.num_heads
        self.scale = head_dim ** -0.5
        
        # Enhanced relative position bias with better initialization
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))
        
        # Compute relative position index with safe operations
        coords_h = torch.arange(self.window_size[0], dtype=torch.long)
        coords_w = torch.arange(self.window_size[1], dtype=torch.long)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        
        # Safe arithmetic operations to prevent overflow
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)
        
        # Ensure index is within safe range
        max_index = (2 * window_size[0] - 1) * (2 * window_size[1] - 1) - 1
        relative_position_index = torch.clamp(relative_position_index, 0, max_index)
        
        self.register_buffer("relative_position_index", relative_position_index)
        
        self.qkv = nn.Linear(dim, self.num_heads * head_dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(self.num_heads * head_dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
        
        # Better initialization
        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        nn.init.xavier_uniform_(self.qkv.weight)
        nn.init.xavier_uniform_(self.proj.weight)
        
        self.softmax = nn.Softmax(dim=-1)
        
        # Cache for formatted bias
        self.register_buffer('formatted_rel_pos_bias', None, persistent=False)
        self.cached_window_size = window_size
    
    def _get_rel_pos_bias(self):
        """Get formatted relative position bias with enhanced stability"""
        recompute_needed = torch.is_grad_enabled() and self.training
        if self.formatted_rel_pos_bias is None or self.cached_window_size != self.window_size or recompute_needed:
            self.cached_window_size = self.window_size
            
            # Safe indexing to prevent overflow
            index_flattened = self.relative_position_index.view(-1)
            index_clamped = torch.clamp(index_flattened, 0, self.relative_position_bias_table.shape[0] - 1)
            
            rel_pos_bias = self.relative_position_bias_table[index_clamped]
            rel_pos_bias = rel_pos_bias.view(
                self.window_size[0] * self.window_size[1],
                self.window_size[0] * self.window_size[1],
                self.num_heads
            )
            rel_pos_bias = rel_pos_bias.permute(2, 0, 1).contiguous()
            
            # Enhanced numerical stability with adaptive clipping
            rel_pos_bias = torch.clamp(rel_pos_bias, min=-10.0, max=10.0)
            
            if not recompute_needed:
                self.formatted_rel_pos_bias = rel_pos_bias.detach()
            
            return rel_pos_bias
        
        return self.formatted_rel_pos_bias
    
    def forward(self, x, mask=None):
        """Enhanced forward with better numerical stability"""
        B_, N, C = x.shape

        # QKV projection with error handling
        try:
            head_dim = C // self.num_heads
            qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, head_dim).permute(2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[2]
        except RuntimeError as e:
            logger.error(f"QKV projection failed: {e}, input shape: {x.shape}")
            logger.error(f"Expected QKV output shape: {(B_, N, 3, self.num_heads, C // self.num_heads)}")
            logger.error(f"Actual QKV output shape: {self.qkv(x).shape}")
            raise
        
        # Scaled dot-product attention with numerical stability
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        
        # Check for numerical issues
        if torch.isnan(attn).any() or torch.isinf(attn).any():
            logger.warning("NaN/Inf detected in attention scores, applying correction")
            attn = torch.nan_to_num(attn, nan=0.0, posinf=5.0, neginf=-5.0)
        
        # Add relative position bias
        rel_pos_bias = self._get_rel_pos_bias()
        attn = attn + rel_pos_bias.unsqueeze(0)
        
        # Apply mask if provided
        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
        
        # Enhanced softmax with stability
        attn = torch.clamp(attn, min=-50.0, max=50.0)  # Prevent overflow
        attn = self.softmax(attn)
        
        # Check attention weights
        if torch.isnan(attn).any() or torch.isinf(attn).any():
            logger.warning("NaN/Inf in attention weights, using uniform weights")
            attn = torch.ones_like(attn) / N
        
        attn = self.attn_drop(attn)
        
        # Output projection
        head_dim = C // self.num_heads
        x = (attn @ v).transpose(1, 2).reshape(B_, N, self.num_heads * head_dim)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


def window_partition(x, window_size):
    """Enhanced window partition with better padding"""
    B, H, W, C = x.shape
    
    # Ensure dimensions are compatible
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    
    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h), mode='reflect')
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W
    
    x = x.view(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """Enhanced window reverse with proper unpadding"""
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w
    
    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.view(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_padded, W_padded, -1)
    
    # Remove padding
    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]
    
    return x


class TemporalSeparableAttention(nn.Module):
    """Enhanced temporal separable attention with improved stability"""
    
    def __init__(self, dim, num_heads, window_size=8, qkv_bias=False, dropout=0.):
        super().__init__()
        self.dim = dim

        # Ensure num_heads is compatible with dim
        if dim % num_heads != 0:
            logger.warning(f"TemporalSeparableAttention: dim ({dim}) not divisible by num_heads ({num_heads}), adjusting num_heads")
            for h in range(num_heads, 0, -1):
                if dim % h == 0:
                    num_heads = h
                    break
            logger.info(f"TemporalSeparableAttention: Adjusted num_heads to {num_heads}")

        self.num_heads = num_heads
        
        # Handle window size parameter
        if isinstance(window_size, (list, tuple)):
            spatial_window_size = window_size[1] if len(window_size) > 1 else window_size[0]
        else:
            spatial_window_size = window_size
        self.window_size = spatial_window_size
        
        # Enhanced spatial attention
        self.spatial_attn = WindowAttention(
            dim=dim,
            window_size=(self.window_size, self.window_size),
            num_heads=self.num_heads,
            qkv_bias=qkv_bias,
            attn_drop=dropout,
            proj_drop=dropout
        )
        
        # Enhanced temporal attention
        self.temporal_attn = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=self.num_heads,
            dropout=dropout,
            batch_first=True,
        )
        
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(dropout)
        
        # Initialize weights
        nn.init.xavier_uniform_(self.proj.weight)
    
    def forward(self, x, T, H, W):
        """Enhanced forward with dimension validation"""
        B, L, C = x.shape
        expected_L = T * H * W
        
        if L != expected_L:
            logger.warning(f"Dimension mismatch: L={L}, expected T*H*W={expected_L}")
            # Adjust T to match actual dimensions
            T = max(1, L // (H * W))
            if T * H * W != L:
                raise ValueError(f"Cannot reshape L={L} into T*H*W with H={H}, W={W}")
        
        # Spatial attention phase
        x_spatial = rearrange(x, 'b (t h w) c -> (b t) h w c', t=T, h=H, w=W)
        BT, H_s, W_s, C_s = x_spatial.shape
        
        # Window partition and attention
        x_windows = window_partition(x_spatial, self.window_size)
        x_windows = x_windows.view(-1, self.window_size * self.window_size, C)
        
        # Apply spatial attention
        attn_windows = self.spatial_attn(x_windows)
        
        # Reverse window partition
        attn_windows = attn_windows.view(-1, self.window_size, self.window_size, C)
        x_spatial = window_reverse(attn_windows, self.window_size, H, W)
        x_spatial = x_spatial.reshape(BT, H * W, C)
        
        # Temporal attention phase
        x_temporal = rearrange(x_spatial, '(b t) (h w) c -> (b h w) t c', b=B, t=T, h=H, w=W)
        
        # Apply temporal attention with error handling
        try:
            x_temporal, _ = self.temporal_attn(x_temporal, x_temporal, x_temporal)
        except RuntimeError as e:
            logger.error(f"Temporal attention failed: {e}")
            # Fallback: identity operation
            pass
        
        # Reshape back
        x_out = rearrange(x_temporal, '(b h w) t c -> b (t h w) c', b=B, h=H, w=W)
        
        # Final projection
        x_out = self.proj(x_out)
        x_out = self.proj_drop(x_out)
        
        return x_out


class MemoryEfficientBlock(nn.Module):
    """Enhanced memory efficient block with better stability"""
    
    def __init__(self,
                 dim,
                 num_heads,
                 mlp_ratio=4.,
                 qkv_bias=False,
                 drop=0.,
                 attn_drop=0.,
                 drop_path=0.,
                 act_layer=nn.GELU,
                 norm_layer=nn.LayerNorm,
                 use_checkpoint=True,
                 window_size=8):
        super().__init__()
        self.use_checkpoint = use_checkpoint
        self.norm1 = norm_layer(dim)
        
        # Enhanced attention
        self.attn = TemporalSeparableAttention(
            dim=dim,
            num_heads=num_heads,
            qkv_bias=qkv_bias,
            dropout=attn_drop,
            window_size=window_size,
        )
        
        # Stochastic depth
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)
        
        # Enhanced MLP
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            act_layer(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )
        
        # Initialize MLP weights
        for module in self.mlp:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
    
    def _forward_impl(self, x, T, H, W):
        """Implementation with residual connections"""
        # Attention with residual
        x = x + self.drop_path(self.attn(self.norm1(x), T, H, W))
        
        # MLP with residual
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        
        return x
    
    def forward(self, x, T, H, W):
        """Forward with optional gradient checkpointing"""
        if self.use_checkpoint and self.training:
            return torch.utils.checkpoint.checkpoint(
                self._forward_impl, x, T, H, W, use_reentrant=False
            )
        else:
            return self._forward_impl(x, T, H, W)


class TemporalDownsample(nn.Module):
    """Enhanced temporal downsampling with robust dimension handling"""
    
    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        self.maxpool = nn.MaxPool3d(kernel_size=(2,1,1), stride=(2,1,1))
        
    def forward(self, x, T, H, W):
        """Enhanced forward with better error handling"""
        B, L, C = x.shape
        expected_L = T * H * W
        
        # Robust dimension adjustment
        if L != expected_L:
            logger.warning(f"TemporalDownsample: L ({L}) != T*H*W ({expected_L})")
            # Find best fit for T
            if L % (H * W) == 0:
                T_adjusted = L // (H * W)
                logger.info(f"Adjusted T from {T} to {T_adjusted}")
                T = T_adjusted
            else:
                # Truncate to fit
                L_adjusted = (L // (H * W)) * H * W
                x = x[:, :L_adjusted, :]
                T = L_adjusted // (H * W)
                logger.info(f"Truncated L from {L} to {L_adjusted}, T={T}")
        
        # Reshape and apply temporal downsampling
        x = x.reshape(B, T, H, W, C).permute(0, 4, 1, 2, 3)  # B, C, T, H, W
        
        # Apply downsampling with bounds checking
        if T >= 2:
            x = self.maxpool(x)
            new_T = T // 2
        else:
            logger.warning(f"Cannot downsample T={T}, keeping original")
            new_T = T
        
        # Reshape back
        x = x.permute(0, 2, 3, 4, 1)  # B, T//2, H, W, C
        new_L = new_T * H * W
        x = x.reshape(B, new_L, C)
        
        return x, new_T


class MultiScaleEncoder(nn.Module):
    """Enhanced multi-scale encoder with improved feature fusion"""
    
    def __init__(self,
                 embed_dim=128,
                 depths=[2, 2, 6, 2],
                 num_heads=[4, 8, 16, 32],
                 window_sizes=[8, 8, 8, 8],
                 mlp_ratio=4.,
                 qkv_bias=False,
                 drop_rate=0.,
                 attn_drop_rate=0.,
                 drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm,
                 use_checkpoint=True):
        super().__init__()
        
        self.num_layers = len(depths)
        self.embed_dim = embed_dim
        
        # Stochastic depth decay
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        # Build layers
        self.layers = nn.ModuleList()
        self.temporal_downsamples = nn.ModuleList()
        self.spatial_downsamples = nn.ModuleList()
        self.fusion_projections = nn.ModuleList()
        
        curr_dim = embed_dim
        
        for i_layer in range(self.num_layers):
            layer_depth = depths[i_layer]
            layer_dim = curr_dim
            layer_num_heads = num_heads[i_layer]

            # Ensure num_heads is compatible with layer_dim
            if layer_dim % layer_num_heads != 0:
                for h in range(layer_num_heads, 0, -1):
                    if layer_dim % h == 0:
                        logger.warning(f"Layer {i_layer}: Adjusted num_heads from {layer_num_heads} to {h} for dim {layer_dim}")
                        layer_num_heads = h
                        break
            
            # Build blocks
            blocks = nn.ModuleList([
                MemoryEfficientBlock(
                    dim=layer_dim,
                    num_heads=layer_num_heads,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    drop=drop_rate,
                    attn_drop=attn_drop_rate,
                    drop_path=dpr[sum(depths[:i_layer]) + i_block],
                    norm_layer=norm_layer,
                    use_checkpoint=use_checkpoint,
                    window_size=window_sizes[i_layer] if i_layer < len(window_sizes) else 8,
                )
                for i_block in range(layer_depth)
            ])
            self.layers.append(blocks)
            
            # Downsampling layers
            if i_layer < self.num_layers - 1:
                next_dim = curr_dim * 2
                
                # Fusion projection
                self.fusion_projections.append(nn.Linear(self.embed_dim, next_dim))
                
                # Temporal downsampling
                self.temporal_downsamples.append(TemporalDownsample(curr_dim))
                
                # Spatial downsampling
                self.spatial_downsamples.append(nn.Linear(curr_dim, next_dim))
                
                curr_dim = next_dim
    
    def forward(self, x_pyramid, T_pyramid, H, W):
        """Enhanced forward with robust feature fusion"""
        B, _, C = x_pyramid[0].shape
        
        features = []
        projected_scales = {}
        
        # Process first scale
        x = x_pyramid[0]
        T = T_pyramid[0]
        real_T_values = [T]
        
        # Pre-process other scales with error handling
        for i in range(1, min(self.num_layers, len(x_pyramid))):
            try:
                x_scale = x_pyramid[i]
                T_scale = T_pyramid[i]
                
                # Validate dimensions
                if x_scale.shape[0] != B:
                    logger.warning(f"Batch size mismatch in scale {i}: {x_scale.shape[0]} vs {B}")
                    continue
                
                x_proj = self.fusion_projections[i-1](x_scale)
                projected_scales[i] = {'features': x_proj, 'T': T_scale}
                
            except Exception as e:
                logger.error(f"Error processing scale {i}: {e}")
                continue
        
        # Process each layer
        for i_layer, blocks in enumerate(self.layers):
            # Feature fusion with error handling
            if i_layer > 0 and i_layer in projected_scales:
                try:
                    x_other_scale = projected_scales[i_layer]['features']
                    T_other = projected_scales[i_layer]['T']
                    
                    # Dimension matching with interpolation
                    if T != T_other:
                        x_other_scale = rearrange(x_other_scale, 'b (t h w) c -> b c t h w', 
                                                t=T_other, h=H, w=W)
                        x_other_scale = F.interpolate(x_other_scale, size=(T, H, W), 
                                                    mode='trilinear', align_corners=False)
                        x_other_scale = rearrange(x_other_scale, 'b c t h w -> b (t h w) c')
                    
                    x = x + x_other_scale
                    
                except Exception as e:
                    logger.warning(f"Feature fusion failed at layer {i_layer}: {e}")
            
            # Process blocks
            for block in blocks:
                try:
                    x = block(x, T, H, W)
                except Exception as e:
                    logger.error(f"Block processing failed at layer {i_layer}: {e}")
                    raise
            
            features.append(x)
            
            # Apply downsampling
            if i_layer < self.num_layers - 1:
                x, T = self.temporal_downsamples[i_layer](x, T, H, W)
                real_T_values.append(T)
                x = self.spatial_downsamples[i_layer](x)
        
        return features, real_T_values


class GeographicTemporalEmbedding(nn.Module):
    """Enhanced geographic temporal embedding with overflow protection"""
    
    def __init__(self, embed_dim=128, num_freq_bands=6):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_freq_bands = num_freq_bands
        
        # Enhanced Fourier feature encoding
        self.register_buffer(
            'freq_bands', 
            2.0 ** torch.linspace(0, num_freq_bands-1, num_freq_bands, dtype=torch.float32)
        )
        
        # Feature dimensions
        geo_fourier_dim = 4 * num_freq_bands
        temporal_fourier_dim = 2 * num_freq_bands
        year_embed_dim = 16
        
        # Enhanced year embedding with safe range
        self.year_embed = nn.Embedding(200, year_embed_dim)  # Reduced range to prevent overflow
        
        # Enhanced projector with residual connection
        total_dim = geo_fourier_dim + temporal_fourier_dim + year_embed_dim
        self.projector = nn.Sequential(
            nn.Linear(total_dim, embed_dim * 2),
            nn.LayerNorm(embed_dim * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.LayerNorm(embed_dim),
        )
        
        # Enhanced modulation
        self.scale_shift = nn.Linear(embed_dim, embed_dim * 2)
        
        # Initialize weights
        for module in self.projector:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
    
    def forward(self, lon, lat, year, month):
        """Enhanced forward with safe conversion and overflow protection"""
        B = lon.shape[0]
        device = lon.device
        
        # Enhanced input validation and clamping
        lon = torch.clamp(lon.float(), -180.0, 180.0)
        lat = torch.clamp(lat.float(), -90.0, 90.0)
        year = torch.clamp(year.float(), 1900, 2099)  # Safe range
        month = torch.clamp(month.float(), 1, 12)
        
        # Geographic Fourier features with enhanced stability
        try:
            lon_scaled = lon / 180.0 * math.pi
            lat_scaled = lat / 90.0 * math.pi
            
            geo_features = []
            for freq in self.freq_bands:
                geo_features.extend([
                    torch.sin(freq * lon_scaled),
                    torch.cos(freq * lon_scaled),
                    torch.sin(freq * lat_scaled),
                    torch.cos(freq * lat_scaled)
                ])
            geo_features = torch.stack(geo_features, dim=-1)
            
        except Exception as e:
            logger.error(f"Geographic feature computation failed: {e}")
            geo_features = torch.zeros(B, 4 * self.num_freq_bands, device=device)
        
        # Temporal Fourier features
        try:
            month_angle = (month - 1) / 12.0 * 2 * math.pi
            temporal_features = []
            for freq in self.freq_bands:
                temporal_features.extend([
                    torch.sin(freq * month_angle),
                    torch.cos(freq * month_angle)
                ])
            temporal_features = torch.stack(temporal_features, dim=-1)
            
        except Exception as e:
            logger.error(f"Temporal feature computation failed: {e}")
            temporal_features = torch.zeros(B, 2 * self.num_freq_bands, device=device)
        
        # Year embedding with safe conversion
        try:
            year_idx = safe_long_conversion(year - 1900, 0, 199, 50)  # Safe fallback to middle value
            year_features = self.year_embed(year_idx)
            
        except Exception as e:
            logger.error(f"Year embedding failed: {e}")
            year_features = torch.zeros(B, 16, device=device)
        
        # Combine features
        combined = torch.cat([geo_features, temporal_features, year_features], dim=-1)
        
        # Project with error handling
        try:
            embedding = self.projector(combined)
        except Exception as e:
            logger.error(f"Embedding projection failed: {e}")
            embedding = torch.zeros(B, self.embed_dim, device=device)
        
        return embedding
    
    def modulate(self, x):
        """Enhanced modulation with stability"""
        try:
            scale_shift = self.scale_shift(x)
            scale, shift = scale_shift.chunk(2, dim=-1)
            
            # Enhanced stability
            scale = 1 + 0.1 * torch.tanh(scale)
            shift = 0.1 * torch.tanh(shift)
            
            return scale, shift
            
        except Exception as e:
            logger.error(f"Modulation failed: {e}")
            return torch.ones_like(x), torch.zeros_like(x)


class GeographicTemporalModulator(GeographicTemporalEmbedding):
    """Enhanced compatibility alias with safe conversion"""

    def encode_features(self, lon, lat, year, month):
        """Enhanced encoding with input validation and safe conversion"""
        # Convert inputs to proper tensors with safe conversion
        if not isinstance(lon, torch.Tensor):
            lon = torch.tensor(float(lon), dtype=torch.float32)
        if not isinstance(lat, torch.Tensor):
            lat = torch.tensor(float(lat), dtype=torch.float32)
        if not isinstance(year, torch.Tensor):
            year = torch.tensor(float(year), dtype=torch.float32)
        if not isinstance(month, torch.Tensor):
            month = torch.tensor(float(month), dtype=torch.float32)
        
        return super().forward(lon, lat, year, month)


class OptimizedSwinWaterNet(nn.Module):
    """Enhanced SwinWater v5.1.1 with int64 overflow protection"""
    
    def __init__(
        self,
        img_size=256,
        patch_size=4,
        in_chans=2,
        out_chans=1,
        num_frames=120,
        num_temporal_scales=3,
        base_keep_rate=0.25,
        frame_counts=None,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        num_heads=[3, 6, 12, 24],
        window_size=7,
        mlp_ratio=4.,
        qkv_bias=True,
        qk_scale=None,
        drop_rate=0.,
        attn_drop_rate=0.,
        drop_path_rate=0.1,
        norm_layer=nn.LayerNorm,
        patch_norm=True,
        use_checkpoint=False,
        confidence_channels=1
    ):
        super().__init__()
        
        # Store configuration
        self.num_frames = num_frames
        self.num_temporal_scales = num_temporal_scales
        self.base_keep_rate = base_keep_rate
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_heads = num_heads
        self.window_size = window_size
        self.use_checkpoint = use_checkpoint
        
        # Enhanced debug mode
        self.debug_dimensions = False
        
        # Enhanced patch embedding
        self.patch_embed = EfficientPatchEmbed(
            img_size=self.img_size,
            patch_size=self.patch_size,
            in_chans=self.in_chans,
            embed_dim=self.embed_dim
        )
        
        # Enhanced temporal attention modules
        self.temporal_attention = nn.ModuleDict({
            f'scale_{i}': TemporalAttentionModule(
                embed_dim=self.embed_dim,
                num_heads=self.num_heads[0],
                max_frames=min(48, int(self.num_frames * (self.base_keep_rate ** i))),
                use_gru=True
            )
            for i in range(self.num_temporal_scales)
        })
        
        # Enhanced multi-scale encoder
        self.encoder = MultiScaleEncoder(
            embed_dim=self.embed_dim,
            depths=self.depths,
            num_heads=self.num_heads,
            window_sizes=[self.window_size] * len(self.depths),
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            drop_rate=drop_rate,
            attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate,
            use_checkpoint=self.use_checkpoint
        )
        
        # Enhanced geographic temporal encoder
        self.geo_temporal_encoder = GeographicTemporalEmbedding(
            embed_dim=self.embed_dim,
            num_freq_bands=8  # Increased for better representation
        )
        
        # Enhanced dimension adapter
        final_dim = self.embed_dim * (2 ** (len(self.depths) - 1))
        self.target_dim_adapter = nn.Linear(self.embed_dim, final_dim)
        
        # Enhanced decoder
        self.decoder = MemoryEfficientDecoder(
            in_channels=final_dim,
            img_size=self.img_size,
            patch_size=self.patch_size,
            num_classes=self.out_chans,
            use_checkpoint=self.use_checkpoint
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
        logger.info(f"Initialized OptimizedSwinWaterNet v5.1.1 with {sum(p.numel() for p in self.parameters())} parameters")
    
    def _init_weights(self, m):
        """Enhanced weight initialization"""
        if isinstance(m, nn.Linear):
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
    
    def _verify_dimensions(self, feat, T, H, W, stage_name=""):
        """Enhanced dimension verification"""
        if not isinstance(feat, torch.Tensor):
            return False
        
        B, L, D = feat.shape
        expected_L = T * H * W
        
        if L != expected_L:
            msg = f"{stage_name}: Dimension mismatch L={L} != T*H*W={T}*{H}*{W}={expected_L}"
            if self.debug_dimensions:
                logger.warning(msg)
            return False
        
        return True
    
    def _three_segment_temporal_sampling(self, x, center_frame_idx, segment_size=16):
        """Enhanced three-segment sampling with overflow protection"""
        B, T, C, H, W = x.shape
        device = x.device
        
        # Safe conversion and validation
        center_frame_idx = safe_long_conversion(center_frame_idx, 0, T-1, T//2)
        segment_size = min(segment_size, T // 3)  # Ensure we have enough frames
        
        if segment_size <= 0:
            segment_size = max(1, T // 3)
        
        # Enhanced vectorized sampling with safe operations
        frame_indices = torch.arange(T, device=device, dtype=torch.long).unsqueeze(0).expand(B, -1)
        center_idx = center_frame_idx.unsqueeze(1)
        
        # Compute distances with safe operations
        dist = torch.abs(frame_indices - center_idx).float()  # Use float to prevent overflow
        sorted_dist, sorted_idx = torch.sort(dist, dim=1)
        
        # Select segments with safe indexing
        seg0_idx = sorted_idx[:, :segment_size]
        
        # Mask for second segment
        mask0 = torch.zeros((B, T), dtype=torch.bool, device=device)
        mask0.scatter_(1, seg0_idx, True)
        
        dist1 = dist.masked_fill(mask0, float('inf'))
        sorted_dist1, sorted_idx1 = torch.sort(dist1, dim=1)
        seg1_idx = sorted_idx1[:, :segment_size]
        
        # Mask for third segment
        mask1 = mask0.clone()
        mask1.scatter_(1, seg1_idx, True)
        
        dist2 = dist.masked_fill(mask1, float('-inf'))
        sorted_dist2, sorted_idx2 = torch.sort(dist2, dim=1, descending=True)
        seg2_idx = sorted_idx2[:, :segment_size]
        
        # Gather segments with safe indexing
        batch_idx = torch.arange(B, device=device, dtype=torch.long).unsqueeze(1).expand(-1, segment_size)
        
        try:
            # Safe indexing with bounds checking
            seg0_idx = torch.clamp(seg0_idx, 0, T-1)
            seg1_idx = torch.clamp(seg1_idx, 0, T-1)
            seg2_idx = torch.clamp(seg2_idx, 0, T-1)
            
            seg0 = x[batch_idx, seg0_idx]
            seg1 = x[batch_idx, seg1_idx]
            seg2 = x[batch_idx, seg2_idx]
            
            return [seg0, seg1, seg2]
            
        except Exception as e:
            logger.error(f"Segment gathering failed: {e}")
            # Fallback: simple uniform sampling
            step = max(1, T // (segment_size * 3))
            indices = torch.arange(0, T, step, dtype=torch.long, device=device)[:segment_size*3]
            if len(indices) < segment_size * 3:
                # Pad if needed
                pad_length = segment_size * 3 - len(indices)
                indices = torch.cat([indices, indices[-1:].repeat(pad_length)])
            
            seg0 = x[:, indices[:segment_size]]
            seg1 = x[:, indices[segment_size:segment_size*2]]
            seg2 = x[:, indices[segment_size*2:segment_size*3]]
            return [seg0, seg1, seg2]
    
    def forward(self, batch):
        """Enhanced forward pass with comprehensive overflow protection"""
        try:
            # Extract and validate inputs
            x = batch['input_sequence']
            center_frame_idx = batch.get('center_frame_idx', None)
            
            B, T, C, H, W = x.shape
            device = x.device
            
            # Safe validation and conversion of center frame index
            if center_frame_idx is None:
                center_frame_idx = torch.full((B,), T // 2, device=device, dtype=torch.long)
            elif isinstance(center_frame_idx, (int, float)):
                center_frame_idx = torch.full((B,), int(center_frame_idx), device=device, dtype=torch.long)
            elif center_frame_idx.numel() == 1 and B > 1:
                center_frame_idx = center_frame_idx.expand(B)
            
            # Safe conversion with overflow protection
            center_frame_idx = safe_long_conversion(center_frame_idx, 0, T-1, T//2)
            
            # Extract target frames with safe indexing
            batch_indices = torch.arange(B, device=device, dtype=torch.long)
            target_frames = x[batch_indices, center_frame_idx]  # (B, C, H, W)
            
            # Target frame embedding
            target_patches = self.patch_embed(target_frames.unsqueeze(1))  # (B, 1, N, D)
            target_features = target_patches.squeeze(1)  # (B, N, D)
            
            # Get water frequency if available
            water_frequency = batch.get('occurrence', None)
            
            # Multi-scale temporal sampling
            multi_scale_x = self._three_segment_temporal_sampling(x, center_frame_idx)
            
            # Process multi-scale features
            multi_scale_features = []
            multi_scale_T = []
            
            for scale_idx, scale_x in enumerate(multi_scale_x):
                B_s, T_s, C_s, H_s, W_s = scale_x.shape
                
                # Patch embedding
                x_embed = self.patch_embed(scale_x)  # (B, T', N, D)
                B_e, T_e, N_e, D_e = x_embed.shape
                
                # Apply temporal attention
                temporal_attn_module = self.temporal_attention[f'scale_{scale_idx}']
                x_weighted = temporal_attn_module(x_embed, target_features, water_frequency)
                
                # Flatten for encoder
                x_flat = x_weighted.reshape(B_e, T_e * N_e, D_e)
                multi_scale_features.append(x_flat)
                multi_scale_T.append(T_e)
            
            # Geographic temporal encoding
            geo_temporal_encoding = self.geo_temporal_encoder(
                batch['tile_lon'], 
                batch['tile_lat'],
                batch['year'],
                batch['month']
            )
            
            # Add geographic temporal encoding
            for i in range(len(multi_scale_features)):
                B_f, L_f, D_f = multi_scale_features[i].shape
                geo_encoding_expanded = geo_temporal_encoding.unsqueeze(1).expand(B_f, L_f, D_f)
                multi_scale_features[i] = multi_scale_features[i] + 0.1 * geo_encoding_expanded
            
            # Multi-scale encoding
            H_p = W_p = self.img_size // self.patch_size
            encoder_features, real_T_values = self.encoder(multi_scale_features, multi_scale_T, H_p, W_p)
            
            # Extract center frame features with safe operations
            selected_features = []
            for i, feat in enumerate(encoder_features):
                B_f, L_f, D_f = feat.shape
                
                # Get temporal dimension with safe operations
                T_feat = real_T_values[i] if i < len(real_T_values) else 1
                T_feat = max(1, T_feat)  # Ensure positive
                
                # Calculate spatial tokens per frame
                if L_f % T_feat == 0:
                    N_feat = L_f // T_feat
                else:
                    # Adjust temporal dimension
                    T_feat = max(1, L_f // (H_p * W_p))
                    N_feat = H_p * W_p
                    if T_feat * N_feat != L_f:
                        logger.warning(f"Cannot properly reshape layer {i}")
                        N_feat = max(1, L_f // T_feat)
                
                # Safe center frame index calculation
                feat_center_idx = safe_long_conversion(center_frame_idx, 0, T_feat-1, T_feat//2)
                start_indices = feat_center_idx * N_feat
                start_indices = torch.clamp(start_indices, 0, max(0, L_f - N_feat))
                
                # Safe batch indexing
                batch_indices = torch.arange(B_f, device=device, dtype=torch.long).unsqueeze(1)
                token_indices = torch.arange(N_feat, device=device, dtype=torch.long).unsqueeze(0)
                indices = start_indices.unsqueeze(1) + token_indices
                indices = torch.clamp(indices, 0, L_f - 1)  # Ensure valid indices
                
                center_feats = feat[batch_indices, indices]
                selected_features.append(center_feats)
            
            # Decode final features
            final_features = selected_features[-1]
            B_f, N_f, D_f = final_features.shape
            
            # Reshape for decoder with safe operations
            target_h = target_w = self.img_size // self.patch_size
            target_patches = target_h * target_w
            
            if N_f != target_patches:
                # Safe reshaping with overflow protection
                sqrt_n = int(math.sqrt(max(1, N_f)))
                best_h = best_w = sqrt_n
                
                # Find better factorization safely
                for h in range(max(1, sqrt_n-3), sqrt_n+4):
                    if h > 0 and N_f % h == 0:
                        w = N_f // h
                        if w > 0 and abs(h - sqrt_n) + abs(w - sqrt_n) < abs(best_h - sqrt_n) + abs(best_w - sqrt_n):
                            best_h, best_w = h, w
                
                # Safe reshape and interpolate
                features_spatial = final_features.permute(0, 2, 1).reshape(B_f, D_f, best_h, best_w)
                decoder_input = F.interpolate(
                    features_spatial,
                    size=(target_h, target_w),
                    mode='bilinear',
                    align_corners=False
                )
            else:
                decoder_input = final_features.permute(0, 2, 1).reshape(B_f, D_f, target_h, target_w)
            
            # Decode
            logits, confidence = self.decoder(
                decoder_input,
                selected_features[:-1],
                None,
                water_frequency
            )
            
            return {
                'inpaint': {
                    'logits': logits,
                    'confidence': confidence
                }
            }
            
        except Exception as e:
            logger.error(f"Forward pass failed: {e}")
            # Return dummy output to prevent training crash
            B = batch['input_sequence'].shape[0] if 'input_sequence' in batch else 1
            device = batch['input_sequence'].device if 'input_sequence' in batch else torch.device('cpu')
            
            dummy_logits = torch.zeros(B, self.out_chans, self.img_size, self.img_size, 
                                     device=device, requires_grad=True)
            dummy_confidence = torch.ones(B, 1, self.img_size, self.img_size, 
                                        device=device, requires_grad=True) * 0.5
            
            return {
                'inpaint': {
                    'logits': dummy_logits,
                    'confidence': dummy_confidence
                }
            }


class TemporalAttentionModule(nn.Module):
    """Enhanced temporal attention module with overflow protection"""
    
    def __init__(self, embed_dim, num_heads, max_frames=32, use_gru=True):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.max_frames = max_frames
        self.use_gru = use_gru
        
        # Enhanced projections with better initialization
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        
        # Enhanced positional embedding
        self.pos_embed = nn.Parameter(torch.randn(1, max_frames, embed_dim) * 0.02)
        
        # Enhanced GRU with residual connections
        if self.use_gru:
            self.gru_layer1 = nn.GRU(embed_dim, embed_dim, num_layers=1, batch_first=True)
            self.gru_layer2 = nn.GRU(embed_dim, embed_dim, num_layers=1, batch_first=True)
            self.gru_residual_proj = nn.Linear(embed_dim, embed_dim)
            self.gru_norm1 = nn.LayerNorm(embed_dim)
            self.gru_norm2 = nn.LayerNorm(embed_dim)
            
            # Enhanced memory management
            self.register_buffer('memory_cache_layer1', torch.zeros(1, 1, embed_dim))
            self.register_buffer('memory_cache_layer2', torch.zeros(1, 1, embed_dim))
            self.memory_decay = 0.95
        
        self.dynamic_attention_beta = 2.0
        
        # Enhanced output projection
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        self.norm = nn.LayerNorm(embed_dim)
        
        # Initialize weights
        for module in [self.q_proj, self.k_proj, self.v_proj, self.out_proj]:
            nn.init.xavier_uniform_(module.weight)
        
        if self.use_gru:
            nn.init.xavier_uniform_(self.gru_residual_proj.weight)
    
    def reset_memory_cache(self):
        """Enhanced memory reset"""
        if self.use_gru:
            self.memory_cache_layer1.zero_()
            self.memory_cache_layer2.zero_()
    
    def get_memory_info(self):
        """Enhanced memory info"""
        if not self.use_gru:
            return {"memory_enabled": False}
        
        return {
            "memory_enabled": True,
            "memory_decay": self.memory_decay,
            "layer1_norm": torch.norm(self.memory_cache_layer1).item(),
            "layer2_norm": torch.norm(self.memory_cache_layer2).item(),
            "dynamic_beta": self.dynamic_attention_beta
        }
    
    def forward(self, x, target_features, water_frequency=None):
        """Enhanced forward with comprehensive overflow protection"""
        B, T, N, D = x.shape
        
        # Enhanced positional encoding with safe operations
        try:
            if T <= self.max_frames:
                pos_embed = self.pos_embed[:, :T, :].unsqueeze(2)
            else:
                pos_embed = F.interpolate(
                    self.pos_embed.transpose(1, 2),
                    size=T,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2).unsqueeze(2)
            
            x_with_pos = x + pos_embed
            
        except Exception as e:
            logger.warning(f"Positional encoding failed: {e}")
            x_with_pos = x
        
        # Enhanced dynamic region computation
        dynamic_mask = torch.full((B, N), 0.5, device=x.device, dtype=x.dtype)
        dynamic_score = torch.full((B, N), 0.5, device=x.device, dtype=x.dtype)
        
        if water_frequency is not None and self.use_gru:
            try:
                H_p = W_p = int(math.sqrt(max(1, N)))
                freq_reshaped = F.interpolate(
                    water_frequency.unsqueeze(1),
                    size=(H_p, W_p),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(1)
                
                patch_frequencies = freq_reshaped.reshape(B, -1)
                patch_frequencies_reshaped = patch_frequencies.reshape(B, H_p, W_p)
                dynamic_degree = compute_dynamic_degree(patch_frequencies_reshaped)
                dynamic_score = dynamic_degree.reshape(B, -1)
                dynamic_mask = torch.sigmoid(8 * (dynamic_score - 0.5))
                
            except Exception as e:
                logger.warning(f"Dynamic region computation failed: {e}")
        
        # Enhanced GRU processing with safe operations
        if self.use_gru:
            try:
                # Safe reshaping
                total_elements = B * N
                if total_elements > 1000000:  # Prevent memory issues
                    logger.warning(f"Large tensor size: {total_elements}, using chunked processing")
                    
                x_reshaped = x_with_pos.permute(0, 2, 1, 3).reshape(B*N, T, D)
                
                # Layer 1 GRU
                h0_layer1 = self.memory_cache_layer1.expand(1, B*N, self.embed_dim).contiguous()
                gru_out1, h1_layer1 = self.gru_layer1(x_reshaped, h0_layer1)
                gru_out1 = self.gru_norm1(gru_out1)
                
                # Layer 2 GRU
                h0_layer2 = self.memory_cache_layer2.expand(1, B*N, self.embed_dim).contiguous()
                gru_out2, h1_layer2 = self.gru_layer2(gru_out1, h0_layer2)
                gru_out2 = self.gru_norm2(gru_out2)
                
                # Residual connection
                residual_input = self.gru_residual_proj(x_reshaped)
                gru_final = residual_input + 0.3 * gru_out1 + 0.7 * gru_out2
                
                # Update memory with enhanced stability and overflow protection
                with torch.no_grad():
                    new_memory1 = h1_layer1.mean(dim=1, keepdim=True)
                    new_memory2 = h1_layer2.mean(dim=1, keepdim=True)
                    
                    # Clamp memory values for stability and prevent overflow
                    new_memory1 = torch.clamp(new_memory1, -1.0, 1.0)
                    new_memory2 = torch.clamp(new_memory2, -1.0, 1.0)
                    
                    self.memory_cache_layer1 = self.memory_decay * self.memory_cache_layer1 + (1 - self.memory_decay) * new_memory1
                    self.memory_cache_layer2 = self.memory_decay * self.memory_cache_layer2 + (1 - self.memory_decay) * new_memory2
                    
                    # Additional clamping after update
                    self.memory_cache_layer1 = torch.clamp(self.memory_cache_layer1, -2.0, 2.0)
                    self.memory_cache_layer2 = torch.clamp(self.memory_cache_layer2, -2.0, 2.0)
                
                # Reshape back
                gru_out = gru_final.reshape(B, N, T, D).permute(0, 2, 1, 3)
                
                # Apply dynamic mask
                mask_expanded = dynamic_mask.unsqueeze(1).unsqueeze(-1)
                x_with_gru = x_with_pos + 0.5 * mask_expanded * gru_out
                
            except Exception as e:
                logger.warning(f"GRU processing failed: {e}")
                x_with_gru = x_with_pos
        else:
            x_with_gru = x_with_pos
        
        # Enhanced attention computation with safe operations
        try:
            target_reshaped = target_features.reshape(B*N, D)
            seq_reshaped = x_with_gru.permute(0, 2, 1, 3).reshape(B*N, T, D)
            
            # QKV computation
            Q = self.q_proj(target_reshaped).unsqueeze(1)
            K = self.k_proj(seq_reshaped)
            V = self.v_proj(seq_reshaped)
            
            # Attention scores with stability and overflow protection
            scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(max(1, D))
            
            # Enhanced numerical stability
            if torch.isnan(scores).any() or torch.isinf(scores).any():
                logger.warning("NaN/Inf in attention scores")
                scores = torch.nan_to_num(scores, nan=0.0, posinf=5.0, neginf=-5.0)
            
            scores = torch.clamp(scores, min=-50.0, max=50.0)
            attn_weights = F.softmax(scores, dim=-1)
            
            # Check attention weights
            if torch.isnan(attn_weights).any() or torch.isinf(attn_weights).any():
                logger.warning("NaN/Inf in attention weights")
                attn_weights = torch.ones_like(attn_weights) / max(1, T)
            
            # Apply attention
            attn_out = torch.matmul(attn_weights, V).squeeze(1)
            
            # Output projection
            attn_out = self.out_proj(attn_out)
            attn_out = attn_out.reshape(B, N, D).unsqueeze(1).expand(-1, T, -1, -1)
            
            # Final output
            weighted_x = x_with_gru + attn_out
            weighted_x = self.norm(weighted_x + x)
            
            return weighted_x
            
        except Exception as e:
            logger.error(f"Attention computation failed: {e}")
            return self.norm(x_with_gru + x)


def create_swin_water_net(config):
    """Enhanced factory function with better error handling"""
    try:
        # Enhanced configuration parsing
        if hasattr(config, 'model'):
            model_config = config.model
        else:
            model_config = config.get('model', {})
        
        if hasattr(model_config, 'swin_config'):
            swin_config = model_config.swin_config
        else:
            swin_config = model_config.get('swin_config', {})
        
        # Extract configuration with defaults
        def safe_get(obj, key, default):
            if hasattr(obj, 'get'):
                return obj.get(key, default)
            else:
                return getattr(obj, key, default)
        
        # Model parameters
        img_size = safe_get(swin_config, 'img_size', 256)
        patch_size = safe_get(swin_config, 'patch_size', 8)
        in_chans = safe_get(swin_config, 'in_chans', 2)
        out_chans = safe_get(swin_config, 'out_chans', 2)
        embed_dim = safe_get(swin_config, 'embed_dim', 128)
        depths = safe_get(swin_config, 'depths', [2, 2, 6, 2])
        num_heads = safe_get(swin_config, 'num_heads', [3, 6, 12, 24])
        window_sizes = safe_get(swin_config, 'window_sizes', [8, 8, 8, 8])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes

        # Validate and fix num_heads to ensure compatibility with embed_dim
        def validate_num_heads(embed_dim, depths, num_heads):
            """Ensure num_heads are compatible with embed_dim at each layer"""
            validated_heads = []
            current_dim = embed_dim

            for i, (depth, heads) in enumerate(zip(depths, num_heads)):
                if current_dim % heads != 0:
                    # Find the largest divisor of current_dim that is <= heads
                    for h in range(heads, 0, -1):
                        if current_dim % h == 0:
                            validated_heads.append(h)
                            logger.warning(f"Layer {i}: Adjusted num_heads from {heads} to {h} for dim {current_dim}")
                            break
                else:
                    validated_heads.append(heads)

                # Update dimension for next layer (typically doubles)
                if i < len(depths) - 1:
                    current_dim *= 2

            return validated_heads

        num_heads = validate_num_heads(embed_dim, depths, num_heads)
        
        # Training parameters
        use_checkpoint = safe_get(model_config, 'use_gradient_checkpoint', True)
        
        # Create model
        model = OptimizedSwinWaterNet(
            img_size=img_size,
            patch_size=patch_size,
            in_chans=in_chans,
            out_chans=out_chans,
            embed_dim=embed_dim,
            depths=depths,
            num_heads=num_heads,
            window_size=window_size,
            use_checkpoint=use_checkpoint
        )
        
        logger.info(f"Created SwinWaterNet v5.1.1 with {sum(p.numel() for p in model.parameters())} parameters")
        return model
        
    except Exception as e:
        logger.error(f"Model creation failed: {e}")
        raise


# Backward compatibility
SwinWaterNet = OptimizedSwinWaterNet

__all__ = [
    'OptimizedSwinWaterNet',
    'SwinWaterNet',
    'create_swin_water_net',
    'GeographicTemporalModulator',
    'safe_long_conversion'
]