"""
Video SwinWaterNet - Multi-Scale Temporal Inpainting Model

simplified the frame selection based on swin_water_net.py
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import numpy as np
from einops import rearrange, repeat
from einops.layers.torch import Rearrange
from typing import Dict, List, Optional, Tuple
import math
import logging
from .loss import compute_dynamic_degree

logger = logging.getLogger(__name__)


from model_glad.decoder_glad import MemoryEfficientDecoder

class PositionalEncoding3D(nn.Module):
    """3D sinusoidal positional encoding for video patches"""
    
    def __init__(self, embed_dim, max_temporal_len=120):
        super().__init__()
        self.embed_dim = embed_dim
        
        # Precompute temporal position encodings
        pe_t = torch.zeros(max_temporal_len, embed_dim // 3)
        position = torch.arange(0, max_temporal_len).unsqueeze(1).float()
        
        div_term = torch.exp(torch.arange(0, embed_dim // 3, 2).float() * 
                           -(math.log(10000.0) / (embed_dim // 3)))
        
        pe_t[:, 0::2] = torch.sin(position * div_term)
        pe_t[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe_t', pe_t)
    
    def forward(self, t_positions):
        """Get positional encoding for given temporal positions"""
        return self.pe_t[t_positions]




class EfficientPatchEmbed(nn.Module):
    """
    Memory-efficient patch embedding for video sequences
    """
    
    def __init__(self, 
                 img_size=256, 
                 patch_size=8,
                 in_chans=2,
                 embed_dim=128):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.grid_size = img_size // patch_size
        
        # Efficient patch embedding with single conv
        self.proj = nn.Conv2d(
            in_chans, embed_dim, 
            kernel_size=patch_size, 
            stride=patch_size
        )
        
        self.norm = nn.LayerNorm(embed_dim)
    
    def forward(self, x):
        """
        Args:
            x: (B, T, C, H, W) video sequence
        Returns:
            x: (B, T, N, D) sequence of embedded patches
            where N = (H/p)*(W/p) is number of patches
        """
        B, T, C, H, W = x.shape
        
        # Process each frame - flatten to process in batches
        x = x.flatten(0, 1)  # (B*T, C, H, W)
        
        # Project patches
        x = self.proj(x)  # (B*T, D, H', W')
        
        # Reshape to patches
        D, H_p, W_p = x.shape[1], x.shape[2], x.shape[3]
        x = x.reshape(B, T, D, H_p, W_p)
        x = x.permute(0, 1, 3, 4, 2)  # (B, T, H', W', D)
        x = x.reshape(B, T, H_p*W_p, D)  # (B, T, N, D)
        
        # Layer normalization
        x = self.norm(x)
        
        return x


class WindowAttention(nn.Module):
    """
    Window based multi-head self attention (W-MSA) module with relative position bias.
    It supports both of shifted and non-shifted window.
    
    优化版本: 提前计算好格式化的relative_position_bias，避免每次前向传播时的view和permute操作
    """
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))

        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w]))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)
        
        # 预计算相对位置偏置的索引，避免在forward中重复view操作
        self.bias_index = self.relative_position_index.view(-1)
        
        # 注册一个空的buffer用于缓存格式化后的relative_position_bias
        # 在第一次forward或权重更新时会计算
        self.register_buffer('formatted_rel_pos_bias', None, persistent=False)
        self.cached_window_size = window_size

    def _get_rel_pos_bias(self):
        """获取格式化后的relative position bias"""
        # NOTE: Gradient checkpointing requires that the graph traced during
        # the original forward pass and the recomputation pass be identical.
        # If we cache the computed bias the first time (thus skipping the
        # computation in the second pass) PyTorch detects a different number
        # of saved tensors and raises `CheckpointError`.

        # To stay safe we *always* recompute the formatted bias when gradient
        # checkpointing is enabled (i.e. when autograd recording is on).
        # The extra cost is negligible compared with attention ops and avoids
        # the mismatch error.

        recompute_needed = torch.is_grad_enabled() and self.training
        if self.formatted_rel_pos_bias is None or self.cached_window_size != self.window_size or recompute_needed:
            self.cached_window_size = self.window_size

            rel_pos_bias = self.relative_position_bias_table[self.bias_index]
            rel_pos_bias = rel_pos_bias.view(
                self.window_size[0] * self.window_size[1],
                self.window_size[0] * self.window_size[1],
                self.num_heads
            )
            rel_pos_bias = rel_pos_bias.permute(2, 0, 1).contiguous()
            # 数值裁剪，避免偏置过大/过小导致 softmax 溢出
            rel_pos_bias = torch.clamp(rel_pos_bias, min=-5.0, max=5.0)

            # 仅在不会影响梯度检查点的一般推理路径下缓存
            if not recompute_needed:
                self.formatted_rel_pos_bias = rel_pos_bias

            return rel_pos_bias

        # 如果已经缓存并且不在梯度检查点路径下，直接返回缓存
        return self.formatted_rel_pos_bias
        
    def forward(self, x, mask=None):
        """Standard attention computation with optimized position bias handling"""
        B_, N, C = x.shape
        
        # QKV projection
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]  # (B_, nH, N, C//nH)
        
        # Scaled dot-product attention
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))  # (B_, nH, N, N)
        
        # 使用预计算的相对位置偏置
        rel_pos_bias = self._get_rel_pos_bias()  # (nH, Wh*Ww, Wh*Ww)
        attn = attn + rel_pos_bias.unsqueeze(0)  # 添加到注意力分数
        
        # Apply mask (if any)
        if mask is not None:
            nW = mask.shape[0]
            # Reshape attention matrix to apply mask
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            # 在 softmax 前对 logits 做裁剪，防止 exp 溢出
            attn = self.softmax(torch.clamp(attn, min=-80.0, max=80.0))
        else:
            # 同样对未加 mask 的分支进行裁剪
            attn = self.softmax(torch.clamp(attn, min=-80.0, max=80.0))
        
        # Apply dropout
        attn = self.attn_drop(attn)
        
        # Output projection
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


def window_partition(x, window_size):
    """
    Partitions a tensor into non-overlapping windows.
    Args:
        x: (B, H, W, C)
        window_size (int): window size
    Returns:
        windows: (num_windows*B, window_size, window_size, C)
    """
    B, H, W, C = x.shape
    
    # 确保H和W能被window_size整除，如果不能则进行padding
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    
    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W
    
    x = x.view(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """
    Merges windows back to the original tensor shape.
    Args:
        windows: (num_windows*B, window_size, window_size, C)
        window_size (int): Window size
        H (int): Height of image
        W (int): Width of image
    Returns:
        x: (B, H, W, C)
    """
    # 计算padding后的尺寸
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w
    
    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.view(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_padded, W_padded, -1)
    
    # 移除padding，恢复原始尺寸
    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]
    
    return x


class TemporalSeparableAttention(nn.Module):
    """
    Memory-efficient temporal attention with separable operations and windowing.
    First applies spatial attention within frames, then temporal attention across frames.
    """
    def __init__(self, dim, num_heads, window_size=8, qkv_bias=False, dropout=0.):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        
        # Extract spatial window size if a list is passed (e.g., [T, H, W])
        if isinstance(window_size, (list, tuple)):
            spatial_window_size = window_size[1]
        else:
            spatial_window_size = window_size
        self.window_size = spatial_window_size

        # Spatial attention (within frames) - now windowed
        self.spatial_attn = WindowAttention(
            dim=dim,
            window_size=(self.window_size, self.window_size),
            num_heads=num_heads,
            qkv_bias=qkv_bias,
            attn_drop=dropout,
            proj_drop=dropout
        )
        
        # Temporal attention (across frames) - using standard MHA for simplicity
        # as temporal dimension is much smaller.
        self.temporal_attn = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True,
        )
        
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(dropout)
    
    def forward(self, x, T, H, W):
        """
        Apply separable space-time attention
        Args:
            x: (B, T*H*W, C) flattened video features
            T, H, W: dimensions
        """
        B, L, C = x.shape
        assert L == T * H * W, f"Mismatched dimensions: L={L}, T*H*W={T*H*W}"
        
        # Reshape for spatial attention
        x_spatial = rearrange(x, 'b (t h w) c -> (b t) h w c', t=T, h=H, w=W)
        BT, H_spatial, W_spatial, C_spatial = x_spatial.shape
        
        # Partition into windows
        x_windows = window_partition(x_spatial, self.window_size)
        x_windows = x_windows.view(-1, self.window_size * self.window_size, C)

        # Apply spatial windowed attention
        attn_windows = self.spatial_attn(x_windows)
        
        # Reverse window partitioning
        attn_windows = attn_windows.view(-1, self.window_size, self.window_size, C)
        x_spatial = window_reverse(attn_windows, self.window_size, H, W)
        x_spatial = x_spatial.reshape(BT, H * W, C)

        # Reshape for temporal attention
        x_temporal = rearrange(x_spatial, '(b t) (h w) c -> (b h w) t c', b=B, t=T, h=H, w=W)
        
        # Apply temporal attention
        x_temporal, _ = self.temporal_attn(x_temporal, x_temporal, x_temporal)
        
        # Reshape back and combine
        x_out = rearrange(x_temporal, '(b h w) t c -> b (t h w) c', b=B, h=H, w=W)
        
        # Final projection
        x_out = self.proj(x_out)
        x_out = self.proj_drop(x_out)
        
        return x_out


class MemoryEfficientBlock(nn.Module):
    """
    Memory-efficient transformer block with separable space-time attention
    and gradient checkpointing for lower memory usage
    """
    
    def __init__(self,
                 dim,
                 num_heads,
                 mlp_ratio=4.,
                 qkv_bias=False,
                 drop=0.,
                 attn_drop=0.,
                 drop_path=0.,
                 act_layer=nn.GELU,
                 norm_layer=nn.LayerNorm,
                 use_checkpoint=True,
                 window_size=8):
        super().__init__()
        self.use_checkpoint = use_checkpoint
        self.norm1 = norm_layer(dim)
        
        # Memory-efficient separable attention
        self.attn = TemporalSeparableAttention(
            dim=dim,
            num_heads=num_heads,
            qkv_bias=qkv_bias,
            dropout=attn_drop,
            window_size=window_size,
        )
        
        # Stochastic depth
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)
        
        # MLP block
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            act_layer(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )
    
    def _forward_impl(self, x, T, H, W):
        # Attention with residual connection
        x = x + self.drop_path(self.attn(self.norm1(x), T, H, W))
        
        # MLP with residual connection
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        
        return x
    
    def forward(self, x, T, H, W):
        """
        Forward with optional gradient checkpointing
        """
        if self.use_checkpoint and self.training:
            # Use gradient checkpointing to save memory
            return torch.utils.checkpoint.checkpoint(
                self._forward_impl, x, T, H, W, use_reentrant=False
            )
        else:
            return self._forward_impl(x, T, H, W)


# Define einops rearrange as module for checkpointing compatibility
class Rearrange(nn.Module):
    def __init__(self, pattern, **axes_lengths):
        super().__init__()
        self.pattern = pattern
        self.axes_lengths = axes_lengths
        
    def forward(self, x):
        return rearrange(x, self.pattern, **self.axes_lengths)


class TemporalDownsample(nn.Module):
    """
    Custom module for temporal downsampling that properly handles dimensions
    """
    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        self.maxpool = nn.MaxPool3d(kernel_size=(2,1,1), stride=(2,1,1))
        
    def forward(self, x, T, H, W):
        B, L, C = x.shape
        
        # 更宽松的断言，允许一些舍入误差
        expected_L = T * H * W
        if L != expected_L:
            logger.warning(f"TemporalDownsample: L ({L}) != T*H*W ({T}*{H}*{W}={expected_L}). Adjusting.")
            # 调整L以匹配
            L_adjusted = (L // (H * W)) * H * W
            x = x[:, :L_adjusted, :]
            T_adjusted = L_adjusted // (H * W)
            logger.warning(f"Adjusted T from {T} to {T_adjusted}, L from {L} to {L_adjusted}")
            T = T_adjusted
            L = L_adjusted
        
        # Now reshape and apply temporal downsampling
        x = x.reshape(B, T, H, W, C).permute(0, 4, 1, 2, 3)  # B, C, T, H, W
        x = self.maxpool(x)  # Apply temporal downsampling
        
        # Reshape back to sequence format
        x = x.permute(0, 2, 3, 4, 1)  # B, T//2, H, W, C
        new_T = x.shape[1]
        new_L = new_T * H * W
        x = x.reshape(B, new_L, C)
        
        # 验证输出维度
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"TemporalDownsample: input shape {(B, L, C)}, output shape {x.shape}")
            logger.debug(f"T: {T} -> {new_T}, H: {H}, W: {W}")
        
        return x, new_T  # 返回降采样后的tensor和新的T值


class MultiScaleEncoder(nn.Module):
    """
    Multi-scale encoder with progressive temporal downsampling
    Uses pyramid approach for efficient processing of long sequences
    """
    
    def __init__(self,
                 embed_dim=128,
                 depths=[2, 2, 6, 2],
                 num_heads=[4, 8, 16, 32],
                 window_sizes=[8, 8, 8, 8],
                 mlp_ratio=4.,
                 qkv_bias=False,
                 drop_rate=0.,
                 attn_drop_rate=0.,
                 drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm,
                 use_checkpoint=True):
        super().__init__()
        
        self.num_layers = len(depths)
        self.embed_dim = embed_dim
        
        # Stochastic depth decay
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        # Build layers with progressive downsampling
        self.layers = nn.ModuleList()
        self.temporal_downsamples = nn.ModuleList()
        self.spatial_downsamples = nn.ModuleList()
        self.fusion_projections = nn.ModuleList()
        
        # Current dimension after each stage
        curr_dim = embed_dim
        
        for i_layer in range(self.num_layers):
            # Build blocks for current stage
            layer_depth = depths[i_layer]
            layer_dim = curr_dim
            layer_num_heads = num_heads[i_layer]
            
            blocks = nn.ModuleList([
                MemoryEfficientBlock(
                    dim=layer_dim,
                    num_heads=layer_num_heads,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    drop=drop_rate,
                    attn_drop=attn_drop_rate,
                    drop_path=dpr[sum(depths[:i_layer]) + i_block],
                    norm_layer=norm_layer,
                    use_checkpoint=use_checkpoint,
                    window_size=window_sizes[i_layer],
                )
                for i_block in range(layer_depth)
            ])
            self.layers.append(blocks)
            
            # Downsampling for next stage (except the last one)
            if i_layer < self.num_layers - 1:
                next_dim = curr_dim * 2
                # Fusion projection for the *next* stage, to project base_dim -> next_dim
                self.fusion_projections.append(nn.Linear(self.embed_dim, next_dim))

                # Temporal downsampling
                temporal_downsample = TemporalDownsample(curr_dim)
                self.temporal_downsamples.append(temporal_downsample)

                # Spatial downsampling (channel expansion)
                spatial_downsample = nn.Linear(curr_dim, next_dim)
                self.spatial_downsamples.append(spatial_downsample)
                curr_dim = next_dim

    def forward(self, x_pyramid, T_pyramid, H, W):
        """
        Forward pass through multi-scale encoder with progressive fusion.
        更加内存高效的实现，减少重复的插值计算。
        
        Args:
            x_pyramid: List of (B, T_i*H*W, C) input sequences from pyramid
            T_pyramid: List of temporal lengths for each scale
            H, W: spatial patch dimensions
        """
        B, _, C = x_pyramid[0].shape
        
        # Store features from each stage
        features = []
        
        # 预处理其他尺度的特征以减少重复计算
        # 缓存其他尺度特征的投影和重塑结果
        projected_scales = {}
        
        # Process first (highest temporal resolution) scale
        x = x_pyramid[0]
        T = T_pyramid[0]
        
        # 跟踪每个stage的实际T值
        real_T_values = [T]
        
        # 预先处理和缓存所有尺度的特征投影，避免重复插值
        for i in range(1, min(self.num_layers, len(x_pyramid))):
            # 获取当前尺度的特征
            x_scale = x_pyramid[i]
            T_scale = T_pyramid[i]
            
            # 投影到目标维度
            x_proj = self.fusion_projections[i-1](x_scale)
            
            # 缓存投影后的特征和时间维度
            projected_scales[i] = {
                'features': x_proj,
                'T': T_scale
            }

        for i_layer, blocks in enumerate(self.layers):
            # Fuse with features from other scales if available
            if i_layer > 0 and i_layer < len(x_pyramid):
                # 使用预处理的特征
                x_other_scale = projected_scales[i_layer]['features']
                T_other = projected_scales[i_layer]['T']

                # 仅在这里做一次reshape和插值
                if T != T_other or H != H or W != W:  # 如果尺寸不同才插值
                    x_other_scale = rearrange(x_other_scale, 'b (t h w) c -> b c t h w', t=T_other, h=H, w=W)
                    x_other_scale = F.interpolate(x_other_scale, size=(T, H, W), mode='trilinear', align_corners=False)
                    x_other_scale = rearrange(x_other_scale, 'b c t h w -> b (t h w) c')
                
                x = x + x_other_scale

            # Process all blocks in current stage - 这部分不变
            for block in blocks:
                x = block(x, T, H, W)
            
            # Store features from this stage
            features.append(x)
            
            # Apply downsampling if not last layer - 这部分不变
            if i_layer < self.num_layers - 1:
                # 修改为使用TemporalDownsample返回的真实T值
                x, T = self.temporal_downsamples[i_layer](x, T, H, W)
                real_T_values.append(T)  # 记录降采样后的实际T值
                x = self.spatial_downsamples[i_layer](x)
        
        return features, real_T_values


class GeographicTemporalEmbedding(nn.Module):
    """
    Efficient embedding for geographic and temporal conditioning
    """
    
    def __init__(self, embed_dim=128, num_freq_bands=6):
        super().__init__()
        self.embed_dim = embed_dim
        
        # Fourier feature encoding
        self.register_buffer(
            'freq_bands', 
            2.0 ** torch.linspace(0, num_freq_bands-1, num_freq_bands)
        )
        
        # Feature dimensions
        geo_fourier_dim = 4 * num_freq_bands  # sin/cos for lon/lat
        temporal_fourier_dim = 2 * num_freq_bands  # sin/cos for month
        year_embed_dim = 16
        
        # Year embedding (learnable)
        self.year_embed = nn.Embedding(200, year_embed_dim)  # Years 1900-2100
        
        # Feature projector (more efficient)
        total_dim = geo_fourier_dim + temporal_fourier_dim + year_embed_dim
        self.projector = nn.Sequential(
            nn.Linear(total_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU()
        )
        
        # Simple modulation
        self.scale_shift = nn.Linear(embed_dim, embed_dim * 2)
    
    def forward(self, lon, lat, year, month):
        """Encode geographic and temporal features"""
        B = lon.shape[0]
        device = lon.device
        
        # Geographic Fourier features
        lon_scaled = lon / 180.0 * math.pi  # [-π, π]
        lat_scaled = lat / 90.0 * math.pi   # [-π, π]
        
        geo_features = []
        for freq in self.freq_bands:
            geo_features.extend([
                torch.sin(freq * lon_scaled),
                torch.cos(freq * lon_scaled),
                torch.sin(freq * lat_scaled),
                torch.cos(freq * lat_scaled)
            ])
        geo_features = torch.stack(geo_features, dim=-1)
        
        # Temporal Fourier features
        month_angle = (month - 1) / 12.0 * 2 * math.pi  # [0, 2π]
        temporal_features = []
        for freq in self.freq_bands:
            temporal_features.extend([
                torch.sin(freq * month_angle),
                torch.cos(freq * month_angle)
            ])
        temporal_features = torch.stack(temporal_features, dim=-1)
        
        # Year embedding
        year_idx = torch.clamp(year - 1900, 0, 199).long()
        year_features = self.year_embed(year_idx)
        
        # Combine all features
        combined = torch.cat([geo_features, temporal_features, year_features], dim=-1)
        
        # Project to embedding dimension
        return self.projector(combined)  # (B, embed_dim)
    
    def modulate(self, x):
        """Apply FiLM-style modulation to features"""
        scale_shift = self.scale_shift(x)  # (B, 2*embed_dim)
        scale, shift = scale_shift.chunk(2, dim=-1)
        
        # Keep scale factors close to 1 for stability
        scale = 1 + 0.1 * torch.tanh(scale)
        
        return scale, shift


# -----------------------------------------------------------------------------
# Backward-compatibility wrapper: original training code expects
# `GeographicTemporalModulator` with `encode_features` + `modulate`.
# We wrap the new `GeographicTemporalEmbedding` to match该接口.
# -----------------------------------------------------------------------------

class GeographicTemporalModulator(GeographicTemporalEmbedding):
    """Compatibility alias for legacy import path."""

    def encode_features(self, lon, lat, year, month):  # noqa: N802 legacy name
        return super().forward(lon, lat, year, month)

    # `modulate` is inherited as-is.


class OptimizedSwinWaterNet(nn.Module):
    """优化版SwinWater模型 - 内存高效和计算优化版本，支持概率值学习"""
    
    def __init__(
        self,
        img_size=256,
        patch_size=4,
        in_chans=2,
        out_chans=1,  # 修改为单通道输出概率值
        num_frames=120,
        num_temporal_scales=3,
        base_keep_rate=0.25,
        frame_counts=None,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        num_heads=[3, 6, 12, 24],
        window_size=7,
        mlp_ratio=4.,
        qkv_bias=True,
        qk_scale=None,
        drop_rate=0.,
        attn_drop_rate=0.,
        drop_path_rate=0.1,
        norm_layer=nn.LayerNorm,
        patch_norm=True,
        use_checkpoint=False,
        confidence_channels=1
    ):
        super().__init__()
        
        # 基本模型参数
        self.num_frames = num_frames
        self.num_temporal_scales = num_temporal_scales
        self.base_keep_rate = base_keep_rate
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans  # 单通道输出概率值
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_heads = num_heads
        self.window_size = window_size
        self.mlp_ratio = mlp_ratio
        self.qkv_bias = qkv_bias
        self.qk_scale = qk_scale
        self.drop_rate = drop_rate
        self.attn_drop_rate = attn_drop_rate
        self.drop_path_rate = drop_path_rate
        self.norm_layer = norm_layer
        self.patch_norm = patch_norm
        self.use_checkpoint = use_checkpoint
        self.confidence_channels = confidence_channels
        
        # 维度验证和调试设置
        self.debug_dimensions = False  # 设置为True以启用详细的维度调试输出
        
        # 时间降采样模块（带注意力）- 优化为短期、中期、长期策略
        # Remove all AttentionGuidedPyramidSampling logic and frame_counts/base_keep_rate/num_temporal_scales
        # Instead, we will use a fixed 3-segment sampling for 48-frame input
        # (No self.temporal_sampling)
        
        # Patch embedding for video sequences
        self.patch_embed = EfficientPatchEmbed(
            img_size=self.img_size,
            patch_size=self.patch_size,
            in_chans=self.in_chans,
            embed_dim=self.embed_dim
        )
        
        # 时间注意力模块 - 学习哪些帧对目标帧重建最重要
        self.temporal_attention = nn.ModuleDict({
            f'scale_{i}': TemporalAttentionModule(
                embed_dim=self.embed_dim,
                num_heads=self.num_heads[0],
                max_frames=int(self.num_frames * (self.base_keep_rate ** i)),
                use_gru=False
            )
            for i in range(self.num_temporal_scales)
        })
        
        # Multi-scale encoder with Swin blocks
        self.encoder = MultiScaleEncoder(
            embed_dim=self.embed_dim,
            depths=self.depths,
            num_heads=self.num_heads,
            window_sizes=[self.window_size] * len(self.depths),
            mlp_ratio=self.mlp_ratio,
            qkv_bias=self.qkv_bias,
            drop_rate=self.drop_rate,
            attn_drop_rate=self.attn_drop_rate,
            drop_path_rate=self.drop_path_rate,
            use_checkpoint=self.use_checkpoint
        )
        
        # 地理时间编码
        self.geo_temporal_encoder = GeographicTemporalEmbedding(
            embed_dim=self.embed_dim
        )
        
        # 目标帧特征投影层
        # 计算最终编码层的特征维度
        final_dim = self.embed_dim * (2 ** (len(self.depths) - 1))
        self.target_dim_adapter = nn.Linear(self.embed_dim, final_dim)
        
        # Decoder outputs segmentation logits with `out_chans` (usually 2) and separate confidence logits (1)
        self.decoder = MemoryEfficientDecoder(
            in_channels=final_dim,
            out_channels=self.out_chans,
            img_size=self.img_size,
            patch_size=self.patch_size,
            num_classes=self.out_chans,  # 2-class segmentation logits
            use_checkpoint=self.use_checkpoint
        )
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _three_segment_temporal_sampling(self, x, center_frame_idx, segment_size=16):
        """
        Vectorized version. For each batch, select three segments of segment_size frames:
        - closest to center_frame_idx (centered window)
        - next closest (adjacent window, not overlapping)
        - farthest (from the two ends, not overlapping)
        If T < 3*segment_size, segments are as balanced as possible and non-overlapping.
        Args:
            x: (B, T, C, H, W)
            center_frame_idx: (B,)
            segment_size: int
        Returns:
            segments: list of 3 tensors, each (B, segment_size, C, H, W)
        """
        B, T, C, H, W = x.shape
        device = x.device
        seg = segment_size
        # 1. Compute all frame indices
        frame_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, -1)  # (B, T)
        center_idx = center_frame_idx.unsqueeze(1)  # (B, 1)
        # 2. Compute distance to center for each frame
        dist = torch.abs(frame_indices - center_idx)  # (B, T)
        # 3. For each batch, get sorted indices by distance
        sorted_dist, sorted_idx = torch.sort(dist, dim=1)  # (B, T)
        # 4. Closest segment: first seg indices
        seg0_idx = sorted_idx[:, :seg]  # (B, seg)
        # 5. Next closest: next seg indices, but not overlapping with seg0
        mask0 = torch.zeros((B, T), dtype=torch.bool, device=device)
        mask0.scatter_(1, seg0_idx, True)
        # Mask out seg0
        dist1 = dist.masked_fill(mask0, float('inf'))
        sorted_dist1, sorted_idx1 = torch.sort(dist1, dim=1)
        seg1_idx = sorted_idx1[:, :seg]
        # 6. Farthest: largest distance, not overlapping with seg0 or seg1
        mask1 = mask0.clone()
        mask1.scatter_(1, seg1_idx, True)
        dist2 = dist.masked_fill(mask1, float('-inf'))
        sorted_dist2, sorted_idx2 = torch.sort(dist2, dim=1, descending=True)
        seg2_idx = sorted_idx2[:, :seg]
        # 7. Gather segments
        batch_idx = torch.arange(B, device=device).unsqueeze(1).expand(-1, seg)
        seg0 = x[batch_idx, seg0_idx]  # (B, seg, C, H, W)
        seg1 = x[batch_idx, seg1_idx]
        seg2 = x[batch_idx, seg2_idx]
        return [seg0, seg1, seg2]

    def _verify_dimensions(self, feat, T, H, W, stage_name=""):
        """验证特征张量维度是否符合预期"""
        if not isinstance(feat, torch.Tensor):
            return
        
        B, L, D = feat.shape
        expected_L = T * H * W
        
        if L != expected_L:
            msg = f"{stage_name}: 维度不匹配 L={L} != T*H*W={T}*{H}*{W}={expected_L}，差异={L-expected_L}"
            if self.debug_dimensions:
                logger.warning(msg)
                
            if abs(L - expected_L) > expected_L * 0.5:  # 如果差异超过50%，可能是严重错误
                logger.error(msg)
                
        return L == expected_L
        
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def reset_temporal_memory(self):
        """重置所有时间注意力模块的记忆缓存"""
        for module_name, module in self.temporal_attention.items():
            if hasattr(module, 'reset_memory_cache'):
                module.reset_memory_cache()
                logger.info(f"Reset memory cache for {module_name}")
    
    def get_memory_status(self):
        """获取所有时间注意力模块的记忆状态"""
        status = {}
        for module_name, module in self.temporal_attention.items():
            if hasattr(module, 'get_memory_info'):
                status[module_name] = module.get_memory_info()
        return status
    
    def set_dynamic_attention_beta(self, beta_value):
        """动态调整注意力偏置强度"""
        for module in self.temporal_attention.values():
            if hasattr(module, 'dynamic_attention_beta'):
                module.dynamic_attention_beta = beta_value
    
    def set_memory_decay(self, decay_value):
        """动态调整记忆衰减率"""
        for module in self.temporal_attention.values():
            if hasattr(module, 'memory_decay'):
                module.memory_decay = decay_value
            
    def forward(self, batch):
        """
        视频修复网络前向传播
        
        Args:
            batch: 包含输入数据的字典，包括：
                - input_sequence: [B, T, 2, H, W] (水体掩码 + 缺失掩码)
                - center_frame_idx: 目标帧索引 [B]
                - tile_lon, tile_lat: 地理坐标 [B]
                - year, month: 时间信息 [B]
               
        Returns:
            outputs: 字典格式输出，包含：
                - inpaint: {'logits': [B, 2, H, W], 'confidence': [B, 1, H, W]}
        """
        # 从batch字典中提取数据
        x = batch['input_sequence']  # (B, T, 2, H, W)
        center_frame_idx = batch.get('center_frame_idx', None)
        
        B, T, C, H, W = x.shape
        device = x.device
        
        # 标准化center_frame_idx为batch维度的张量
        if center_frame_idx is None:
            center_frame_idx = torch.full((B,), T // 2, device=device, dtype=torch.long)
        elif isinstance(center_frame_idx, (int, float)):
            center_frame_idx = torch.tensor([int(center_frame_idx)] * B, device=device, dtype=torch.long)
        elif center_frame_idx.numel() == 1 and B > 1:
            center_frame_idx = center_frame_idx.expand(B)
        
        # 1. 单独提取目标帧 - 完全向量化操作
        # 使用高级索引进行批量提取，避免循环
        batch_indices = torch.arange(B, device=device)
        target_frames = x[batch_indices, center_frame_idx]  # (B, 2, H, W)
        
        # 2. 对目标帧进行特征嵌入
        target_patches = self.patch_embed(target_frames.unsqueeze(1))  # (B, 1, N, D)
        target_features = target_patches.squeeze(1)  # (B, N, D)
        
        # 获取水体频率信息(如果可用)
        water_frequency = batch.get('occurrence', None)  # (B, H, W)
        
        # 3. 使用三等分采样策略
        multi_scale_x = self._three_segment_temporal_sampling(x, center_frame_idx)
        
        # 4. 处理多尺度序列，使用目标帧作为参考
        multi_scale_features = []
        multi_scale_T = []
        
        for scale_idx, scale_x in enumerate(multi_scale_x):
            # scale_x: (B, T', 2, H, W)
            B_s, T_s, C_s, H_s, W_s = scale_x.shape
            
            # Patch embedding
            x_embed = self.patch_embed(scale_x)  # (B, T', N, D)
            B_e, T_e, N_e, D_e = x_embed.shape
            
            # 应用时间注意力，现在不需要找目标帧位置
            # 直接使用提前计算的目标帧特征作为query
            temporal_attn_module = self.temporal_attention[f'scale_{scale_idx}']
            x_weighted = temporal_attn_module(x_embed, target_features, water_frequency)  # (B, T', N, D)
            
            # Flatten for encoder
            x_flat = x_weighted.reshape(B_e, T_e * N_e, D_e)
            multi_scale_features.append(x_flat)
            multi_scale_T.append(T_e)
        
        # 5. 获取地理时间编码
        geo_temporal_encoding = self.geo_temporal_encoder(
            batch['tile_lon'], 
            batch['tile_lat'],
            batch['year'],
            batch['month']
        )  # (B, D)
        
        # 6. 将地理时间编码添加到所有特征 - 向量化版本
        # 一次性处理所有尺度的特征
        for i in range(len(multi_scale_features)):
            B_f, L_f, D_f = multi_scale_features[i].shape
            # 使用广播机制进行向量化加法
            multi_scale_features[i] = multi_scale_features[i] + 0.1 * geo_temporal_encoding.unsqueeze(1).expand(B_f, L_f, D_f)
        
        # 7. Multi-scale encoding
        H_p = W_p = self.img_size // self.patch_size
        
        # 调试维度信息
        if self.debug_dimensions:
            for i, feat in enumerate(multi_scale_features):
                logger.debug(f"Multi-scale feature {i}: shape {feat.shape}, T={multi_scale_T[i]}")
        
        try:
            encoder_features, real_T_values = self.encoder(multi_scale_features, multi_scale_T, H_p, W_p)
        except Exception as e:
            logger.error(f"Encoder failed with error: {e}")
            logger.error(f"Input shapes: {[f.shape for f in multi_scale_features]}")
            logger.error(f"Multi-scale T: {multi_scale_T}")
            logger.error(f"H_p: {H_p}, W_p: {W_p}")
            raise
        
        # ---------------------- 只解码中心帧的关键修改 ----------------------
        
        # 8. 保留层次化的跳跃连接，但仅为中心帧提取特征tokens
        selected_features = []
        for i, feat in enumerate(encoder_features):
            # feat: (B, T'*N, D)
            B_f, L_f, D_f = feat.shape
            
            # 获取该特征层对应的真实时间维度(从encoder返回值中)
            if i < len(real_T_values):
                T_feat = real_T_values[i]
            else:
                # 如果找不到确切的时间维度，安全地推断(避免之前的错误)
                power = i - len(real_T_values) + 1
                T_feat = max(1, real_T_values[-1] // (2 ** power))
                logger.info(f"层 {i}: 推断T={T_feat}(从{real_T_values[-1]}/2^{power})")
            
            # 计算每个时间步的token数量 - 采用调整时间维度而不是裁剪特征的方法
            N_feat_exact = L_f / T_feat  # 浮点除法得到准确值
            
            if L_f % T_feat == 0:
                # 完全整除的情况
                N_feat = L_f // T_feat
            else:
                # 不能整除时，优先调整T_feat使得N_feat为整数
                # 找到最佳的T_feat，使N_feat接近理想值且L_f = T_feat * N_feat
                N_feat_round = round(N_feat_exact)  # 四舍五入得到接近的N
                
                if N_feat_round > 0 and L_f % N_feat_round == 0:
                    # 如果能找到整除的N值，优先使用
                    adjusted_T_feat = L_f // N_feat_round
                    logger.info(f"层 {i}: 调整T从{T_feat}到{adjusted_T_feat}，使N={N_feat_round}能整除L={L_f}")
                    T_feat = adjusted_T_feat
                    N_feat = N_feat_round
                else:
                    # 否则尝试调整T
                    N_feat = H_p * W_p  # 目标是得到标准的空间尺寸
                    if L_f >= N_feat:  # 确保有足够的token
                        adjusted_T_feat = L_f // N_feat
                        logger.info(f"层 {i}: 调整T从{T_feat}到{adjusted_T_feat}，使N={N_feat}(H*W={H_p}*{W_p})能整除L={L_f}")
                        T_feat = adjusted_T_feat
                    else:
                        # 无法达到目标N，退回到推断的T，使用向下取整
                        N_feat = L_f // T_feat
                        logger.warning(f"层 {i}: L={L_f}不能被理想T={T_feat}整除，使用N={N_feat}(可能小于H*W={H_p*W_p})")
            
            self._verify_dimensions(feat, T_feat, N_feat, 1, f"层{i}特征")
            
            # 只为中心帧构建索引 - 确保安全
            feat_center_idx = torch.clamp(center_frame_idx, 0, T_feat-1)
            
            # 为每个批次样本构建中心帧token的索引 - 向量化版本
            # 计算每个批次的起始索引: (B_f,)
            start_indices = feat_center_idx * N_feat
            # 确保索引不超界
            start_indices = torch.clamp(start_indices, 0, L_f - N_feat)
            
            # 创建索引张量: (B_f, N_feat)
            batch_indices = torch.arange(B_f, device=device).unsqueeze(1)  # (B_f, 1)
            token_indices = torch.arange(N_feat, device=device).unsqueeze(0)  # (1, N_feat)
            indices = start_indices.unsqueeze(1) + token_indices  # (B_f, N_feat)
            
            # 使用高级索引进行批量特征提取
            center_feats = feat[batch_indices, indices]  # (B_f, N_feat, D)
            
            # 添加优化的调试信息
            if self.debug_dimensions:
                logger.debug(f"层 {i}: 中心帧特征形状 {center_feats.shape}, 原始特征形状 {feat.shape}")
                logger.debug(f"层 {i}: T={T_feat}, N={N_feat}, L={L_f}, 预期L={T_feat*N_feat}")
            
            selected_features.append(center_feats)
            
        # 9. 使用最终层特征进行解码
        final_features = selected_features[-1]  # (B, N, D')
        B_f, N_f, D_f = final_features.shape
        
        # 计算目标patch尺寸
        target_h = target_w = self.img_size // self.patch_size
        target_patches = target_h * target_w
        
        if self.debug_dimensions:
            logger.info(f"最终特征形状: {final_features.shape}")
            logger.info(f"预期patches: {target_patches} ({target_h}x{target_w}), 实际: {N_f}")
            logger.info(f"元素数量: 预期 {B_f * D_f * target_patches}, 实际 {B_f * N_f * D_f}")
        
        if N_f != target_patches:
            logger.warning(f"Patch数量不匹配: 预期{target_patches}, 实际{N_f}。使用智能重塑适配。")

            # 更智能的解决方案：根据N确定最优的空间布局，然后插值
            # 步骤1：计算近似的空间维度
            sqrt_n = int(math.sqrt(N_f))
            
            # 步骤2：找最接近的整数因式分解 - 向量化版本
            best_h, best_w = sqrt_n, sqrt_n
            min_diff = abs(sqrt_n * sqrt_n - N_f)
            
            # 向量化计算所有可能的h值
            h_range = torch.arange(max(1, sqrt_n-5), sqrt_n+6, device=device)
            
            # 计算对应的w值和差异
            w_exact = N_f / h_range.float()
            w_floor = torch.floor(w_exact).long()
            w_ceil = torch.ceil(w_exact).long()
            
            # 计算差异
            diff_floor = torch.abs(h_range * w_floor - N_f)
            diff_ceil = torch.abs(h_range * w_ceil - N_f)
            
            # 找到最小差异
            min_diff_floor, min_idx_floor = torch.min(diff_floor, dim=0)
            min_diff_ceil, min_idx_ceil = torch.min(diff_ceil, dim=0)
            
            # 选择更好的结果
            if min_diff_floor <= min_diff_ceil:
                min_diff = min_diff_floor.item()
                best_h = h_range[min_idx_floor].item()
                best_w = w_floor[min_idx_floor].item()
            else:
                min_diff = min_diff_ceil.item()
                best_h = h_range[min_idx_ceil].item()
                best_w = w_ceil[min_idx_ceil].item()
            
            # 检查是否有完全整除的情况
            divisible_mask = (N_f % h_range == 0)
            if divisible_mask.any():
                divisible_idx = torch.nonzero(divisible_mask).squeeze()
                best_h = h_range[divisible_idx[0]].item()
                best_w = (N_f // best_h)
                min_diff = 0
            
            logger.info(f"已确定最优空间布局: {best_h}x{best_w}，与目标尺寸差异: {min_diff}")
            
            # 步骤3：特征重排并插值（而非裁剪）
            features_spatial = final_features.permute(0, 2, 1).reshape(B_f, D_f, best_h, best_w)
            decoder_input = F.interpolate(
                features_spatial, 
                size=(target_h, target_w), 
                mode='bilinear', 
                align_corners=False
            )
            logger.info(f"已完成空间适配: {best_h}x{best_w} -> {target_h}x{target_w}")
        else:
            # 正常情况，直接重塑
            decoder_input = final_features.permute(0, 2, 1).reshape(B_f, D_f, target_h, target_w)
        
        # 11. 解码生成最终输出
        # 修改decoder调用:
        # - 不再使用加权sum
        # - 不再计算target_position_features
        # - 跳跃连接使用中心帧的特征而非整个序列
        logits, confidence = self.decoder(
            decoder_input,               # 现在是中心帧特征图
            selected_features[:-1],      # 使用选择的中心帧特征作为skip connections
            None,                        # 不需要target_t_ratio
            water_frequency              # 水体频率保持不变
        )
        
        # 返回训练代码期望的字典格式
        return {
            'inpaint': {
                'logits': logits,
                'confidence': confidence
            }
        }


class TemporalAttentionModule(nn.Module):
    """时间注意力模块 - 学习哪些帧对目标帧重建最重要"""
    
    def __init__(self, embed_dim, num_heads, max_frames=32, use_gru=True):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.max_frames = max_frames
        self.use_gru = use_gru
        
        # Query是目标帧，Key/Value是所有帧
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        
        # 位置编码 - 支持更灵活的帧数
        self.pos_embed = nn.Parameter(torch.randn(1, max_frames, embed_dim) * 0.02)
        
        # Multi-Scale Patch-Level Memory: 2层GRU + Residual + 跨batch记忆缓存
        if self.use_gru:
            # 堆叠2层GRU，每层独立处理不同时序尺度
            self.gru_layer1 = nn.GRU(embed_dim, embed_dim, num_layers=1, batch_first=True)
            self.gru_layer2 = nn.GRU(embed_dim, embed_dim, num_layers=1, batch_first=True)
            
            # Residual连接的投影层
            self.gru_residual_proj = nn.Linear(embed_dim, embed_dim)
            
            # GRU输出归一化
            self.gru_norm1 = nn.LayerNorm(embed_dim)
            self.gru_norm2 = nn.LayerNorm(embed_dim)
            
            # 跨batch记忆缓存 - 滚动窗口记忆机制
            # 使用register_buffer使其不参与梯度计算但会被保存/加载
            self.register_buffer('memory_cache_layer1', torch.zeros(1, 1, embed_dim))
            self.register_buffer('memory_cache_layer2', torch.zeros(1, 1, embed_dim))
            self.memory_decay = 0.95  # 记忆衰减因子
            
        # Dynamic-Region Guided Attention bias参数
        self.dynamic_attention_beta = 2.0  # attention bias强度
        
        # 输出投影
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        self.norm = nn.LayerNorm(embed_dim)
    
    def reset_memory_cache(self):
        """重置跨batch记忆缓存 - 在epoch开始或模式切换时调用"""
        if self.use_gru:
            self.memory_cache_layer1.zero_()
            self.memory_cache_layer2.zero_()
    
    def get_memory_info(self):
        """获取当前记忆缓存状态信息 - 用于调试和监控"""
        if not self.use_gru:
            return {"memory_enabled": False}
        
        return {
            "memory_enabled": True,
            "memory_decay": self.memory_decay,
            "layer1_norm": torch.norm(self.memory_cache_layer1).item(),
            "layer2_norm": torch.norm(self.memory_cache_layer2).item(),
            "dynamic_beta": self.dynamic_attention_beta
        }
        
    def forward(self, x, target_features, water_frequency=None):
        """
        Args:
            x: (B, T, N, D) - 输入序列特征
            target_features: (B, N, D) - 目标帧特征
            water_frequency: (B, H, W) - 水体频率图 (可选)
            
        Returns:
            weighted_x: (B, T, N, D) - 注意力加权后的特征
        """
        B, T, N, D = x.shape
        
        # 添加位置编码 - 增强对任意帧数的兼容性
        if T <= self.max_frames:
            # 直接使用预定义位置编码
            pos_embed = self.pos_embed[:, :T, :].unsqueeze(2)
        else:
            # 对于超长序列，使用插值扩展位置编码
            pos_embed = F.interpolate(
                self.pos_embed.transpose(1, 2),  # (1, D, max_frames)
                size=T,
                mode='linear',
                align_corners=False
            ).transpose(1, 2)  # (1, T, D)
            pos_embed = pos_embed.unsqueeze(2)
            
        x_with_pos = x + pos_embed
        
        # 计算动态区域掩码和分数 - 支持更精确的控制
        # 默认中性值：0.5（中性偏置为0）
        dynamic_mask = torch.full((B, N), 0.5, device=x.device, dtype=x.dtype)
        dynamic_score = torch.full((B, N), 0.5, device=x.device, dtype=x.dtype)
        
        if water_frequency is not None and self.use_gru:
            # 将water_frequency从(B, H, W)转换为patch级别的频率(B, N) - 向量化版本
            H_p = W_p = int(math.sqrt(N))  # 假设patch是正方形排列
            
            # 使用向量化插值操作
            freq_reshaped = F.interpolate(
                water_frequency.unsqueeze(1),  # (B, 1, H, W)
                size=(H_p, W_p),
                mode='bilinear',
                align_corners=False
            ).squeeze(1)  # (B, H_p, W_p)
            
            # 向量化重塑操作
            patch_frequencies = freq_reshaped.reshape(B, -1)  # (B, N)
            
            # 计算动态分数 - 使用loss.py中的compute_dynamic_degree函数
            # 向量化重塑和计算
            patch_frequencies_reshaped = patch_frequencies.reshape(B, H_p, W_p)
            dynamic_degree = compute_dynamic_degree(patch_frequencies_reshaped)  # (B, H_p, W_p)
            dynamic_score = dynamic_degree.reshape(B, -1)  # (B, N) - 重新展平
            
            # 向量化sigmoid计算
            dynamic_mask = torch.sigmoid(8 * (dynamic_score - 0.5))  # 更温和的阈值
        
        # 重塑输入以进行批量处理 (B*N, T, D)
        x_reshaped = x_with_pos.permute(0, 2, 1, 3).reshape(B*N, T, D)
        
        # Multi-Scale Patch-Level Memory: 2层GRU + Residual + 跨batch记忆
        if self.use_gru:
            # 第一层GRU - 短期时序模式
            # 使用记忆缓存作为初始hidden state
            h0_layer1 = self.memory_cache_layer1.expand(1, B*N, self.embed_dim).contiguous()
            gru_out1, h1_layer1 = self.gru_layer1(x_reshaped, h0_layer1)  # (B*N, T, D)
            gru_out1 = self.gru_norm1(gru_out1)
            
            # 第二层GRU - 长期时序模式，以第一层输出为输入
            h0_layer2 = self.memory_cache_layer2.expand(1, B*N, self.embed_dim).contiguous()
            gru_out2, h1_layer2 = self.gru_layer2(gru_out1, h0_layer2)  # (B*N, T, D)
            gru_out2 = self.gru_norm2(gru_out2)
            
            # Residual connection: 原始输入 + 两层GRU的组合输出
            residual_input = self.gru_residual_proj(x_reshaped)
            gru_final = residual_input + 0.3 * gru_out1 + 0.7 * gru_out2
            
            # 更新跨batch记忆缓存 - 使用最后时间步的平均hidden state
            # 应用记忆衰减机制，防止过度依赖历史信息
            with torch.no_grad():
                # 取最后时间步，对batch维度求平均作为全局记忆
                new_memory1 = h1_layer1.mean(dim=1, keepdim=True)  # (1, 1, D)
                new_memory2 = h1_layer2.mean(dim=1, keepdim=True)  # (1, 1, D)
                
                # 滚动更新记忆缓存
                self.memory_cache_layer1 = self.memory_decay * self.memory_cache_layer1 + (1 - self.memory_decay) * new_memory1
                self.memory_cache_layer2 = self.memory_decay * self.memory_cache_layer2 + (1 - self.memory_decay) * new_memory2
            
            # 重塑回原始维度 (B, N, T, D)
            gru_out = gru_final.reshape(B, N, T, D).permute(0, 2, 1, 3)
            
            # 应用动态掩码：只有动态区域使用GRU输出
            # dynamic_mask: (B, N) -> (B, 1, N, 1) for broadcasting
            mask_expanded = dynamic_mask.unsqueeze(1).unsqueeze(-1)
            
            # 混合原始特征和GRU输出
            x_with_gru = x_with_pos + 0.5 * mask_expanded * gru_out
        else:
            x_with_gru = x_with_pos
        
        # 批量计算注意力
        # 重塑目标特征 (B*N, D)
        target_reshaped = target_features.reshape(B*N, D)
        
        # 重塑序列特征用于注意力计算 (B*N, T, D)
        seq_reshaped = x_with_gru.permute(0, 2, 1, 3).reshape(B*N, T, D)
        
        # 计算QKV
        Q = self.q_proj(target_reshaped).unsqueeze(1)  # (B*N, 1, D)
        K = self.k_proj(seq_reshaped)  # (B*N, T, D)
        V = self.v_proj(seq_reshaped)  # (B*N, T, D)
        
        # 注意力权重计算
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(D)  # (B*N, 1, T)
        
        # Dynamic-Region Guided Attention: 添加动态区域注意力偏置
        # 使用公式: scores += (dynamic_score*2-1)*β，直接使用原始dynamic_score
        # 重塑原始dynamic_score为 (B*N, 1) 以匹配scores维度
        dynamic_score_reshaped = dynamic_score.reshape(B*N, 1)  # (B*N, 1)
        
        # 计算注意力偏置: (dynamic_score*2-1)*β
        # dynamic_score ∈ [0,1] → (dynamic_score*2-1) ∈ [-1,1]
        # 动态区域 (score≈1) 获得正偏置 +β，静态区域 (score≈0) 获得负偏置 -β
        # 中性区域 (score=0.5) 获得零偏置
        attention_bias = (dynamic_score_reshaped * 2.0 - 1.0) * self.dynamic_attention_beta
        attention_bias = attention_bias.unsqueeze(-1).expand(-1, -1, T)  # (B*N, 1, T)
        
        # 应用偏置，提升动态patch的注意力权重
        scores = scores + attention_bias
        
        # 数值稳定性检查和处理
        if torch.isnan(scores).any() or torch.isinf(scores).any():
            logger.warning("NaN/Inf detected in attention scores, applying correction")
            scores = torch.nan_to_num(scores, nan=0.0, posinf=10.0, neginf=-10.0)
        
        # 对 scores 做裁剪以提升数值稳定性
        scores = torch.clamp(scores, min=-80.0, max=80.0)
        attn_weights = F.softmax(scores, dim=-1)
        
        # 数值稳定性检查
        if torch.isnan(attn_weights).any() or torch.isinf(attn_weights).any():
            logger.warning("NaN/Inf detected in attention weights, using uniform weights")
            attn_weights = torch.ones_like(attn_weights) / T
        
        # 应用注意力
        attn_out = torch.matmul(attn_weights, V).squeeze(1)  # (B*N, D)
        
        # 数值稳定性检查
        if torch.isnan(attn_out).any() or torch.isinf(attn_out).any():
            logger.warning("NaN/Inf detected in attention output, applying correction")
            attn_out = torch.nan_to_num(attn_out, nan=0.0, posinf=1.0, neginf=-1.0)
            attn_out = torch.clamp(attn_out, -1.0, 1.0)
        
        attn_out = self.out_proj(attn_out)  # (B*N, D)
        
        # 重塑回原始维度并扩展到时间维度
        attn_out = attn_out.reshape(B, N, D).unsqueeze(1).expand(-1, T, -1, -1)  # (B, T, N, D)
        
        # 最终输出：原始特征 + 注意力增强
        weighted_x = x_with_gru + attn_out
        
        # 残差连接和归一化
        weighted_x = self.norm(weighted_x + x)
        
        return weighted_x


def create_swin_water_net(config):
    """Create Optimized SwinWaterNet from configuration for probability learning"""
    # 支持简单的配置对象或字典
    if hasattr(config, 'model'):
        model_config = config.model
    else:
        model_config = config.get('model', {})
    
    # 获取swin配置
    if hasattr(model_config, 'swin_config'):
        swin_config = model_config.swin_config
    else:
        swin_config = model_config.get('swin_config', {})
    
    # Extract memory optimization config
    if hasattr(model_config, 'get'):
        use_checkpoint = model_config.get('use_gradient_checkpoint', True)
        enable_fp16 = model_config.get('enable_fp16', False)
    else:
        use_checkpoint = getattr(model_config, 'use_gradient_checkpoint', True)
        enable_fp16 = getattr(model_config, 'enable_fp16', False)
        
    # 获取其他配置参数
    if isinstance(swin_config, dict):
        num_temporal_scales = swin_config.get('num_temporal_scales', 3)
        base_keep_rate = swin_config.get('base_keep_rate', 0.25)
        num_frames = swin_config.get('num_frames', 120)
        embed_dim = swin_config.get('embed_dim', 128)
        patch_size = swin_config.get('patch_size', 8)
        window_sizes = swin_config.get('window_sizes', [8, 8, 8, 8])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes
        depths = swin_config.get('depths', [2, 2, 6, 2])
        num_heads = swin_config.get('num_heads', [3, 6, 12, 24])
        img_size = swin_config.get('img_size', 256)
    else:
        # 支持对象属性访问
        num_temporal_scales = getattr(swin_config, 'num_temporal_scales', 3)
        base_keep_rate = getattr(swin_config, 'base_keep_rate', 0.25)
        num_frames = getattr(swin_config, 'num_frames', 120)
        embed_dim = getattr(swin_config, 'embed_dim', 128)
        patch_size = getattr(swin_config, 'patch_size', 8)
        window_sizes = getattr(swin_config, 'window_sizes', [8, 8, 8, 8])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes
        depths = getattr(swin_config, 'depths', [2, 2, 6, 2])
        num_heads = getattr(swin_config, 'num_heads', [3, 6, 12, 24])
        img_size = getattr(swin_config, 'img_size', 256)
    
    # 创建模型实例 - 传递正确的参数而不是字典
    model = OptimizedSwinWaterNet(
        img_size=img_size,
        patch_size=patch_size,
        embed_dim=embed_dim,
        depths=depths,
        num_heads=num_heads,
        window_size=window_size,
        num_frames=num_frames,
        num_temporal_scales=num_temporal_scales,
        base_keep_rate=base_keep_rate,
        use_checkpoint=use_checkpoint,
        out_chans=1  # 单通道输出概率值
    )
    
    # Enable mixed precision if configured
    if enable_fp16 and hasattr(model, 'enable_fp16'):
        model.enable_fp16()
    
    return model


# -----------------------------------------------------------------------------
# Backward-compatibility alias
# Many training scripts import `SwinWaterNet` directly. Expose it as an alias
# to the optimized implementation so that external imports remain valid.
# -----------------------------------------------------------------------------

SwinWaterNet = OptimizedSwinWaterNet

__all__ = [
    'OptimizedSwinWaterNet',
    'SwinWaterNet',
    'create_swin_water_net',
    'GeographicTemporalModulator',
]