"""
Swin Water Net v18: Enhanced Architecture with Attention Mask and Multi-scale Patch Attention

Key Features:
1. Attention mask mechanism using second channel of input_sequence
2. Multi-scale patch attention (4, 8, 16, 32) for first stage only
3. Center frame as Query, other frames as Key/Value in attention
4. Based on model_v14.py architecture with [2, 2, 6, 2] blocks
5. Fully end-to-end trainable with mixed precision support
"""


"""
如果划分了window，还需要多尺度patch_size吗？
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging

from decoder_v18 import Decoder

logger = logging.getLogger(__name__)


# ============================================================================
# Core Swin Transformer Components (from model_v14.py)
# ============================================================================

class CyclicShift(nn.Module):
    """Cyclic shift operation"""
    def __init__(self, displacement):
        super().__init__()
        self.displacement = displacement

    def forward(self, x):
        return torch.roll(x, shifts=(self.displacement, self.displacement), dims=(1, 2))


class Residual(nn.Module):
    """Residual connection"""
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(x, **kwargs) + x


class PreNorm(nn.Module):
    """Pre-normalization"""
    def __init__(self, dim, fn):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)


class FeedForward(nn.Module):
    """Feed forward network"""
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim),
        )

    def forward(self, x):
        return self.net(x)


def create_shifted_window_mask(H, W, window_size, shift_size):
    """Create attention mask for shifted windows"""
    if shift_size == 0:
        return None

    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    img_mask = torch.zeros((1, 1, H_padded, W_padded))

    # FULLY VECTORIZED: Create mask using tensor operations
    # Create region masks for each slice combination
    h_region = torch.zeros(H_padded, dtype=torch.long)
    w_region = torch.zeros(W_padded, dtype=torch.long)

    # Assign region indices based on slice boundaries
    h_region[:-window_size] = 0
    h_region[-window_size:-shift_size] = 1
    h_region[-shift_size:] = 2

    w_region[:-window_size] = 0
    w_region[-window_size:-shift_size] = 1
    w_region[-shift_size:] = 2

    # Create 2D region map
    h_grid, w_grid = torch.meshgrid(h_region, w_region, indexing='ij')
    region_map = h_grid * 3 + w_grid  # Combine h and w regions into single index

    # Set the mask
    img_mask[0, 0, :, :] = region_map

    mask_windows = window_partition(img_mask, window_size)
    mask_windows = mask_windows.view(-1, window_size * window_size)
    attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
    attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))

    return attn_mask


def window_partition(x, window_size):
    """Partition input into windows"""
    B, C, H, W = x.shape
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size

    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W

    x = x.view(B, C, H_padded // window_size, window_size, W_padded // window_size, window_size)
    windows = x.permute(0, 2, 4, 1, 3, 5).contiguous().view(-1, C, window_size, window_size)
    return windows


def window_reverse(windows, window_size, H, W):
    """Reverse window partition"""
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    C = windows.shape[1]
    x = windows.view(B, H_padded // window_size, W_padded // window_size, C, window_size, window_size)
    x = x.permute(0, 3, 1, 4, 2, 5).contiguous().view(B, C, H_padded, W_padded)

    if pad_h > 0 or pad_w > 0:
        x = x[:, :, :H, :W]

    return x


# ============================================================================
# Attention Mask Creation (adapted from model_v17.py)
# ============================================================================

def create_attention_mask(mask_channel, patch_size):
    """
    Create attention mask for missing regions
    
    Args:
        mask_channel: (b, t, 1, h, w) - 1 for missing, 0 for valid
                      excluding center frame center frame
        patch_size: patch size
    
    Returns:
        attn_mask: (b, t*out_h*out_w, t*out_h*out_w) attention mask
    """

    # mask_channel: (b, t, 1, h, w) - 1 for missing, 0 for valid
    b, t, c, h, w = mask_channel.shape
    out_h, out_w = h // patch_size, w // patch_size

    # Check if dimensions are valid
    if out_h == 0 or out_w == 0:
        return None

    # Reshape mask to patch level: (b, t, 1, h, w) -> (b, t, 1, out_h, patch_h, out_w, patch_w)
    mask_reshaped = mask_channel.view(b, t, c, out_h, patch_size, out_w, patch_size)
    # Permute to (b, t, out_h, out_w, 1, patch_h, patch_w)
    mask_patches = mask_reshaped.permute(0, 1, 3, 5, 2, 4, 6).contiguous()
    # Flatten to patch level: (b, t*out_h*out_w, patch_h*patch_w)
    mask_patches = mask_patches.view(b, t * out_h * out_w, patch_size * patch_size)

    # Determine missing patches: mean > 0.5
    patch_missing = (mask_patches.mean(-1) > 0.5)  # (b, t*out_h*out_w)

    # Create attention mask:
    # Query's missing regions should only attend to Key's non-missing regions
    # Query's non-missing regions can normally attend to Key's non-missing regions
    # Key's missing regions should NOT be attended to by any query region

    # Create mask that should not be attended to (missing keys)
    attn_mask = patch_missing.unsqueeze(1).repeat(1, out_h*out_w, 1)  # (b, t*out_h*out_w, t*out_h*out_w)

    return attn_mask


# ============================================================================
# Multi-scale Attention with Proper Mask Processing
# ============================================================================

class MultiScaleAttention(nn.Module):
    """
    Multi-scale attention with center frame as query and spatiotemporal relative position bias
    """
    def __init__(self, dim, patch_sizes=[4, 8, 16, 32], num_heads=8, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.patch_sizes = patch_sizes
        self.num_heads = num_heads
        self.num_scales = len(patch_sizes)
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5

        self.query_embedding = nn.Conv2d(dim, dim, kernel_size=1, padding=0)
        self.key_embedding = nn.Conv2d(dim, dim, kernel_size=1, padding=0)
        self.value_embedding = nn.Conv2d(dim, dim, kernel_size=1, padding=0)

        self.output_linear = nn.Sequential(
            nn.Conv2d(dim, dim, kernel_size=3, padding=1),
            nn.GELU()
        )

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj_drop = nn.Dropout(proj_drop)

        # Shared adaptive spatiotemporal bias encoders for all patch sizes

        # 1. Single shared spatial encoder for all patch sizes
        # Uses patch_size_embedding to distinguish different scales
        self.shared_spatial_encoder = nn.Sequential(
            nn.Linear(3, 64),  # Input: (rel_h_norm, rel_w_norm, patch_size_embed)
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, num_heads)  # Output: bias for each head
        )

        # 2. Single shared temporal encoder for all patch sizes
        self.shared_temporal_encoder = nn.Sequential(
            nn.Linear(2, 64),  # Input: (temporal distance, patch_size_embed)
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, num_heads)  # Output: bias for each head
        )

        # Patch size embedding for spatial encoder
        self.patch_size_embedding = nn.Embedding(len(patch_sizes), 1)
        # Initialize patch size embeddings
        with torch.no_grad():
            for i, ps in enumerate(patch_sizes):
                self.patch_size_embedding.weight[i] = torch.log(torch.tensor(ps, dtype=torch.float32))
    
    def forward(self, target_features, context_features, context_mask, center_frame_idx):
        d_k = self.dim // self.num_scales
        output = []

        # target_features: (B, C, H, W)
        # context_features: (B*T, C, H, W)
        # context_mask: (B, T, 1, H, W)
        b, _, h, w = target_features.shape
        _, t_context, _, _, _ = context_features.shape
        context_features = context_features.view(b*t_context, *context_features.shape[2:])

        _query = self.query_embedding(target_features)  # (B, dim, H, W)
        _key = self.key_embedding(context_features)     # (B*T, dim, H, W)
        _value = self.value_embedding(context_features) # (B*T, dim, H, W)

        for patch_size, query, key, value in zip(self.patch_sizes,
                                                torch.chunk(_query, self.num_scales, dim=1),
                                                torch.chunk(_key, self.num_scales, dim=1),
                                                torch.chunk(_value, self.num_scales, dim=1)):
            out_h, out_w = h // patch_size, w // patch_size
            """
            用create_attention_mask创建注意力掩码
            """
            attention_mask = create_attention_mask(context_mask, patch_size)

            # Reshape to patches for multi-head attention
            query = query.view(b, d_k, out_h, patch_size, out_w, patch_size)  # Only center frame
            query = query.permute(0, 2, 4, 1, 3, 5).contiguous().view(
                b, out_h*out_w, d_k*patch_size*patch_size)

            # Reshape key and value from (B*T, d_k, H, W) to patches
            key = key.view(b, t_context, d_k, out_h, patch_size, out_w, patch_size)
            key = key.permute(0, 1, 3, 5, 2, 4, 6).contiguous().view(
                b, t_context*out_h*out_w, d_k*patch_size*patch_size)

            value = value.view(b, t_context, d_k, out_h, patch_size, out_w, patch_size)
            value = value.permute(0, 1, 3, 5, 2, 4, 6).contiguous().view(
                b, t_context*out_h*out_w, d_k*patch_size*patch_size)

            # Reshape for multi-head attention
            query = query.view(b, out_h*out_w, self.num_heads, d_k*patch_size*patch_size//self.num_heads).transpose(1, 2)
            key = key.view(b, t_context*out_h*out_w, self.num_heads, d_k*patch_size*patch_size//self.num_heads).transpose(1, 2)
            value = value.view(b, t_context*out_h*out_w, self.num_heads, d_k*patch_size*patch_size//self.num_heads).transpose(1, 2)

            # Compute attention scores with scaling
            query = query * self.scale
            scores = torch.matmul(query, key.transpose(-2, -1))  # (B, num_heads, out_h*out_w, t_context*out_h*out_w)

            # Add spatiotemporal relative position bias
            scores = self._add_spatiotemporal_bias(scores, patch_size, out_h, out_w, t_context, b, center_frame_idx)

            # Apply attention mask
            if attention_mask is not None:
                # attention_mask shape: (b, t*out_h*out_w, t*out_h*out_w)
                # We need to extract the part for center frame queries: (b, out_h*out_w, t*out_h*out_w)
                # Since we only have center frame as query, we take the first out_h*out_w rows

                # Reshape attention mask to match multi-head format
                mask_expanded = attention_mask.unsqueeze(1).repeat(1, self.num_heads, 1, 1)
                scores = scores.masked_fill(mask_expanded, -1e9)

            p_attn = F.softmax(scores, dim=-1)
            p_attn = self.attn_drop(p_attn)

            # Apply attention
            y = torch.matmul(p_attn, value)  # (B, num_heads, out_h*out_w, head_dim)

            # Reshape back to spatial format
            y = y.transpose(1, 2).contiguous().view(b, out_h*out_w, d_k*patch_size*patch_size)
            y = y.view(b, 1, out_h, out_w, d_k, patch_size, patch_size)  # Only center frame
            y = y.permute(0, 1, 4, 2, 5, 3, 6).contiguous().view(b, d_k, h, w)  # Remove T dimension

            output.append(y)

        # Concatenate outputs from all scales
        output = torch.cat(output, 1)  # (B, dim, h, w)
        x = self.output_linear(output)
        x = self.proj_drop(x)

        return x

    def _add_spatiotemporal_bias(self, scores, patch_size, out_h, out_w, t_context, b, center_frame_idx=None):
        """
        Add unified spatiotemporal relative position bias using fully vectorized computation

        Unified approach: All relationships use spatial_bias × temporal_bias
        - Uses learnable encoders for both spatial and temporal relationships
        - Fully vectorized computation without loops
        - Considers actual temporal distance from center frame

        Args:
            scores: (B, num_heads, out_h*out_w, t_context*out_h*out_w) attention scores
            patch_size: current patch size
            out_h, out_w: spatial dimensions in patches
            t_context: number of context frames
            b: batch size
            center_frame_idx: (B,) center frame indices for each batch item
        """
        device = scores.device
        dtype = scores.dtype

        # Get shared encoders (same for all patch sizes)
        spatial_encoder = self.shared_spatial_encoder
        temporal_encoder = self.shared_temporal_encoder

        # 1. Compute all spatial relative positions (vectorized with normalization)
        query_coords = torch.arange(out_h * out_w, device=device)
        query_h = query_coords // out_w  # (out_h*out_w,)
        query_w = query_coords % out_w   # (out_h*out_w,)

        key_coords = torch.arange(out_h * out_w, device=device)
        key_h = key_coords // out_w  # (out_h*out_w,)
        key_w = key_coords % out_w   # (out_h*out_w,)

        # Broadcasting: (out_h*out_w, 1) - (1, out_h*out_w) = (out_h*out_w, out_h*out_w)
        rel_h_all = query_h.unsqueeze(1) - key_h.unsqueeze(0)  # (out_h*out_w, out_h*out_w)
        rel_w_all = query_w.unsqueeze(1) - key_w.unsqueeze(0)  # (out_h*out_w, out_h*out_w)

        # Normalize relative positions to handle different patch sizes
        max_rel_h = max(out_h - 1, 1)  # Avoid division by zero
        max_rel_w = max(out_w - 1, 1)
        rel_h_norm = rel_h_all.float() / max_rel_h  # Normalize to [-1, 1]
        rel_w_norm = rel_w_all.float() / max_rel_w  # Normalize to [-1, 1]

        # Get patch size embedding
        patch_size_idx = self.patch_sizes.index(patch_size)
        patch_size_embed = self.patch_size_embedding(torch.tensor(patch_size_idx, device=device))  # (1,)

        # Expand patch size embedding to match spatial dimensions
        patch_size_embed_expanded = patch_size_embed.expand(out_h*out_w, out_h*out_w)  # (out_h*out_w, out_h*out_w)

        # Stack spatial relative positions with patch size info: (out_h*out_w, out_h*out_w, 3)
        spatial_rel_all = torch.stack([rel_h_norm, rel_w_norm, patch_size_embed_expanded], dim=-1)

        # Reshape for batch processing: (out_h*out_w * out_h*out_w, 3)
        spatial_rel_flat = spatial_rel_all.reshape(-1, 3)

        # Get spatial bias for all relationships using patch-size-aware encoder
        spatial_bias_flat = spatial_encoder(spatial_rel_flat)  # (out_h*out_w * out_h*out_w, num_heads)

        # Reshape back: (out_h*out_w, out_h*out_w, num_heads)
        spatial_bias_all = spatial_bias_flat.reshape(out_h*out_w, out_h*out_w, self.num_heads)

        # 2. Compute temporal distances (vectorized)
        # Create temporal distance matrix for all context frames
        # context_frame_indices: (t_context,) - indices of context frames
        context_frame_indices = torch.arange(t_context, device=device)

        # Expand for batch processing: (b, t_context)
        center_expanded = center_frame_idx.unsqueeze(1)  # (b, 1)
        context_expanded = context_frame_indices.unsqueeze(0)  # (1, t_context)

        # Compute temporal distances: (b, t_context)
        temporal_distances = torch.abs(context_expanded - center_expanded).float()

        # Get patch size embedding for temporal encoder
        patch_size_idx = self.patch_sizes.index(patch_size)
        patch_size_embed = self.patch_size_embedding(torch.tensor(patch_size_idx, device=device))  # (1,)

        # Prepare temporal encoder input: (temporal_distance, patch_size_embed)
        # Reshape temporal distances: (b * t_context, 1)
        temporal_distances_flat = temporal_distances.reshape(-1, 1)

        # Expand patch_size_embed to match temporal distances: (b * t_context, 1)
        patch_size_embed_expanded = patch_size_embed.expand(b * t_context, 1)

        # Combine temporal distance and patch size info: (b * t_context, 2)
        temporal_encoder_input = torch.cat([temporal_distances_flat, patch_size_embed_expanded], dim=1)

        # Get temporal bias using encoder: (b * t_context, num_heads)
        temporal_bias_flat = temporal_encoder(temporal_encoder_input)

        # Reshape back: (b, t_context, num_heads)
        temporal_bias_all = temporal_bias_flat.reshape(b, t_context, self.num_heads)

        # 3. Combine spatial and temporal bias (fully vectorized - no loops!)
        # spatial_bias_all: (out_h*out_w, out_h*out_w, num_heads)
        # temporal_bias_all: (b, t_context, num_heads)

        # Reshape spatial bias for broadcasting: (1, num_heads, out_h*out_w, out_h*out_w)
        spatial_bias_broadcast = spatial_bias_all.permute(2, 0, 1).unsqueeze(0)

        # Reshape temporal bias for broadcasting: (b, t_context, num_heads, 1, 1)
        temporal_bias_broadcast = temporal_bias_all.unsqueeze(-1).unsqueeze(-1)

        # Vectorized combination using broadcasting
        # spatial_bias_broadcast: (1, num_heads, out_h*out_w, out_h*out_w)
        # temporal_bias_broadcast: (b, t_context, num_heads, 1, 1)
        # Result: (b, t_context, num_heads, out_h*out_w, out_h*out_w)
        combined_bias_all = spatial_bias_broadcast * temporal_bias_broadcast

        # Reshape to final format: (b, num_heads, out_h*out_w, t_context*out_h*out_w)
        # First permute to: (b, num_heads, t_context, out_h*out_w, out_h*out_w)
        combined_bias_permuted = combined_bias_all.permute(0, 2, 1, 3, 4)

        # Then reshape to interleave temporal and spatial dimensions
        total_bias = combined_bias_permuted.reshape(b, self.num_heads, out_h*out_w, t_context*out_h*out_w)

        return scores + total_bias
    
class _MultiScaleAttention(nn.Module):
    """
    Deprecated: Multi-scale attention with center frame as query
    """

    def __init__(self, dim, patch_sizes=[4, 8, 16, 32], num_heads=8, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.patch_sizes = patch_sizes
        self.num_heads = num_heads
        self.num_scales = len(patch_sizes)

        # Each scale gets equal portion of channels
        self.d_k = dim // self.num_scales
        head_dim = self.d_k // num_heads
        self.scale = head_dim ** -0.5

        # Separate embeddings for query, key, value (following STTN)
        total_dim = dim * len(patch_sizes)  # Total dimension after concatenation
        self.query_embedding = nn.Conv2d(total_dim, dim, kernel_size=1, padding=0)
        self.key_embedding = nn.Conv2d(total_dim, dim, kernel_size=1, padding=0)
        self.value_embedding = nn.Conv2d(total_dim, dim, kernel_size=1, padding=0)

        # Output projection (following STTN)
        self.output_linear = nn.Sequential(
            nn.Conv2d(dim, dim, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2, inplace=True)
        )

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, center_patches, context_patches, temporal_mask, H_patches, W_patches, center_frame_idx=None):
        """
        Args:
            center_patches: Dict of {patch_size: (B, N, D)} center frame patches
            context_patches: Dict of {patch_size: (B, T, N, D)} context frame patches
            mask: (B, T, 1, H, W) temporal missing mask - 1 for missing, 0 for valid
            H_patches, W_patches: patch grid dimensions (number of patches in each direction)
            center_frame_idx: (B,) center frame indices for each batch item
        """
        B = list(center_patches.values())[0].shape[0]
        T = list(context_patches.values())[0].shape[1]
        
        # For multi-scale attention, we need the original spatial dimensions
        min_patch_size = min(self.patch_sizes)
        # Image size
        H = W = H_patches * min_patch_size
        
        # Convert all patches to spatial format and concatenate for Q, K, V generation
        center_features = []
        context_features = []
        
        for patch_size in self.patch_sizes:
            center_patch = center_patches[patch_size]  # (B, N, D)
            shape = center_patch.shape
            context_patch = context_patches[patch_size]  # (B, T, N, D)
            
            out_h, out_w = H // patch_size, W // patch_size
            
            # Convert to spatial format
            center_spatial = center_patch.reshape(B, out_h, out_w, self.dim).permute(0, 3, 1, 2)  # (B, D, out_h, out_w)
            context_spatial = context_patch.reshape(B * T, out_h, out_w, self.dim).permute(0, 3, 1, 2)  # (B*T, D, out_h, out_w)
            
            # Interpolate to common resolution (use the finest scale)
            target_h, target_w = max(1, H // min_patch_size), max(1, W // min_patch_size)
            target_size = (target_h, target_w)
            '''
            这里插值实现不够优雅，能否通过维度重排使得维度恢复为原图像大小
            '''
            center_spatial = F.interpolate(center_spatial, size=target_size, mode='bilinear', align_corners=False)
            context_spatial = F.interpolate(context_spatial, size=target_size, mode='bilinear', align_corners=False)
            
            center_features.append(center_spatial)
            context_features.append(context_spatial)
        
        # Concatenate features from all scales
        center_combined = torch.cat(center_features, dim=1)  # (B, D*num_scales, target_h, target_w)
        context_combined = torch.cat(context_features, dim=1)  # (B*T, D*num_scales, target_h, target_w)
        
        # Generate Q, K, V using STTN approach
        bt, _, h, w = context_combined.size()
        d_k = self.dim // len(self.patch_sizes)
        
        output = []
        '''
        STTN是先做embedding后重组，而这里是先重组再做embedding，因此需要插值
        '''
        _query = self.query_embedding(center_combined)  # (B, dim, h, w)
        _key = self.key_embedding(context_combined)     # (B*T, dim, h, w)
        _value = self.value_embedding(context_combined) # (B*T, dim, h, w)
        
        # Process each scale following STTN approach
        for i, (ph, pw) in enumerate([(ps, ps) for ps in self.patch_sizes]):
            # Extract features for this scale
            query = _query[:, i*d_k:(i+1)*d_k]  # (B, d_k, h, w)
            key = _key[:, i*d_k:(i+1)*d_k]      # (B*T, d_k, h, w)  
            value = _value[:, i*d_k:(i+1)*d_k]  # (B*T, d_k, h, w)
            
            # Calculate patch grid size for this scale
            # out_w, out_h = w // pw, h // ph
            
            _, _, _, h_origin, w_origin = temporal_mask.shape
            # Process mask following STTN approach with complete attention rules
            # temporal_mask shape: (B, T, 1, H, W) - 1 for missing, 0 for valid
            # But context has T-1 frames (excluding center), so we need to adjust

            # Extract the mask for context frames (excluding center frame)
            _, T_mask = temporal_mask.shape[:2]

            # Create mask for context frames matching context_patches shape
            # We need to exclude the center frame from temporal_mask
            device = temporal_mask.device

            # Use the actual center frame indices for each batch item
            # For simplicity, we'll use the first batch item's center_frame_idx
            # since all items in the batch should have the same center frame index
            center_idx = center_frame_idx[0].item()

            # Create indices for all frames except center
            all_indices = torch.arange(T_mask, device=device)
            context_indices = torch.cat([all_indices[:center_idx], all_indices[center_idx+1:]])
            
            # Extract context mask: (B, T-1, 1, H, W)
            context_mask = temporal_mask[:, context_indices]
            shape = context_mask.shape
            B_context, T_context = context_mask.shape[:2]
            
            out_h, out_w = h_origin // ph, w_origin // pw

            mm = context_mask.view(B_context, T_context, 1, out_h, ph, out_w, pw)
            # Interpolate mask to match current resolution
            # target_h, target_w = out_h*ph, out_w*pw
            # mask_resized = F.interpolate(context_mask.reshape(B_context*T_context, 1, H, W).float(),
            #                            size=(target_h, target_w), mode='nearest')
            mm = mm.permute(0, 1, 3, 5, 2, 4, 6).contiguous().view(B_context, T_context*out_h*out_w, ph*pw)
            
            shape = mm.shape

            # Average over patch pixels and create binary mask
            attn_mask = (mm.mean(-1) > 0.5).unsqueeze(1).repeat(1, T_context*out_h*out_w, 1)
            # Create attention mask following the three rules:
            # Rule 1: Query's missing regions should only attend to Key's non-missing regions
            # Rule 2: Query's non-missing regions can normally attend to Key's non-missing regions  
            # Rule 3: Key's missing regions should NOT be attended to by any query region
            

            # This automatically implements Rule 3: missing keys are masked out for all queries
            # Rules 1 and 2 are naturally handled since we only mask missing keys
            # (missing query regions will only see valid keys, valid query regions will see valid keys normally)

            
            # Reshape to patches following STTN approach
            # Note: For query, we only use center frame (not T frames)
            shape = query.shape
            d_k, out_h, ph, out_w, pw = query.shape[1:]
            query.shape
            query = query.view(B, 1, d_k, out_h, ph, out_w, pw)  # Only center frame
            query = query.permute(0, 1, 3, 5, 2, 4, 6).contiguous().view(
                B, 1*out_h*out_w, d_k*ph*pw)

            key = key.view(B, T, d_k, out_h, ph, out_w, pw)
            key = key.permute(0, 1, 3, 5, 2, 4, 6).contiguous().view(
                B, T*out_h*out_w, d_k*ph*pw)

            value = value.view(B, T, d_k, out_h, ph, out_w, pw)
            value = value.permute(0, 1, 3, 5, 2, 4, 6).contiguous().view(
                B, T*out_h*out_w, d_k*ph*pw)
            
            # Apply attention following STTN approach
            scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(query.size(-1))
            if mm is not None:
                scores.masked_fill(mm, -1e9)
            p_attn = F.softmax(scores, dim=-1)
            p_attn = self.attn_drop(p_attn)

            y = torch.matmul(p_attn, value)
            
            # Reshape back following STTN approach
            y = y.view(B, 1, out_h, out_w, d_k, ph, pw)  # Center frame only
            y = y.permute(0, 1, 4, 2, 5, 3, 6).contiguous().view(B, d_k, h, w)  # Remove T dimension
            
            output.append(y)
        
        # Concatenate outputs from all scales
        output = torch.cat(output, 1)  # (B, dim, h, w)
        x = self.output_linear(output)
        x = self.proj_drop(x)
        
        # Interpolate back to original resolution
        x = F.interpolate(x, size=(H, W), mode='bilinear', align_corners=False)
        
        return x


# ============================================================================
# Spatiotemporal Window Attention with Attention Mask
# ============================================================================

class _SpatiotemporalWindowAttention(nn.Module):
    """
    Desprected:
    Window attention with center frame as Query, other frames as Key/Value and attention mask
    """
    
    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        # Separate projections for Query (center) and Key/Value (context)
        self.q_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv_proj = nn.Linear(dim, dim * 2, bias=qkv_bias)
        
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
        
        # Relative position bias
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )
        
        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)
        
        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
    
    def forward(self, center_x, context_x, mask=None, attention_mask=None):
        """
        Args:
            center_x: (B_, N, C) center frame windows
            context_x: (B_, T_c*N, C) context frame windows
            mask: window-based attention mask for shifted windows
            attention_mask: (B, 1, H, W) missing mask for attention computation
        """
        B_, N, C = center_x.shape
        T_c = context_x.shape[1] // N

        # Query from center frame
        q = self.q_proj(center_x).reshape(B_, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)

        # Key/Value from context frames
        kv = self.kv_proj(context_x).reshape(B_, T_c * N, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]  # Each: (B_, heads, T_c*N, head_dim)
        
        # Attention computation
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))  # (B_, heads, N, T_c*N)
        
        # Add relative position bias (only for spatial dimensions within windows)
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        
        # For spatiotemporal attention, we tile the bias across temporal dimension
        if N == self.window_size * self.window_size:
            bias_expanded = relative_position_bias.unsqueeze(0).repeat(B_, 1, 1, T_c).view(B_, self.num_heads, N, T_c * N)
            attn = attn + bias_expanded
        
        # Apply attention mask based on missing regions
        if attention_mask is not None:
            # attention_mask: (B, 1, H, W) with 1 for missing, 0 for valid
            H = W = int(math.sqrt(N))
            b = B_ // (H * W // (self.window_size * self.window_size))
            
            # Create mask for missing regions at window level
            attn_mask = create_attention_mask(attention_mask, self.window_size, self.window_size, b, T_c, H, W)
            if attn_mask is not None:
                # Reshape for window-based attention
                nW = B_ // b
                attn_mask = attn_mask.view(b, nW, -1, T_c * N).transpose(1, 2)  # (b, N, nW, T_c*N)
                attn_mask = attn_mask.contiguous().view(-1, N, T_c * N)  # (b*nW, N, T_c*N)
                attn_mask = attn_mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)  # (b*nW, heads, N, T_c*N)
                attn = attn.masked_fill(~attn_mask, -1e9)
        
        # Apply window-based shifted attention mask
        if mask is not None:
            nW = mask.shape[0]
            # For spatiotemporal attention, we need to handle the mask differently
            # The mask is designed for spatial attention (N x N), but we have (N x T_c*N)
            # We expand the mask to cover the temporal dimension
            if T_c > 1:
                # Expand mask to temporal dimension: (nW, N, N) -> (nW, N, T_c*N)
                expanded_mask = mask.unsqueeze(-1).repeat(1, 1, 1, T_c).view(nW, N, T_c * N)
                attn = attn.view(B_ // nW, nW, self.num_heads, N, T_c * N) + expanded_mask.unsqueeze(1).unsqueeze(0)
            else:
                # Standard case for spatial attention
                attn = attn.view(B_ // nW, nW, self.num_heads, N, T_c * N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, T_c * N)
        
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


class SwinTransformerBlock(nn.Module):
    """Swin Transformer Block with multi-scale and spatiotemporal attention"""
    
    def __init__(self, dim, num_heads, window_size=8, shift_size=0, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.GroupNorm,
                 use_multiscale=True, patch_sizes=[4, 8, 16, 32]):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio
        self.use_multiscale = use_multiscale
        
        self.norm1 = norm_layer(1, dim)
        
        if use_multiscale:
            # Multi-scale patch attention (for first stage only)
            self.attn = MultiScaleAttention(
                dim, patch_sizes=patch_sizes, num_heads=num_heads,
                attn_drop=attn_drop, proj_drop=drop)
        else:
            # Standard window attention
            self.attn = WindowAttention(
                dim, window_size=window_size, num_heads=num_heads,
                qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        
        self.drop_path = nn.Identity() if drop_path <= 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(1, dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )
    
    def forward(self, x, context_features=None, context_mask=None, center_frame_idx=None):
        """
        Args:
            x: (B, L, C) input features (center frame for spatiotemporal)
            H, W: spatial dimensions
            context_x: (B, T_c, L, C) context features for spatiotemporal attention
            temporal_mask: (B, T, 1, H, W) temporal missing mask for multi-scale attention
            center_patches: Dict of {patch_size: (B, N, D)} center frame patches for multi-scale
            context_patches: Dict of {patch_size: (B, T, N, D)} context frame patches for multi-scale
            center_frame_idx: (B,) center frame indices for each batch item
        """
        B, C, H, W = x.shape
        x = self.norm1(x)

        shortcut = x
        
            
        # Standard window-based attention
        # x = x.reshape(B, H, W, C)
        
        # Create attention mask for shifted windows
        attn_mask = None
        if self.shift_size > 0:
            attn_mask = create_shifted_window_mask(H, W, self.window_size, self.shift_size)
            attn_mask = attn_mask.to(x.device)
        
        # Cyclic shift
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(2, 3))
        else:
            shifted_x = x
        
        # Window partition
        x_windows = window_partition(shifted_x, self.window_size)
        x_windows = x_windows.permute(0, 2, 3, 1).reshape(-1, self.window_size * self.window_size, C)
        
        if self.use_multiscale and context_features is not None:
            # Process context frames
            B, T_c, C, H, W = context_features.shape
            context_features = context_features.view(B*T_c, C, H, W)
            context_features = self.norm1(context_features)

            context_mask = context_mask.view(B * T_c, 1, H, W)  # (B*T_c, H, W)

            # Apply cyclic shift to all frames at once if needed
            if self.shift_size > 0:
                context_features = torch.roll(context_features, shifts=(-self.shift_size, -self.shift_size), dims=(2, 3))

            # Window partition for all context frames simultaneously
            context_windows = window_partition(context_features, self.window_size)  # (B*T_c*nW, C, window_size, window_size)
            # context_windows = context_windows.permute(0, 2, 3, 1).reshape(-1, self.window_size * self.window_size, C)  # (B*T_c*nW, window_size^2, C)

            context_mask_windows = window_partition(context_mask, self.window_size)  # (B*T_c*nW, C, window_size, window_size)
            # context_mask_windows = context_mask_windows.squeeze(1).reshape(-1, self.window_size * self.window_size)  # (B*T_c*nW, window_size^2)

            # Reshape to group by spatial windows: (B*nW, T_c, window_size^2, C)
            # nW = context_windows.shape[0] // (B * T_c)
            # context_windows = context_windows.view(B, T_c, nW, self.window_size * self.window_size, C)
            # context_windows = context_windows.permute(0, 2, 1, 3, 4).contiguous()  # (B, nW, T_c, window_size^2, C)
            # context_windows = context_windows.view(B * nW, T_c * self.window_size * self.window_size, C)
            
            # context_mask_windows = context_mask_windows.view(B, nW, T_c, self.window_size * self.window_size)
            # context_mask_windows = context_mask_windows.permute(0, 2, 1, 3).contiguous()  # (B, T_c, nW, window_size^2)
            # context_mask_windows = context_mask_windows.view(B * T_c, nW * self.window_size * self.window_size)  # (B*T_c, nW*window_size^2)

            # Spatiotemporal attention with attention mask
            context_windows = context_windows.view(-1, T_c, C, self.window_size, self.window_size)
            context_mask_windows = context_mask_windows.view(-1, T_c, 1, self.window_size, self.window_size)
            x_windows = x_windows.permute(0, 2, 1).reshape(-1, C, self.window_size, self.window_size)
            attn_windows = self.attn(x_windows, context_windows, context_mask_windows, center_frame_idx)
        else:
            # Standard window attention
            attn_windows = self.attn(x_windows, mask=attn_mask)
            
        # Merge windows
        attn_windows = attn_windows.reshape(-1, self.window_size, self.window_size, C)
        shifted_x = window_reverse(attn_windows, self.window_size, H, W)
        
        # Reverse cyclic shift
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x
        
        x = x.reshape(B, H * W, C)
        
        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        
        return x


class WindowAttention(nn.Module):
    """Standard window-based multi-head self attention"""
    
    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
        
        # Relative position bias
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )
        
        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)
        
        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
    
    def forward(self, x, mask=None):
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)
        
        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = F.softmax(attn, dim=-1)
        else:
            attn = F.softmax(attn, dim=-1)
        
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


# ============================================================================
# Patch Merging Layer
# ============================================================================

class PatchMerging(nn.Module):
    """Patch merging layer"""
    
    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)
    
    def forward(self, x):
        """
        Args:
            x: (B, H*W, C)
        Returns:
            x: (B, (H/2)*(W/2), 2*C)
        """
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        assert H % 2 == 0 and W % 2 == 0, f"x size ({H}*{W}) are not even."
        
        x = x.view(B, H, W, C)
        
        # Extract 4 sub-patches
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C
        
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C
        
        x = self.norm(x)
        x = self.reduction(x)  # B H/2*W/2 2*C
        
        return x


# ============================================================================
# Swin Encoder with Multi-scale and Spatiotemporal Processing
# ============================================================================

class FeaturesExtractor(nn.Module):
    """Extract features from center and context frames"""
    
    def __init__(self, embed_dim):
        super().__init__()
        self.embed_dim = embed_dim
        self.extractor = nn.Sequential(
            nn.Conv2d(2, embed_dim//4, kernel_size=3, stride=2, padding=1),
            nn.GroupNorm(8, embed_dim//4),
            nn.GELU(),
            nn.Conv2d(embed_dim//4, embed_dim//4, kernel_size=3, stride=1, padding=1),
            nn.GroupNorm(8, embed_dim//4),
            nn.GELU(),
            nn.Conv2d(embed_dim//4, embed_dim//2, kernel_size=3, stride=2, padding=1),
            nn.GroupNorm(8, embed_dim//2),
            nn.GELU(),
            nn.Conv2d(embed_dim//2, embed_dim, kernel_size=3, stride=1, padding=1),
            nn.GroupNorm(8, embed_dim),
            nn.GELU()
        )
    def forward(self, x):
        return self.extractor(x)

class Encoder(nn.Module):
    """Swin encoder with [2, 2, 6, 2] structure, multi-scale and spatiotemporal processing"""
    
    def __init__(self, embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24],
                 window_size=8, mlp_ratio=4., drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 use_multiscale=True, patch_sizes=[4, 8, 16, 32]):
        super().__init__()
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_layers = len(depths)
        self.use_multiscale = use_multiscale
        self.patch_sizes = patch_sizes
        
        # Build layers
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        self.layers = nn.ModuleList()
        self.downsample_layers = nn.ModuleList()
        
        for i_layer in range(self.num_layers):
            # First stage uses multi-scale attention, others use spatiotemporal
            use_multiscale = use_multiscale and (i_layer == 0)
            
            layer_blocks = nn.ModuleList()
            for i in range(depths[i_layer]):
                layer_blocks.append(
                    SwinTransformerBlock(
                        dim=int(embed_dim * 2 ** i_layer),
                        num_heads=num_heads[i_layer],
                        window_size=window_size,
                        shift_size=0 if (i % 2 == 0) else window_size // 2,
                        mlp_ratio=mlp_ratio,
                        drop=drop_rate,
                        attn_drop=attn_drop_rate,
                        drop_path=dpr[sum(depths[:i_layer]) + i],
                        use_multiscale=use_multiscale,
                        patch_sizes=patch_sizes
                    )
                )
            self.layers.append(layer_blocks)
            
            # Downsample layer
            if i_layer < self.num_layers - 1:
                downsample = PatchMerging(
                    input_resolution=None,  # Will be set during forward
                    dim=int(embed_dim * 2 ** i_layer),
                    norm_layer=nn.LayerNorm
                )
                self.downsample_layers.append(downsample)
            else:
                self.downsample_layers.append(None)
    
    def forward(self, x, context_features, context_mask, center_frame_idx):
        """
        Args:
            center_patches_dict: Dict of {patch_size: (B, N, D)} center frame patches
            context_patches_dict: Dict of {patch_size: (B, T_c, N, D)} context frame patches (optional)
            temporal_mask: (B, T, 1, H, W) temporal missing mask for multi-scale attention
            center_frame_idx: (B,) center frame indices for each batch item
        Returns:
            features: List[(B, C, H, W)] multi-scale features
        """
        features = []
        
        _, _, C, H_patches, W_patches = context_features.shape
        # Calculate H, W from patch configuration
        # N represents the number of patches, so H_patches = W_patches = sqrt(N)
        # H_patches = W_patches = int(math.sqrt(N))
        
        for i_layer, layer_blocks in enumerate(self.layers):
            # Apply transformer blocks
            for block in layer_blocks:
                if block.use_multiscale and i_layer == 0:
                    # Use multi-scale attention with precomputed patches
                    x = block(x, context_features, context_mask, center_frame_idx)
                else:
                    # Standard self-attention
                    x = block(x, H_patches, W_patches)
            
            # Convert to conv format and save features
            x_conv = x.reshape(B, H_patches, W_patches, -1).permute(0, 3, 1, 2)  # (B, C, H, W)
            features.append(x_conv)
            
            # Downsample
            if i_layer < self.num_layers - 1:
                downsample = self.downsample_layers[i_layer]
                if downsample is not None:
                    # Set input resolution dynamically
                    downsample.input_resolution = (H_patches, W_patches)
                    x = downsample(x)
                    H_patches, W_patches = H_patches // 2, W_patches // 2

        return features


# ============================================================================
# Main Model: SwinWaterNetV18
# ============================================================================

class WaterNet(nn.Module):
    """
    Swin Water Net v18: Enhanced Architecture with Attention Mask and Multi-scale Patch Attention

    Key features:
    1. Attention mask mechanism using second channel of input_sequence
    2. Multi-scale patch attention (4, 8, 16, 32) for first stage only
    3. Center frame as Query, other frames as Key/Value
    4. Swin Transformer with [2, 2, 6, 2] blocks
    5. U-Net style decoder
    """
    
    def __init__(self, img_size=256, patch_sizes=[4, 8, 16, 32], in_chans=2, out_chans=2, 
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], window_size=8,
                 num_frames=48, mlp_ratio=4.0, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1):
        super().__init__()
        
        self.img_size = img_size
        self.patch_sizes = patch_sizes
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.num_frames = num_frames
        
        self.extractor = FeaturesExtractor(embed_dim)
        
        # Swin encoder with multi-scale and spatiotemporal processing
        self.encoder = Encoder(
            embed_dim=embed_dim, depths=depths, num_heads=num_heads,
            window_size=window_size, mlp_ratio=mlp_ratio,
            drop_rate=drop_rate, attn_drop_rate=attn_drop_rate, drop_path_rate=drop_path_rate,
            use_multiscale=True, patch_sizes=patch_sizes
        )
        
        # U-Net style decoder
        """
        将初始特征提取的特征也纳入解码器的输入中，实现Unet的skip connection
        """
        self.decoder = Decoder(
            img_size=img_size,
            patch_size=patch_sizes[1],  # Use 8x8 as base
            num_classes=out_chans,
            use_checkpoint=False,
            embed_dim=embed_dim,
            depths=depths
        )
        
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward(self, batch):
        """
        Args:
            batch: dict containing:
                - 'input_sequence': (B, T, C, H, W) video sequence with 2 channels
                  - Channel 0: water(1) vs land(0)
                  - Channel 1: missing mask (1 for missing, 0 for valid)
                - 'center_frame_idx': (B,) center frame indices
                - 'occurrence': (B, H, W) water frequency (optional)
        Returns:
            dict with 'inpaint' containing 'logits'
        """
        video = batch['input_sequence']  # (B, T, C, H, W) where C=2
        center_frame_idx = batch['center_frame_idx']  # (B,)

        B, T, C, H, W = video.shape
        device = video.device
        
        # Ensure we have 2 channels
        assert C == 2, f"Expected 2 channels (water+missing), got {C}"

        # Ensure center_frame_idx is within valid range
        center_frame_idx = torch.clamp(center_frame_idx, 0, T - 1)
        
        # Extract center frame and context frames
        batch_indices = torch.arange(B, device=device)
        center_frames = video[batch_indices, center_frame_idx].unsqueeze(1)  # (B, 1, C, H, W)

        # Create mask to exclude center frame
        all_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, T)
        center_indices_expanded = center_frame_idx.unsqueeze(1)
        context_indices_mask = all_indices != center_indices_expanded  # (B, T)
        # VECTORIZED: Extract context frames using advanced indexing
        context_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, T)  # (B, T)
        context_indices = context_indices[context_indices_mask]  # Flatten and get context indices
        # Reshape to (B, T-1) for proper indexing
        context_indices = context_indices.view(B, T-1)
        # Use advanced indexing to extract context frames efficiently
        batch_indices = torch.arange(B, device=device).unsqueeze(1).expand(B, T-1)  # (B, T-1)
        context_frames = video[batch_indices, context_indices]  # (B, T-1, C, H, W)

        # Create temporal mask from second channel for multi-scale attention
        # Extract missing mask (channel 1) from all frames: (B, T, 1, H, W)
        context_mask = context_frames[:, :, 1:2, :, :]  # All frames, channel 1

        # Apply multi-scale patch embedding
        center_frames = center_frames.squeeze(1)
        context_frames = context_frames.view(B*(T-1), C, H, W)
        context_mask = context_mask.view(B*(T-1), 1, H, W)

        center_features = self.extractor(center_frames)  # Dict of {patch_size: (B, C, h, w)}
        context_features = self.extractor(context_frames)  # Dict of {patch_size: (B, T-1, C, h, w)}
        context_features = context_features.view(B, T-1, *context_features.shape[1:])  # (B, T-1, c, h, w)
        context_mask = F.interpolate(context_mask, scale_factor=1.0/4, mode='nearest')  # (B*T, C, H, W)
        context_mask = context_mask.view(B, T-1, 1, H//4, W//4)  # (B, T-1, 1, H//4, W//4)

        # Encode with multi-scale attention
        encoder_features = self.encoder(center_features, context_features, context_mask, center_frame_idx)
        
        """
        加入特征提取的特征
        """
        
        # Decode
        logits = self.decoder(encoder_features[::-1])
        
        # Output format (removed patch_size_weights)
        output = {
            'inpaint': {'logits': logits}
        }
        
        return output


def create_swin_water_net_v18(config):
    """Create SwinWaterNetV18 model from config"""
    model_config = config.model.swin_config
    
    def safe_get(obj, key, default):
        if hasattr(obj, 'get'):
            return obj.get(key, default)
        elif hasattr(obj, key):
            return getattr(obj, key)
        else:
            return default
    
    model = WaterNet(
        img_size=safe_get(model_config, 'img_size', 256),
        patch_sizes=safe_get(model_config, 'patch_sizes', [4, 8, 16, 32]),
        in_chans=safe_get(model_config, 'in_chans', 2),
        out_chans=safe_get(model_config, 'out_chans', 2),
        embed_dim=safe_get(model_config, 'embed_dim', 96),
        depths=safe_get(model_config, 'depths', [2, 2, 6, 2]),
        num_heads=safe_get(model_config, 'num_heads', [3, 6, 12, 24]),
        window_size=safe_get(model_config, 'window_size', 8),
        num_frames=safe_get(config.data, 'num_frames', 48),
        mlp_ratio=safe_get(model_config, 'mlp_ratio', 4.0),
        drop_rate=safe_get(model_config, 'drop_rate', 0.),
        attn_drop_rate=safe_get(model_config, 'attn_drop_rate', 0.),
        drop_path_rate=safe_get(model_config, 'drop_path_rate', 0.1)
    )
    
    return model


# For compatibility
def create_swin_water_net(config):
    """Compatibility alias"""
    return create_swin_water_net_v18(config)


__all__ = [
    'create_swin_water_net',
]