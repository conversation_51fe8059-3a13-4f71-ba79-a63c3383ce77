"""
Swin Water Net v10: 中间帧专注架构 (重构版本)
基于标准Swin Transformer组件实现，复用已有函数

核心特点：
1. 复用标准Swin Transformer的WindowAttention和SwinBlock
2. 专注于中心帧的编码-解码，其他帧提供上下文
3. 使用标准的窗口注意力和移位窗口机制
4. 简化实现，删除冗余函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import math
import logging
from einops import rearrange, repeat
from typing import List, Tuple, Optional

from model.decoder_v8 import SwinUNetDecoder

logger = logging.getLogger(__name__)


# ============================================================================
# 复用标准Swin Transformer组件
# ============================================================================

class CyclicShift(nn.Module):
    """循环移位操作"""
    def __init__(self, displacement):
        super().__init__()
        self.displacement = displacement

    def forward(self, x):
        return torch.roll(x, shifts=(self.displacement, self.displacement), dims=(1, 2))


class Residual(nn.Module):
    """残差连接"""
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(x, **kwargs) + x


class PreNorm(nn.Module):
    """预归一化"""
    def __init__(self, dim, fn):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)


class FeedForward(nn.Module):
    """前馈网络"""
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim),
        )

    def forward(self, x):
        return self.net(x)


def create_shifted_window_mask(H, W, window_size, shift_size):
    """创建移位窗口的注意力掩码，支持自动padding，优化向量化操作"""
    if shift_size == 0:
        return None

    # 计算padding后的尺寸
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    # 创建图像mask
    img_mask = torch.zeros((1, H_padded, W_padded, 1))  # 1 H_padded W_padded 1

    # 向量化创建mask区域 - 避免嵌套for循环
    h_slices = [slice(0, -window_size),
                slice(-window_size, -shift_size),
                slice(-shift_size, None)]
    w_slices = [slice(0, -window_size),
                slice(-window_size, -shift_size),
                slice(-shift_size, None)]

    # 使用向量化操作创建mask
    cnt = 0
    for h in h_slices:
        for w in w_slices:
            img_mask[:, h, w, :] = cnt
            cnt += 1

    # 分割成窗口
    mask_windows = window_partition(img_mask, window_size)  # nW, window_size, window_size, 1
    mask_windows = mask_windows.view(-1, window_size * window_size)
    attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
    attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))

    return attn_mask


def window_partition(x, window_size):
    """将输入分割成窗口，支持自动padding"""
    B, H, W, C = x.shape

    # 确保H和W能被window_size整除，如果不能则进行padding
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size

    if pad_h > 0 or pad_w > 0:
        logger.warning(f"Padding input to {window_size}x{window_size} grid, added {pad_h}x{pad_w} padding")
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W

    x = x.view(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """将窗口合并回原始输入，支持自动去除padding"""
    # 计算padding
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.view(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_padded, W_padded, -1)

    # 去除padding
    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]

    return x


class PatchEmbed(nn.Module):
    """标准patch embedding"""

    def __init__(self, img_size=256, patch_size=4, in_chans=2, embed_dim=96):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2

        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, x):
        """
        Args: x: (B, T, C, H, W) video sequence
        Returns: x: (B, T, N, D) sequence of embedded patches
        """
        B, T, _, _, _ = x.shape

        # 向量化处理所有帧 - 一次性卷积操作
        x = x.flatten(0, 1)  # (B*T, C, H, W)
        x = self.proj(x)  # (B*T, D, H', W')

        D, H_p, W_p = x.shape[1], x.shape[2], x.shape[3]
        x = x.reshape(B, T, D, H_p, W_p)
        x = x.permute(0, 1, 3, 4, 2)  # (B, T, H', W', D)
        x = x.reshape(B, T, H_p*W_p, D)  # (B, T, N, D)

        # 向量化归一化 - 一次性处理所有帧的所有patch
        x_flat = x.view(B * T, H_p * W_p, D)  # (B*T, N, D)
        x_flat = self.norm(x_flat)
        x = x_flat.view(B, T, H_p * W_p, D)  # (B, T, N, D)

        return x


class WindowAttention(nn.Module):
    """窗口注意力机制"""

    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        # 相对位置偏置
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )

        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)

    def forward(self, x, mask=None):
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = F.softmax(attn, dim=-1)
        else:
            attn = F.softmax(attn, dim=-1)

        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class SwinTransformerBlock(nn.Module):
    """Swin Transformer块"""

    def __init__(self, dim, num_heads, window_size=8, shift_size=0, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio

        self.norm1 = norm_layer(dim)
        self.attn = WindowAttention(
            dim, window_size=window_size, num_heads=num_heads,
            qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)

        self.drop_path = nn.Identity() if drop_path <= 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )

    def forward(self, x, H, W):
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"

        shortcut = x
        x = self.norm1(x)
        x = x.reshape(B, H, W, C)

        # 创建移位窗口的注意力掩码
        attn_mask = None
        if self.shift_size > 0:
            attn_mask = create_shifted_window_mask(H, W, self.window_size, self.shift_size)
            if attn_mask is not None:
                attn_mask = attn_mask.to(x.device)

        # 循环移位
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        # 分割窗口
        x_windows = window_partition(shifted_x, self.window_size)
        x_windows = x_windows.reshape(-1, self.window_size * self.window_size, C)

        # W-MSA/SW-MSA (传递mask)
        attn_windows = self.attn(x_windows, mask=attn_mask)

        # 合并窗口
        attn_windows = attn_windows.reshape(-1, self.window_size, self.window_size, C)
        shifted_x = window_reverse(attn_windows, self.window_size, H, W)

        # 反向循环移位
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x

        x = x.reshape(B, H * W, C)

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class CrossTemporalWindowAttention(nn.Module):
    """跨时间窗口注意力 - 基于标准WindowAttention改造，优化向量化操作"""

    def __init__(self, dim, heads, head_dim, window_size, shifted=False):
        super().__init__()
        self.dim = dim
        self.heads = heads
        self.head_dim = head_dim
        self.window_size = window_size
        self.shifted = shifted
        self.scale = head_dim ** -0.5

        # 分离的QKV投影：中心帧作为Q，上下文帧作为KV
        self.q_proj = nn.Linear(dim, heads * head_dim, bias=False)
        self.kv_proj = nn.Linear(dim, heads * head_dim * 2, bias=False)
        self.proj = nn.Linear(heads * head_dim, dim)

        if shifted:
            displacement = window_size // 2
            self.cyclic_shift = CyclicShift(-displacement)
            self.cyclic_back_shift = CyclicShift(displacement)

    def forward(self, center_x, context_x):
        """
        Args:
            center_x: (B, H, W, D) 中心帧特征
            context_x: (B, T_c, H, W, D) 上下文帧特征
        """
        B, H, W, D = center_x.shape
        _, T_c, _, _, _ = context_x.shape

        # 创建注意力掩码（仅在移位窗口时需要）
        attn_mask = None
        if self.shifted:
            # 为移位窗口创建掩码
            attn_mask = create_shifted_window_mask(H, W, self.window_size, self.window_size // 2)
            if attn_mask is not None:
                attn_mask = attn_mask.to(center_x.device)

        # 应用移位 - 向量化操作
        if self.shifted:
            center_x = self.cyclic_shift(center_x)
            # 向量化处理所有上下文帧的移位
            context_x_flat = context_x.view(B * T_c, H, W, D)
            context_x_shifted = self.cyclic_shift(context_x_flat)
            context_x = context_x_shifted.view(B, T_c, H, W, D)

        # 窗口分割 - 向量化操作
        center_windows = window_partition(center_x, self.window_size)  # (B*nW, WS, WS, D)
        center_windows = center_windows.reshape(-1, self.window_size * self.window_size, D)

        # 向量化处理所有上下文帧的窗口分割
        context_x_flat = context_x.view(B * T_c, H, W, D)  # (B*T_c, H, W, D)
        context_windows_flat = window_partition(context_x_flat, self.window_size)  # (B*T_c*nW, WS, WS, D)
        context_windows_flat = context_windows_flat.reshape(-1, self.window_size * self.window_size, D)  # (B*T_c*nW, WS*WS, D)

        # 重新组织为 (B*nW, T_c*WS*WS, D)
        nW = center_windows.shape[0] // B  # 每个batch的窗口数
        context_windows = context_windows_flat.view(B, T_c, nW, self.window_size * self.window_size, D)
        context_windows = context_windows.permute(0, 2, 1, 3, 4).contiguous()  # (B, nW, T_c, WS*WS, D)
        context_windows = context_windows.view(B * nW, T_c * self.window_size * self.window_size, D)  # (B*nW, T_c*WS*WS, D)

        # QKV投影
        q = self.q_proj(center_windows)  # (B*nW, WS*WS, heads*head_dim)
        kv = self.kv_proj(context_windows)  # (B*nW, T_c*WS*WS, 2*heads*head_dim)

        # 重塑为多头格式
        BnW, WS2, _ = q.shape
        _, TcWS2, _ = kv.shape

        q = q.reshape(BnW, WS2, self.heads, self.head_dim).transpose(1, 2)  # (B*nW, heads, WS*WS, head_dim)
        kv = kv.reshape(BnW, TcWS2, 2, self.heads, self.head_dim).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]  # 每个都是 (B*nW, heads, T_c*WS*WS, head_dim)

        # 注意力计算
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))  # (B*nW, heads, WS*WS, T_c*WS*WS)

        # 应用掩码（如果存在）
        if attn_mask is not None:
            # 扩展掩码以适应跨时间注意力
            # attn_mask: (nW, WS*WS, WS*WS) -> (nW, WS*WS, T_c*WS*WS)
            nW_mask = attn_mask.shape[0]
            expanded_mask = attn_mask.unsqueeze(-1).expand(-1, -1, -1, T_c).reshape(nW_mask, WS2, TcWS2)
            attn = attn.view(BnW // nW_mask, nW_mask, self.heads, WS2, TcWS2) + expanded_mask.unsqueeze(0).unsqueeze(2)
            attn = attn.view(-1, self.heads, WS2, TcWS2)

        attn = attn.softmax(dim=-1)

        out = (attn @ v).transpose(1, 2).reshape(BnW, WS2, -1)  # (B*nW, WS*WS, heads*head_dim)
        out = self.proj(out)

        # 窗口合并
        out = out.reshape(-1, self.window_size, self.window_size, self.heads * self.head_dim)
        out = window_reverse(out, self.window_size, H, W)  # (B, H, W, D)

        # 反向移位
        if self.shifted:
            out = self.cyclic_back_shift(out)

        return out


class ContextAggregator(nn.Module):
    """基于标准Swin组件的上下文特征聚合器"""

    def __init__(self, embed_dim, num_heads=8, window_size=8, depths=[2], mlp_ratio=4.):
        super().__init__()
        self.embed_dim = embed_dim
        self.window_size = window_size
        head_dim = embed_dim // num_heads

        # 使用标准Swin块进行跨时间注意力
        self.layers = nn.ModuleList()
        for i in range(sum(depths)):
            shifted = (i % 2 == 1)  # 交替使用移位窗口
            self.layers.append(
                CrossTemporalWindowAttention(
                    dim=embed_dim,
                    heads=num_heads,
                    head_dim=head_dim,
                    window_size=window_size,
                    shifted=shifted
                )
            )

        # 归一化和MLP
        self.norm = nn.LayerNorm(embed_dim)
        self.mlp = FeedForward(embed_dim, int(embed_dim * mlp_ratio))

    def forward(self, center_features, context_features):
        """
        Args:
            center_features: (B, N, D) 中心帧特征
            context_features: (B, T-1, N, D) 上下文帧特征
        Returns:
            aggregated_features: (B, N, D) 聚合后的特征
        """
        B, N, D = center_features.shape
        _, T_c, _, _ = context_features.shape

        # 计算空间维度
        H = W = int(N ** 0.5)
        assert H * W == N, f"Feature map must be square, got {N} patches"

        # 重塑为空间格式
        center_x = center_features.reshape(B, H, W, D)  # (B, H, W, D)
        context_x = context_features.reshape(B, T_c, H, W, D)  # (B, T_c, H, W, D)

        # 向量化归一化 - 一次性处理所有上下文帧
        center_x = self.norm(center_x)
        # 重塑context_x以便向量化归一化
        context_x_flat = context_x.view(B * T_c, H, W, D)
        context_x_flat = self.norm(context_x_flat)
        context_x = context_x_flat.view(B, T_c, H, W, D)

        # 多层跨时间注意力
        enhanced_center = center_x
        for layer in self.layers:
            enhanced_center = enhanced_center + layer(enhanced_center, context_x)

        # MLP处理
        enhanced_center = enhanced_center + self.mlp(enhanced_center)

        # 重塑回序列格式并添加残差连接
        enhanced_center = enhanced_center.reshape(B, N, D)
        aggregated_features = enhanced_center + center_features

        return aggregated_features


# ============================================================================
# 删除旧的重复实现，使用上面基于标准Swin组件的版本
# ============================================================================





class PatchMerging(nn.Module):
    """标准的Patch Merging层"""

    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)

    def forward(self, x):
        """
        Args:
            x: (B, H*W, C) 输入特征
        Returns:
            x: (B, (H/2)*(W/2), 2*C) 下采样后的特征
        """
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        assert H % 2 == 0 and W % 2 == 0, f"x size ({H}*{W}) are not even."

        x = x.view(B, H, W, C)

        # 提取4个子patch
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C (左上)
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C (右上)
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C (左下)
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C (右下)

        # 拼接4个子patch
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C

        x = self.norm(x)
        x = self.reduction(x)  # B H/2*W/2 2*C

        return x


class SwinEncoder(nn.Module):
    """改进的Swin编码器，减少信息损失"""

    def __init__(self, embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24],
                 window_size=8, mlp_ratio=4., drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 img_size=256, patch_size=4):
        super().__init__()
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_layers = len(depths)
        self.img_size = img_size
        self.patch_size = patch_size

        # 计算各层的分辨率
        patches_resolution = img_size // patch_size
        self.patches_resolution = patches_resolution

        # 构建各层
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        self.layers = nn.ModuleList()
        self.downsample_layers = nn.ModuleList()

        for i_layer in range(self.num_layers):
            # 当前层的分辨率
            layer_resolution = patches_resolution // (2 ** i_layer)

            layer_blocks = nn.ModuleList()
            for i in range(depths[i_layer]):
                layer_blocks.append(
                    SwinTransformerBlock(
                        dim=int(embed_dim * 2 ** i_layer),
                        num_heads=num_heads[i_layer],
                        window_size=window_size,
                        shift_size=0 if (i % 2 == 0) else window_size // 2,
                        mlp_ratio=mlp_ratio,
                        drop=drop_rate,
                        attn_drop=attn_drop_rate,
                        drop_path=dpr[sum(depths[:i_layer]) + i]
                    )
                )
            self.layers.append(layer_blocks)

            # 使用标准PatchMerging进行下采样
            if i_layer < self.num_layers - 1:
                downsample = PatchMerging(
                    input_resolution=(layer_resolution, layer_resolution),
                    dim=int(embed_dim * 2 ** i_layer),
                    norm_layer=nn.LayerNorm
                )
                self.downsample_layers.append(downsample)
            else:
                self.downsample_layers.append(None)

    def forward(self, x):
        """
        Args: x: (B, N, D) 输入特征
        Returns: features: List[(B, C, H, W)] 多尺度特征
        """
        features = []
        B, N, _ = x.shape
        H = W = int(math.sqrt(N))

        for i_layer, layer_blocks in enumerate(self.layers):
            # Transformer blocks
            for block in layer_blocks:
                x = block(x, H, W)

            # 转换为卷积格式并保存特征
            x_conv = x.reshape(B, H, W, -1).permute(0, 3, 1, 2)  # (B, C, H, W)
            features.append(x_conv)

            # 下采样（如果不是最后一层）
            if i_layer < self.num_layers - 1:
                downsample = self.downsample_layers[i_layer]
                if downsample is not None:
                    # 使用标准PatchMerging进行下采样
                    x = downsample(x)  # (B, H*W, C) -> (B, (H/2)*(W/2), 2*C)
                    H, W = H // 2, W // 2

        return features


class SwinWaterNetV10(nn.Module):
    """
    Swin Water Net v10: 中间帧专注架构

    核心特点：
    1. 只对中间帧进行完整的编码-解码
    2. 其他帧提供轻量级上下文信息
    3. 使用Swin Transformer的窗口注意力减少计算
    4. 渐进式特征融合优化内存使用
    """

    def __init__(self, img_size=256, patch_size=4, in_chans=2, out_chans=2, embed_dim=96,
                 depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], window_size=8,
                 num_frames=48, mlp_ratio=4.0, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1):
        super().__init__()

        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.num_frames = num_frames

        # Patch embedding
        self.patch_embed = PatchEmbed(
            img_size=img_size, patch_size=patch_size,
            in_chans=in_chans, embed_dim=embed_dim
        )

        # 上下文聚合器
        self.context_aggregator = ContextAggregator(
            embed_dim=embed_dim
        )

        # Swin编码器（只处理中间帧）
        self.encoder = SwinEncoder(
            embed_dim=embed_dim, depths=depths, num_heads=num_heads,
            window_size=window_size, mlp_ratio=mlp_ratio,
            drop_rate=drop_rate, attn_drop_rate=attn_drop_rate, drop_path_rate=drop_path_rate,
            img_size=img_size, patch_size=patch_size
        )

        # 解码器
        final_dim = embed_dim * (2 ** (len(depths) - 1))
        self.decoder = SwinUNetDecoder(
            in_channels=final_dim, img_size=img_size, patch_size=patch_size,
            num_classes=out_chans, use_checkpoint=False
        )

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def forward(self, batch):
        """
        Args:
            batch: dict containing:
                - 'input_sequence': (B, T, C, H, W) video sequence
                - 'center_frame_idx': (B,) center frame indices
                - other metadata
        Returns:
            dict with 'inpaint' containing 'logits'
        """
        video = batch['input_sequence']  # (B, T, C, H, W)
        center_frame_idx = batch['center_frame_idx']  # (B,)
        B, T, C, H, W = video.shape

        # 向量化操作：选择中心帧和上下文帧
        # 1. 确保center_frame_idx在有效范围内
        center_frame_idx = torch.clamp(center_frame_idx, 0, T - 1)

        # 2. 完全向量化选择中心帧
        batch_indices = torch.arange(B, device=video.device)
        center_frames = video[batch_indices, center_frame_idx].unsqueeze(1)  # (B, 1, C, H, W)

        # 3. 完全向量化选择上下文帧
        # 创建所有帧的索引矩阵
        all_indices = torch.arange(T, device=video.device).unsqueeze(0).expand(B, T)  # (B, T)
        center_indices_expanded = center_frame_idx.unsqueeze(1)  # (B, 1)

        # 创建mask，排除中心帧
        context_mask = all_indices != center_indices_expanded  # (B, T)

        # 重新排列video以便于mask操作: (B, T, C, H, W) -> (B*T, C, H, W)
        video_flat = video.view(B * T, C, H, W)

        # 创建batch索引用于选择
        batch_indices_expanded = torch.arange(B, device=video.device).unsqueeze(1).expand(B, T)  # (B, T)
        flat_indices = batch_indices_expanded * T + all_indices  # (B, T) 展平后的索引

        # 使用mask选择上下文帧的索引
        context_flat_indices = flat_indices[context_mask]  # (B*(T-1),)

        # 选择上下文帧并重新整形
        context_frames_flat = video_flat[context_flat_indices]  # (B*(T-1), C, H, W)
        context_frames = context_frames_flat.view(B, T-1, C, H, W)  # (B, T-1, C, H, W)

        # Patch embedding
        center_patches = self.patch_embed(center_frames)  # (B, 1, N, D)
        center_patches = center_patches.squeeze(1)  # (B, N, D)

        context_patches = self.patch_embed(context_frames)  # (B, T-1, N, D)

        # 上下文特征聚合
        aggregated_features = self.context_aggregator(center_patches, context_patches)

        # 编码（只处理聚合后的中间帧特征）
        encoder_features = self.encoder(aggregated_features)

        # 解码
        logits = self.decoder(encoder_features[::-1])

        return {'inpaint': {'logits': logits}}


def create_swin_water_net_v10(config):
    """创建SwinWaterNetV10模型"""
    model_config = config.model.swin_config

    # 安全获取配置值的辅助函数
    def safe_get(obj, key, default):
        if hasattr(obj, 'get'):
            return obj.get(key, default)
        elif hasattr(obj, key):
            return getattr(obj, key)
        else:
            return default

    model = SwinWaterNetV10(
        img_size=safe_get(model_config, 'img_size', 256),
        patch_size=safe_get(model_config, 'patch_size', 4),
        in_chans=safe_get(model_config, 'in_chans', 2),
        out_chans=safe_get(model_config, 'out_chans', 2),
        embed_dim=safe_get(model_config, 'embed_dim', 96),
        depths=safe_get(model_config, 'depths', [2, 2, 6, 2]),
        num_heads=safe_get(model_config, 'num_heads', [3, 6, 12, 24]),
        window_size=safe_get(model_config, 'window_size', 8),
        num_frames=safe_get(config.data, 'num_frames', 48),
        mlp_ratio=safe_get(model_config, 'mlp_ratio', 4.0),
        drop_rate=safe_get(model_config, 'drop_rate', 0.),
        attn_drop_rate=safe_get(model_config, 'attn_drop_rate', 0.),
        drop_path_rate=safe_get(model_config, 'drop_path_rate', 0.1)
    )

    return model


# 为了兼容性，提供别名
def create_swin_water_net(config):
    """兼容性别名"""
    return create_swin_water_net_v10(config)


__all__ = [
    'SwinWaterNetV10',
    'create_swin_water_net_v10',
    'create_swin_water_net',
    'PatchEmbed',
    'WindowAttention',
    'SwinTransformerBlock',
    'ContextAggregator',
    'PatchMerging',
    'SwinEncoder'
]