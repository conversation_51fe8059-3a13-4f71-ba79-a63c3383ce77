"""
Optimized STTN-based Video Water Body Inpainting Model (model_v12.py)

Simplified implementation focusing on core STTN features with adaptive patch sizing and 30M parameters
Reference: "Learning Joint Spatial-Temporal Transformations for Video Inpainting" (ECCV 2020)

Key Features:
1. Adaptive patch sizing based on water frequency (far from 0.5 → large patches, near 0.5 → small patches)
2. Hierarchical 4-stage encoder with 30M parameters
3. Efficient spatial-temporal attention
4. Enhanced geographic-temporal context
5. Multi-scale progressive decoding

Architecture Overview:
Input: (B, T, 2, H, W) - temporal water body sequence + water frequency map
    ↓
Adaptive Multi-scale Patch Embedding - frequency-guided patch size selection
    ↓
Hierarchical STTN Encoder - 4 stages with increasing embedding dimensions
    ↓
Enhanced Geographic-Temporal Context - contextual encoding
    ↓
Progressive Decoder - multi-resolution reconstruction
    ↓
Output: (B, 2, H, W) - inpainted water body prediction
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging
from einops import rearrange
from typing import List, Tuple, Optional, Dict, Any
import numpy as np

logger = logging.getLogger(__name__)

# Import existing modules
from model.mixed_precision_utils import ensure_dtype_compatibility
from model.loss import compute_dynamic_degree


class AdaptivePatchSizeSelector(nn.Module):
    """
    Dynamic degree-based adaptive patch size selector using compute_dynamic_degree
    
    Key insight: Use dynamic degree (computed from water frequency) to determine patch sizing.
    Higher dynamic degree (uncertain regions) → smaller patches for precision
    Lower dynamic degree (stable regions) → larger patches for efficiency
    """
    
    def __init__(self, patch_sizes=[4, 8, 16, 32], embed_dim=128, img_size=256):
        super().__init__()
        self.patch_sizes = patch_sizes
        self.embed_dim = embed_dim
        self.img_size = img_size
        self.num_scales = len(patch_sizes)
        
        # Patch embeddings for each scale
        self.patch_embeds = nn.ModuleList()
        for patch_size in patch_sizes:
            embed = nn.Sequential(
                nn.Conv2d(2, embed_dim // 2, patch_size, patch_size),
                nn.GroupNorm(8, embed_dim // 2),
                nn.ReLU(inplace=True),
                nn.Conv2d(embed_dim // 2, embed_dim, 1, 1),
            )
            self.patch_embeds.append(embed)
        
        # Simplified fusion network
        self.fusion_net = nn.Sequential(
            nn.Conv2d(embed_dim, embed_dim, 3, 1, 1),
            nn.GroupNorm(8, embed_dim),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x, water_frequency=None):
        """
        Dynamic degree-based adaptive patch embedding
        
        Args:
            x: (B, T, 2, H, W) input sequence
            water_frequency: (B, H, W) water frequency map (0-1), optional
            
        Returns:
            patches: (B, T, N, D) adaptively embedded patches
            patch_info: dict with patch size selection info
        """
        B, T, C, H, W = x.shape
        
        # If no water frequency provided, use center frame missing mask as proxy
        if water_frequency is None:
            center_idx = T // 2
            missing_mask = x[:, center_idx, 1, :, :]  # (B, H, W)
            water_frequency = 1.0 - missing_mask
        
        # Compute dynamic degree using the standard function
        dynamic_degree = compute_dynamic_degree(water_frequency)  # (B, H, W)
        
        # Select optimal patch size based on dynamic degree
        # Higher dynamic degree → smaller patches (index 0: size 4)
        # Lower dynamic degree → larger patches (index 3: size 32)
        patch_weights = self._compute_patch_weights(dynamic_degree)  # (B, H, W)

        # Compute differentiable scale weights
        scale_weights = self._compute_scale_weights(patch_weights)  # (4,) - weights for each scale

        # Apply weighted combination of all patch embeddings (differentiable)
        x_reshaped = rearrange(x, 'b t c h w -> (b t) c h w')

        # Compute patches for all scales
        all_patches = []
        target_size = None

        for i, patch_embed in enumerate(self.patch_embeds):
            scale_patches = patch_embed(x_reshaped)  # (BT, D, H_i, W_i)

            # Determine target size from the first scale (smallest patches = highest resolution)
            if target_size is None:
                target_size = scale_patches.shape[-2:]

            # Resize to target size if needed
            if scale_patches.shape[-2:] != target_size:
                scale_patches = F.interpolate(
                    scale_patches, size=target_size,
                    mode='bilinear', align_corners=False
                )

            all_patches.append(scale_patches)

        # Weighted combination of all scales
        patches = sum(w * p for w, p in zip(scale_weights, all_patches))

        # Reshape back to temporal format
        _, D, H_p, W_p = patches.shape
        patches = rearrange(patches, '(b t) d h w -> b t d h w', b=B, t=T)

        # Apply adaptive spatial weighting
        weight_map = F.interpolate(
            patch_weights.unsqueeze(1), size=(H_p, W_p),
            mode='bilinear', align_corners=False
        )

        # Reshape weight_map to broadcast with patches (B, T, D, H_p, W_p)
        # weight_map is currently (B, 1, H_p, W_p), we need (B, 1, 1, H_p, W_p)
        weight_map = weight_map.unsqueeze(2)  # (B, 1, 1, H_p, W_p)

        # Apply weighting - this should preserve the 5D structure
        patches = patches * (0.5 + 0.5 * weight_map)  # Soft weighting
        
        # Apply fusion
        patches_spatial = rearrange(patches, 'b t d h w -> (b t) d h w')
        patches_fused = self.fusion_net(patches_spatial)
        patches_final = rearrange(patches_fused, '(b t) d h w -> b t (h w) d', b=B, t=T)
        
        # Safe index computation
        dominant_idx = scale_weights.argmax().item()
        dominant_idx = min(dominant_idx, len(self.patch_sizes) - 1)  # Ensure valid index

        patch_info = {
            'scale_weights': scale_weights.detach().cpu().numpy(),
            'dominant_scale_idx': dominant_idx,
            'dominant_patch_size': self.patch_sizes[dominant_idx],
            'resolution': (H_p, W_p),
            'num_patches': H_p * W_p,
            'dynamic_degree_mean': dynamic_degree.mean().item()
        }
        
        return patches_final, patch_info
    
    def _compute_patch_weights(self, dynamic_degree):
        """Convert dynamic degree to patch size preference"""
        # Invert dynamic degree: high degree → small patches
        # Normalize to [0, 1] where 1 means prefer smallest patches
        degree_normalized = (dynamic_degree - 0.01) / (6.0 - 0.01)  # Based on compute_dynamic_degree range
        degree_normalized = torch.clamp(degree_normalized, 0, 1)
        return degree_normalized
    
    def _compute_scale_weights(self, patch_weights):
        """Compute differentiable scale weights using soft selection"""
        avg_weight = patch_weights.mean()  # Keep as tensor for differentiability

        # Soft mapping to scale weights using sigmoid functions
        # High weight → prefer smaller patches (scale 0)
        # Low weight → prefer larger patches (scale 3)

        # Define soft boundaries using sigmoid
        temp = 10.0  # Temperature for sharpness

        # Scale 0 (4x4): high weight preference
        w0 = torch.sigmoid(temp * (avg_weight - 0.7))

        # Scale 1 (8x8): medium-high weight preference
        w1 = torch.sigmoid(temp * (avg_weight - 0.5)) * torch.sigmoid(temp * (0.7 - avg_weight))

        # Scale 2 (16x16): medium-low weight preference
        w2 = torch.sigmoid(temp * (avg_weight - 0.3)) * torch.sigmoid(temp * (0.5 - avg_weight))

        # Scale 3 (32x32): low weight preference
        w3 = torch.sigmoid(temp * (0.3 - avg_weight))

        # Normalize weights to sum to 1
        scale_weights = torch.stack([w0, w1, w2, w3])
        scale_weights = scale_weights / (scale_weights.sum() + 1e-8)

        return scale_weights


class SpatialTemporalAttention(nn.Module):
    """
    Memory-optimized spatial-temporal attention

    Key optimizations:
    1. Chunked attention computation to avoid memory explosion
    2. Gradient checkpointing for memory efficiency
    3. Adaptive attention patterns based on sequence length
    4. Local attention with global context injection
    """

    def __init__(self, embed_dim=128, num_heads=8, qkv_bias=True, attn_drop=0.1, proj_drop=0.1,
                 max_seq_len=2048, chunk_size=512, use_sparse_attention=True):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        self.max_seq_len = max_seq_len
        self.chunk_size = chunk_size  # Chunk size for memory-efficient attention
        self.use_sparse_attention = use_sparse_attention

        assert embed_dim % num_heads == 0, f"embed_dim {embed_dim} must be divisible by num_heads {num_heads}"

        # QKV projections
        self.qkv = nn.Linear(embed_dim, embed_dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.proj_drop = nn.Dropout(proj_drop)

        # Lightweight temporal positional encoding
        self.temporal_pos_embed = nn.Parameter(torch.zeros(1, 100, embed_dim))
        nn.init.trunc_normal_(self.temporal_pos_embed, std=0.02)
    
    def forward(self, x, mask=None):
        """
        Memory-optimized attention with chunked computation

        Args:
            x: (B, T, N, D) spatiotemporal features
            mask: (B, T, N) attention mask

        Returns:
            out: (B, T, N, D) attended features
        """
        B, T, N, D = x.shape

        # Add temporal positional encoding
        if T <= self.temporal_pos_embed.shape[1]:
            temp_pos = self.temporal_pos_embed[:, :T, :].unsqueeze(2)
            x = x + temp_pos

        # Flatten for attention computation
        x_flat = x.view(B, T * N, D)
        TN = T * N

        # Always use chunked attention for memory efficiency
        return self._chunked_attention(x_flat, B, T, N, mask)

    def _chunked_attention(self, x_flat, B, T, N, mask):
        """
        Memory-efficient chunked attention computation

        Processes attention in chunks to avoid memory explosion
        """
        TN = T * N

        # Compute QKV for all tokens
        qkv = self.qkv(x_flat).reshape(B, TN, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, B, num_heads, TN, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]

        # Initialize output
        output = torch.zeros_like(v)

        # Process in chunks to avoid memory explosion
        for i in range(0, TN, self.chunk_size):
            end_i = min(i + self.chunk_size, TN)
            q_chunk = q[:, :, i:end_i, :]  # (B, num_heads, chunk_size, head_dim)

            # Compute attention for this chunk against all keys
            attn_chunk = (q_chunk @ k.transpose(-2, -1)) * self.scale  # (B, num_heads, chunk_size, TN)

            # Apply mask if provided
            if mask is not None:
                mask_flat = mask.view(B, TN)
                mask_chunk = mask_flat.unsqueeze(1).unsqueeze(1)  # (B, 1, 1, TN)
                attn_chunk = attn_chunk.masked_fill(mask_chunk, float('-inf'))

            # Softmax and dropout
            attn_chunk = F.softmax(attn_chunk, dim=-1)
            attn_chunk = self.attn_drop(attn_chunk)

            # Apply attention to values
            output[:, :, i:end_i, :] = attn_chunk @ v  # (B, num_heads, chunk_size, head_dim)

        # Reshape and apply final projection
        output = output.transpose(1, 2).reshape(B, TN, self.embed_dim)
        output = self.proj(output)
        output = self.proj_drop(output)

        return output.view(B, T, N, self.embed_dim)
    
    def _memory_efficient_attention(self, x_flat, B, T, N, mask):
        """Memory-efficient attention using gradient checkpointing"""
        def attention_function(x_chunk):
            qkv = self.qkv(x_chunk).reshape(B, -1, 3, self.num_heads, self.head_dim)
            qkv = qkv.permute(2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[2]
            
            # Full attention computation (preserves global context)
            attn = (q @ k.transpose(-2, -1)) * self.scale
            attn = F.softmax(attn, dim=-1)
            attn = self.attn_drop(attn)
            
            out = (attn @ v).transpose(1, 2).reshape(B, -1, self.embed_dim)
            return self.proj(out)
        
        # Use gradient checkpointing to save memory
        if self.training:
            out = torch.utils.checkpoint.checkpoint(attention_function, x_flat, use_reentrant=False)
        else:
            out = attention_function(x_flat)
        
        out = self.proj_drop(out)
        return out.view(B, T, N, self.embed_dim)
    
    def _sparse_attention(self, x_flat, B, T, N, mask):
        """
        Sparse attention pattern preserving global context for STTN
        
        Combines local spatial attention with global temporal attention
        to preserve STTN's cross-temporal patch matching capability
        """
        TN = T * N
        
        # Compute QKV for all tokens
        qkv = self.qkv(x_flat).reshape(B, TN, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, B, num_heads, TN, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # Reshape to separate temporal and spatial dimensions
        q = q.view(B, self.num_heads, T, N, self.head_dim)
        k = k.view(B, self.num_heads, T, N, self.head_dim)
        v = v.view(B, self.num_heads, T, N, self.head_dim)
        
        output = torch.zeros_like(v)
        
        # 1. Local spatial attention within each temporal frame
        for t in range(T):
            q_t = q[:, :, t]  # (B, num_heads, N, head_dim)
            k_t = k[:, :, t]  # (B, num_heads, N, head_dim)
            v_t = v[:, :, t]  # (B, num_heads, N, head_dim)
            
            # Local spatial attention
            local_attn = (q_t @ k_t.transpose(-2, -1)) * self.scale
            local_attn = F.softmax(local_attn, dim=-1)
            local_attn = self.attn_drop(local_attn)
            
            output[:, :, t] = local_attn @ v_t
        
        # 2. Global temporal attention for missing regions (STTN's core idea)
        if mask is not None:
            mask_reshaped = mask.view(B, T, N)  # (B, T, N)
            
            # Find missing regions for global temporal attention
            missing_positions = []
            for b in range(B):
                for t in range(T):
                    missing_idx = mask_reshaped[b, t].nonzero(as_tuple=True)[0]
                    if len(missing_idx) > 0:
                        # Sample representative missing positions
                        n_repr = min(64, len(missing_idx))  # Limit for efficiency
                        sampled_idx = missing_idx[torch.randperm(len(missing_idx), device=missing_idx.device)[:n_repr]]
                        missing_positions.append((b, t, sampled_idx))
            
            # Global temporal attention for missing regions
            if missing_positions:
                for b, t_missing, missing_idx in missing_positions:
                    # Query from missing positions at time t_missing
                    q_missing = q[b, :, t_missing, missing_idx]  # (num_heads, n_missing, head_dim)
                    
                    # Keys and values from all other temporal frames
                    k_global = k[b, :, :, :].view(self.num_heads, T * N, self.head_dim)  # (num_heads, T*N, head_dim)
                    v_global = v[b, :, :, :].view(self.num_heads, T * N, self.head_dim)
                    
                    # Global attention computation
                    global_attn = (q_missing @ k_global.transpose(-2, -1)) * self.scale
                    global_attn = F.softmax(global_attn, dim=-1)
                    global_attn = self.attn_drop(global_attn)
                    
                    global_out = global_attn @ v_global  # (num_heads, n_missing, head_dim)
                    
                    # Combine with local output (weighted fusion)
                    alpha = 0.6  # Weight for global information
                    output[b, :, t_missing, missing_idx] = (
                        (1 - alpha) * output[b, :, t_missing, missing_idx] + 
                        alpha * global_out
                    )
        
        # Reshape back and apply final projection
        output = output.view(B, self.num_heads, TN, self.head_dim)
        output = output.transpose(1, 2).reshape(B, TN, self.embed_dim)
        output = self.proj(output)
        output = self.proj_drop(output)
        
        return output.view(B, T, N, self.embed_dim)


class STTNTransformerBlock(nn.Module):
    """
    STTN Transformer block with spatial-temporal attention and feed-forward network
    """
    
    def __init__(self, embed_dim=128, num_heads=8, mlp_ratio=4.0, qkv_bias=True,
                 drop=0.1, attn_drop=0.1, drop_path=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        
        # Spatial-temporal attention
        self.attn = SpatialTemporalAttention(
            embed_dim=embed_dim, num_heads=num_heads, qkv_bias=qkv_bias,
            attn_drop=attn_drop, proj_drop=drop
        )
        
        # Drop path for stochastic depth
        self.drop_path = nn.Dropout(drop_path) if drop_path > 0. else nn.Identity()
        
        # Feed-forward network
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(drop)
        )
    
    def forward(self, x, mask=None):
        """Forward pass"""
        # Attention block with residual connection
        x_norm = self.norm1(x)
        attn_out = self.attn(x_norm, mask)
        x = x + self.drop_path(attn_out)
        
        # MLP block with residual connection
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        
        return x


class HierarchicalSTTNEncoder(nn.Module):
    """
    Hierarchical STTN encoder with 4 stages for 30M parameter model
    
    Architecture progression:
    Stage 1: 128 dim,  2 blocks - Local spatial-temporal patterns
    Stage 2: 256 dim,  4 blocks - Mid-level feature integration  
    Stage 3: 512 dim,  6 blocks - High-level semantic understanding
    Stage 4: 768 dim,  4 blocks - Global context and refinement
    """
    
    def __init__(self, base_embed_dim=128, stage_depths=[2, 4, 6, 4], 
                 num_heads=[4, 8, 16, 24], mlp_ratio=4.0, qkv_bias=True,
                 drop_rate=0.1, attn_drop_rate=0.1, drop_path_rate=0.2):
        super().__init__()
        
        self.num_stages = len(stage_depths)
        self.stage_depths = stage_depths
        
        # Calculate embedding dimensions for each stage
        self.stage_dims = [base_embed_dim * (2 ** i) for i in range(self.num_stages)]
        
        # Build stages
        self.stages = nn.ModuleList()
        
        for stage_idx in range(self.num_stages):
            stage_dim = self.stage_dims[stage_idx]
            depth = stage_depths[stage_idx]
            heads = num_heads[stage_idx]
            
            # Calculate drop path rates for this stage
            stage_dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]
            
            # Build blocks for this stage
            blocks = nn.ModuleList([
                STTNTransformerBlock(
                    embed_dim=stage_dim, num_heads=heads, mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias, drop=drop_rate, attn_drop=attn_drop_rate,
                    drop_path=stage_dpr[i]
                ) for i in range(depth)
            ])
            
            # Add dimension transition if not the first stage
            if stage_idx > 0:
                prev_dim = self.stage_dims[stage_idx - 1]
                transition = nn.Sequential(
                    nn.Linear(prev_dim, stage_dim),
                    nn.LayerNorm(stage_dim),
                    nn.GELU()
                )
            else:
                transition = nn.Identity()
            
            self.stages.append(nn.ModuleDict({
                'transition': transition,
                'blocks': blocks,
                'norm': nn.LayerNorm(stage_dim)
            }))
        
        # Global feature aggregation
        total_dim = sum(self.stage_dims)
        self.global_fusion = nn.Sequential(
            nn.Linear(total_dim, base_embed_dim * 4),
            nn.LayerNorm(base_embed_dim * 4),
            nn.GELU(),
            nn.Linear(base_embed_dim * 4, base_embed_dim * 2)
        )
    
    def forward(self, x, mask=None):
        """
        Hierarchical encoding with multi-stage processing
        
        Args:
            x: (B, T, N, D) input features
            mask: (B, T, N) attention mask
            
        Returns:
            stage_features: List of features from each stage
            global_features: Globally fused features
        """
        stage_features = []
        current_x = x
        
        for stage_idx, stage in enumerate(self.stages):
            # Apply dimension transition
            current_x = stage['transition'](current_x)
            
            # Process through stage blocks
            for block in stage['blocks']:
                current_x = block(current_x, mask)
            
            # Apply stage normalization
            current_x = stage['norm'](current_x)
            stage_features.append(current_x)
        
        # Global fusion of all stage features
        # Align all features to the same number of patches (use smallest)
        min_patches = min(feat.shape[2] for feat in stage_features)
        aligned_features = []
        
        for feat in stage_features:
            B, T, N, D = feat.shape
            if N > min_patches:
                # Spatial pooling to reduce patch count
                spatial_size = int(math.sqrt(N))
                target_size = int(math.sqrt(min_patches))
                feat_spatial = rearrange(feat, 'b t (h w) d -> (b t) d h w', h=spatial_size)
                feat_pooled = F.adaptive_avg_pool2d(feat_spatial, (target_size, target_size))
                feat_aligned = rearrange(feat_pooled, '(b t) d h w -> b t (h w) d', b=B, t=T)
            else:
                feat_aligned = feat
            aligned_features.append(feat_aligned)
        
        # Concatenate and fuse
        global_concat = torch.cat(aligned_features, dim=-1)  # (B, T, N_min, total_dim)
        global_features = self.global_fusion(global_concat)
        
        return stage_features, global_features


class EnhancedGeographicTemporalEmbedding(nn.Module):
    """
    Enhanced geographic and temporal embedding for water body context
    """
    
    def __init__(self, embed_dim=128, num_fourier_bands=16):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_fourier_bands = num_fourier_bands
        
        # Fourier encoding for geographic coordinates
        self.register_buffer(
            'geo_freq_bands',
            2.0 ** torch.linspace(0, num_fourier_bands-1, num_fourier_bands)
        )
        
        # Geographic encoding network
        geo_fourier_dim = 4 * num_fourier_bands  # sin/cos for lon/lat
        self.geo_encoder = nn.Sequential(
            nn.Linear(geo_fourier_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim, embed_dim // 2)
        )
        
        # Temporal encoding
        self.year_embed = nn.Embedding(300, embed_dim // 4)  # 1800-2100
        self.month_embed = nn.Embedding(12, embed_dim // 4)
        self.season_embed = nn.Embedding(4, embed_dim // 4)  # Spring, Summer, Fall, Winter
        
        # Final fusion
        total_dim = embed_dim // 2 + embed_dim // 4 * 3
        self.fusion = nn.Sequential(
            nn.Linear(total_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, lon, lat, year, month):
        """Enhanced geographic-temporal encoding"""
        B = lon.shape[0]
        
        # Geographic Fourier encoding
        lon_scaled = lon * math.pi / 180.0
        lat_scaled = lat * math.pi / 90.0
        
        geo_freqs = self.geo_freq_bands.unsqueeze(0)  # (1, num_bands)
        lon_freqs = lon_scaled.unsqueeze(1) * geo_freqs  # (B, num_bands)
        lat_freqs = lat_scaled.unsqueeze(1) * geo_freqs
        
        geo_fourier = torch.cat([
            torch.sin(lon_freqs), torch.cos(lon_freqs),
            torch.sin(lat_freqs), torch.cos(lat_freqs)
        ], dim=1)  # (B, 4*num_bands)
        
        geo_feat = self.geo_encoder(geo_fourier)
        
        # Temporal embeddings
        year_idx = torch.clamp(year - 1800, 0, 299).long()
        month_idx = torch.clamp(month - 1, 0, 11).long()
        season_idx = ((month_idx - 1) // 3).long()  # 0=Spring, 1=Summer, 2=Fall, 3=Winter
        
        year_feat = self.year_embed(year_idx)
        month_feat = self.month_embed(month_idx)
        season_feat = self.season_embed(season_idx)
        
        # Combine all features
        combined = torch.cat([geo_feat, year_feat, month_feat, season_feat], dim=-1)
        
        return self.fusion(combined)


class ProgressiveSTTNDecoder(nn.Module):
    """
    Progressive decoder with multi-resolution reconstruction
    """
    
    def __init__(self, stage_dims=[128, 256, 512, 768], patch_sizes=[4, 8, 16, 32],
                 img_size=256, out_chans=2, base_dim=128):
        super().__init__()
        self.stage_dims = stage_dims
        self.patch_sizes = patch_sizes
        self.img_size = img_size
        self.out_chans = out_chans
        self.num_stages = len(stage_dims)
        
        # Progressive upsampling blocks
        self.upsample_blocks = nn.ModuleList()
        
        for i in range(self.num_stages - 1, -1, -1):
            current_dim = stage_dims[i]
            
            if i == self.num_stages - 1:
                # First decoder block (highest level)
                block = nn.Sequential(
                    nn.Linear(current_dim, current_dim // 2),
                    nn.LayerNorm(current_dim // 2),
                    nn.GELU(),
                    nn.Linear(current_dim // 2, base_dim)
                )
            else:
                # Subsequent blocks with skip connections
                prev_dim = base_dim
                block = nn.Sequential(
                    nn.Linear(current_dim + prev_dim, current_dim // 2),
                    nn.LayerNorm(current_dim // 2),
                    nn.GELU(),
                    nn.Linear(current_dim // 2, base_dim)
                )
            
            self.upsample_blocks.append(block)
        
        # Multi-scale output heads
        self.output_heads = nn.ModuleList()
        for patch_size in patch_sizes:
            head = nn.Sequential(
                nn.Linear(base_dim, base_dim // 2),
                nn.GELU(),
                nn.Linear(base_dim // 2, out_chans * patch_size * patch_size)
            )
            self.output_heads.append(head)
        
        # Final fusion network
        self.final_fusion = nn.Sequential(
            nn.Conv2d(out_chans * len(patch_sizes), base_dim, 3, 1, 1),
            nn.GroupNorm(8, base_dim),
            nn.GELU(),
            nn.Conv2d(base_dim, base_dim // 2, 3, 1, 1),
            nn.GroupNorm(4, base_dim // 2),
            nn.GELU(),
            nn.Conv2d(base_dim // 2, out_chans, 3, 1, 1)
        )
    
    def forward(self, stage_features, global_features, patch_info):
        """Progressive decoding with multi-scale reconstruction"""
        B, T, N, _ = global_features.shape
        center_idx = T // 2
        
        # Progressive upsampling through stages
        current_features = global_features[:, center_idx, :, :]  # (B, N, D)
        
        for i, (stage_feat, upsample_block) in enumerate(
            zip(reversed(stage_features), self.upsample_blocks)
        ):
            stage_center = stage_feat[:, center_idx, :, :]  # (B, N_stage, D_stage)
            
            if i == 0:
                # First block
                current_features = upsample_block(stage_center)
            else:
                # Subsequent blocks with skip connections
                # Align patch dimensions
                if current_features.shape[1] != stage_center.shape[1]:
                    cf_spatial = rearrange(current_features, 'b (h w) d -> b d h w', 
                                         h=int(math.sqrt(current_features.shape[1])))
                    target_h = int(math.sqrt(stage_center.shape[1]))
                    cf_aligned = F.adaptive_avg_pool2d(cf_spatial, (target_h, target_h))
                    current_features = rearrange(cf_aligned, 'b d h w -> b (h w) d')
                
                # Concatenate and process
                combined = torch.cat([stage_center, current_features], dim=-1)
                current_features = upsample_block(combined)
        
        # Multi-scale output generation
        scale_outputs = []
        
        for patch_size, output_head in zip(self.patch_sizes, self.output_heads):
            # Generate output for this scale
            scale_logits = output_head(current_features)  # (B, N, C*patch_size^2)
            
            # Reshape to spatial format
            spatial_dim = int(math.sqrt(N))
            scale_logits = scale_logits.view(B, spatial_dim, spatial_dim, 
                                           self.out_chans, patch_size, patch_size)
            scale_logits = scale_logits.permute(0, 3, 1, 4, 2, 5)  # (B, C, H, pH, W, pW)
            scale_output = scale_logits.contiguous().view(
                B, self.out_chans, spatial_dim * patch_size, spatial_dim * patch_size
            )
            
            # Resize to target resolution
            if scale_output.shape[-1] != self.img_size:
                scale_output = F.interpolate(
                    scale_output, size=(self.img_size, self.img_size),
                    mode='bilinear', align_corners=False
                )
            
            scale_outputs.append(scale_output)
        
        # Fuse multi-scale outputs
        if len(scale_outputs) > 1:
            multi_scale_concat = torch.cat(scale_outputs, dim=1)
            final_output = self.final_fusion(multi_scale_concat)
        else:
            final_output = scale_outputs[0]
        
        return final_output


class OptimizedSTTNWaterNet(nn.Module):
    """
    Optimized STTN Water Network with 30M parameters
    
    Key features:
    - Adaptive patch sizing based on water frequency
    - Hierarchical 4-stage encoder (30M parameters)
    - Spatial-temporal attention mechanisms
    - Enhanced geographic-temporal modeling
    - Progressive multi-scale decoding
    """
    
    def __init__(self,
                 img_size=256,
                 patch_sizes=[4, 8, 16, 32],
                 in_chans=2,
                 out_chans=2,
                 base_embed_dim=128,
                 stage_depths=[2, 4, 6, 4],
                 num_heads=[4, 8, 16, 24],
                 mlp_ratio=4.0,
                 qkv_bias=True,
                 drop_rate=0.1,
                 attn_drop_rate=0.1,
                 drop_path_rate=0.2):
        super().__init__()
        
        self.img_size = img_size
        self.patch_sizes = patch_sizes
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.base_embed_dim = base_embed_dim
        
        # Adaptive patch embedding with frequency-based selection
        self.patch_embedding = AdaptivePatchSizeSelector(
            patch_sizes=patch_sizes, embed_dim=base_embed_dim, img_size=img_size
        )
        
        # Hierarchical STTN encoder (30M parameters)
        self.encoder = HierarchicalSTTNEncoder(
            base_embed_dim=base_embed_dim, stage_depths=stage_depths,
            num_heads=num_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
            drop_rate=drop_rate, attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate
        )
        
        # Enhanced geographic-temporal embedding
        self.geo_temporal_embedding = EnhancedGeographicTemporalEmbedding(
            embed_dim=base_embed_dim
        )
        
        # Progressive decoder
        stage_dims = [base_embed_dim * (2 ** i) for i in range(len(stage_depths))]
        self.decoder = ProgressiveSTTNDecoder(
            stage_dims=stage_dims, patch_sizes=patch_sizes,
            img_size=img_size, out_chans=out_chans, base_dim=base_embed_dim
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
        # Log model complexity
        self._log_model_info()
    
    def _init_weights(self, m):
        """Weight initialization"""
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.Embedding):
            nn.init.trunc_normal_(m.weight, std=0.02)
    
    def _log_model_info(self):
        """Log model parameter count"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        logger.info(f"Optimized STTN Water Net:")
        logger.info(f"  Total parameters: {total_params:,} ({total_params/1e6:.1f}M)")
        logger.info(f"  Trainable parameters: {trainable_params:,}")
        logger.info(f"  Model size: ~{total_params * 4 / 1024 / 1024:.1f} MB")
    
    def forward(self, batch):
        """
        Forward pass with adaptive patch sizing
        
        Args:
            batch: dict containing:
                - 'input_sequence': (B, T, 2, H, W)
                - 'occurrence': (B, H, W) water frequency map
                - 'tile_lon', 'tile_lat': (B,) coordinates
                - 'year', 'month': (B,) temporal info
        """
        x = batch['input_sequence']  # (B, T, 2, H, W)
        water_frequency = batch.get('occurrence', None)  # (B, H, W)
        
        B, T, C, H, W = x.shape
        
        # 1. Adaptive patch embedding based on water frequency
        patches, patch_info = self.patch_embedding(x, water_frequency)
        
        # 2. Enhanced geographic-temporal encoding
        geo_temp_embed = self.geo_temporal_embedding(
            batch['tile_lon'], batch['tile_lat'],
            batch['year'].float(), batch['month'].float()
        )
        
        # 3. Integrate geographic-temporal context
        geo_temp_expanded = geo_temp_embed.unsqueeze(1).unsqueeze(1)  # (B, 1, 1, D)
        geo_temp_expanded = geo_temp_expanded.expand(B, T, patches.shape[2], -1)
        
        # Feature fusion
        enhanced_patches = patches + 0.1 * geo_temp_expanded
        
        # 4. Create missing mask for attention
        missing_mask = self._create_adaptive_mask(x, patch_info)
        
        # 5. Hierarchical encoding
        stage_features, global_features = self.encoder(enhanced_patches, missing_mask)
        
        # 6. Progressive decoding
        logits = self.decoder(stage_features, global_features, patch_info)
        
        return {
            'inpaint': {
                'logits': logits
            }
        }
    
    def _create_adaptive_mask(self, x, patch_info):
        """Create adaptive missing mask based on patch info"""
        B, T, C, H, W = x.shape
        missing_channel = x[:, :, 1, :, :]  # (B, T, H, W)
        
        # Use the resolution from adaptive patch embedding
        target_res = patch_info['resolution']
        
        # Downsample missing mask to patch resolution
        missing_downsampled = F.adaptive_avg_pool2d(
            missing_channel.view(B*T, 1, H, W), target_res
        ).view(B, T, *target_res)
        
        # Convert to patch format
        missing_mask = rearrange(missing_downsampled, 'b t h w -> b t (h w)')
        missing_mask = missing_mask > 0.5  # Binarize
        
        return missing_mask


def create_swin_water_net(config):
    """
    Create optimized STTN water network from configuration
    """
    # Get model configuration
    if hasattr(config, 'model'):
        model_config = config.model
    else:
        model_config = config.get('model', {})
    
    # Check for STTN-specific configuration first
    sttn_config = None
    if hasattr(model_config, 'sttn_config'):
        sttn_config = model_config.sttn_config
    elif hasattr(model_config, 'get'):
        sttn_config = model_config.get('sttn_config', None)
    else:
        sttn_config = getattr(model_config, 'sttn_config', None)
    
    # Fall back to swin_config if no STTN config
    if sttn_config is None:
        if hasattr(model_config, 'swin_config'):
            swin_config = model_config.swin_config
        elif hasattr(model_config, 'get'):
            swin_config = model_config.get('swin_config', {})
        else:
            swin_config = getattr(model_config, 'swin_config', {})
        
        # Convert swin_config to sttn_config format
        sttn_config = {
            'img_size': getattr(swin_config, 'img_size', 256) if hasattr(swin_config, 'img_size') else swin_config.get('img_size', 256),
            'base_embed_dim': getattr(swin_config, 'embed_dim', 128) if hasattr(swin_config, 'embed_dim') else swin_config.get('embed_dim', 128),
            'patch_sizes': [4, 8, 16, 32],  # Default adaptive patch sizes
            'stage_depths': [2, 4, 6, 4],   # 30M parameter configuration
            'num_heads': [4, 8, 16, 24]
        }
    
    # Parse STTN configuration
    if isinstance(sttn_config, dict):
        img_size = sttn_config.get('img_size', 256)
        patch_sizes = sttn_config.get('patch_sizes', [4, 8, 16, 32])
        base_embed_dim = sttn_config.get('base_embed_dim', 128)
        stage_depths = sttn_config.get('stage_depths', [2, 4, 6, 4])
        num_heads = sttn_config.get('num_heads', [4, 8, 16, 24])
        in_chans = sttn_config.get('in_chans', 2)
        out_chans = sttn_config.get('out_chans', 2)
        mlp_ratio = sttn_config.get('mlp_ratio', 4.0)
        drop_rate = sttn_config.get('drop_rate', 0.1)
        attn_drop_rate = sttn_config.get('attn_drop_rate', 0.1)
        drop_path_rate = sttn_config.get('drop_path_rate', 0.2)
    else:
        # Handle object-type configuration
        img_size = getattr(sttn_config, 'img_size', 256)
        patch_sizes = getattr(sttn_config, 'patch_sizes', [4, 8, 16, 32])
        base_embed_dim = getattr(sttn_config, 'base_embed_dim', 128)
        stage_depths = getattr(sttn_config, 'stage_depths', [2, 4, 6, 4])
        num_heads = getattr(sttn_config, 'num_heads', [4, 8, 16, 24])
        in_chans = getattr(sttn_config, 'in_chans', 2)
        out_chans = getattr(sttn_config, 'out_chans', 2)
        mlp_ratio = getattr(sttn_config, 'mlp_ratio', 4.0)
        drop_rate = getattr(sttn_config, 'drop_rate', 0.1)
        attn_drop_rate = getattr(sttn_config, 'attn_drop_rate', 0.1)
        drop_path_rate = getattr(sttn_config, 'drop_path_rate', 0.2)
    
    # Create optimized model
    model = OptimizedSTTNWaterNet(
        img_size=img_size,
        patch_sizes=patch_sizes,
        in_chans=in_chans,
        out_chans=out_chans,
        base_embed_dim=base_embed_dim,
        stage_depths=stage_depths,
        num_heads=num_heads,
        mlp_ratio=mlp_ratio,
        qkv_bias=True,
        drop_rate=drop_rate,
        attn_drop_rate=attn_drop_rate,
        drop_path_rate=drop_path_rate
    )
    
    return model


# ======================= Additional Utility Functions =======================

def visualize_attention_weights(attn_weights, save_path=None):
    """
    Visualize attention weights (for debugging and analysis)
    
    Args:
        attn_weights: (B, num_heads, T*N, T*N) attention weights
        save_path: save path (optional)
    """
    import matplotlib.pyplot as plt
    
    # Take first sample's first head
    attn = attn_weights[0, 0].detach().cpu().numpy()
    
    plt.figure(figsize=(10, 10))
    plt.imshow(attn, cmap='Blues', interpolation='nearest')
    plt.colorbar()
    plt.title('STTN Attention Weights')
    plt.xlabel('Key Position (T*N)')
    plt.ylabel('Query Position (T*N)')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


def analyze_memory_usage(model, input_shape=(1, 48, 2, 256, 256)):
    """
    Analyze detailed memory usage of the model

    Args:
        model: STTN model
        input_shape: input shape (B, T, C, H, W)
    """
    B, T, C, H, W = input_shape
    N = H * W  # Number of patches for full resolution

    print(f"=== Memory Usage Analysis ===")
    print(f"Input shape: {input_shape}")
    print(f"Sequence length (T): {T}")
    print(f"Spatial resolution: {H}x{W}")
    print(f"Number of spatial patches (N): {N}")
    print()

    # 1. Input data memory
    input_memory = B * T * C * H * W * 4 / (1024**3)  # 4 bytes per float32
    print(f"1. Input Data Memory: {input_memory:.2f} GB")

    # 2. Patch embedding memory (assuming 4x4 patches)
    patch_size = 4
    num_patches = (H // patch_size) * (W // patch_size)
    embed_dim = 128
    patch_memory = B * T * num_patches * embed_dim * 4 / (1024**3)
    print(f"2. Patch Embedding Memory: {patch_memory:.2f} GB")
    print(f"   - Patches per frame: {num_patches}")
    print(f"   - Total patches in sequence: {T * num_patches}")

    # 3. Attention memory (CRITICAL BOTTLENECK)
    total_tokens = T * num_patches
    num_heads = 8
    attention_matrix_memory = B * num_heads * total_tokens * total_tokens * 4 / (1024**3)
    print(f"3. Attention Matrix Memory: {attention_matrix_memory:.2f} GB ⚠️")
    print(f"   - Total tokens: {total_tokens}")
    print(f"   - Attention matrix size: {total_tokens}x{total_tokens}")

    # 4. Model parameters
    total_params = sum(p.numel() for p in model.parameters())
    param_memory = total_params * 4 / (1024**3)
    print(f"4. Model Parameters: {param_memory:.2f} GB ({total_params/1e6:.1f}M params)")

    # 5. Gradients (same as parameters)
    gradient_memory = param_memory
    print(f"5. Gradients: {gradient_memory:.2f} GB")

    # 6. Optimizer states (AdamW: 2x parameters)
    optimizer_memory = param_memory * 2
    print(f"6. Optimizer States: {optimizer_memory:.2f} GB")

    # 7. Intermediate activations (rough estimate)
    # Each transformer block creates intermediate activations
    num_blocks = 16  # Total blocks across all stages
    mlp_ratio = 4
    intermediate_memory = B * T * num_patches * embed_dim * mlp_ratio * num_blocks * 4 / (1024**3)
    print(f"7. Intermediate Activations: {intermediate_memory:.2f} GB")

    # Total memory estimate
    total_memory = (input_memory + patch_memory + attention_matrix_memory +
                   param_memory + gradient_memory + optimizer_memory + intermediate_memory)

    print(f"\n=== Total Memory Estimate ===")
    print(f"Total: {total_memory:.2f} GB")
    print(f"Available GPU Memory: 80 GB (A100)")
    print(f"Memory Utilization: {total_memory/80*100:.1f}%")

    if total_memory > 80:
        print(f"⚠️  MEMORY OVERFLOW: {total_memory-80:.2f} GB over limit!")

        # Suggest optimizations
        print(f"\n=== Optimization Suggestions ===")

        # Reduce sequence length
        optimal_T = int(T * (80 / total_memory) ** 0.5)
        print(f"1. Reduce sequence length: {T} → {optimal_T}")

        # Reduce spatial resolution
        optimal_H = int(H * (80 / total_memory) ** 0.25)
        print(f"2. Reduce spatial resolution: {H}x{W} → {optimal_H}x{optimal_H}")

        # Use gradient checkpointing
        checkpoint_savings = intermediate_memory * 0.8
        print(f"3. Enable gradient checkpointing: saves ~{checkpoint_savings:.2f} GB")

        # Use sparse attention
        sparse_savings = attention_matrix_memory * 0.9
        print(f"4. Use sparse attention: saves ~{sparse_savings:.2f} GB")

    return total_memory


def analyze_model_complexity(model, input_shape=(1, 48, 2, 256, 256)):
    """
    Analyze model complexity

    Args:
        model: STTN model
        input_shape: input shape
    """
    # First do memory analysis
    memory_usage = analyze_memory_usage(model, input_shape)

    try:
        from thop import profile

        # Create dummy input
        dummy_batch = {
            'input_sequence': torch.randn(input_shape),
            'tile_lon': torch.randn(1),
            'tile_lat': torch.randn(1),
            'year': torch.randint(2000, 2025, (1,)),
            'month': torch.randint(1, 13, (1,))
        }

        # Calculate FLOPs and parameters
        flops, params = profile(model, inputs=(dummy_batch,))

        print(f"\n=== Computational Complexity ===")
        print(f"Parameters: {params / 1e6:.2f}M")
        print(f"FLOPs: {flops / 1e9:.2f}G")

        return flops, params, memory_usage
    except ImportError:
        print("thop library not available for complexity analysis")
        return None, None, memory_usage