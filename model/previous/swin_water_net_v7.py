"""
Video SwinWaterNet - Multi-Scale Temporal Inpainting Model

Input: (B, 120, C, H, W) video sequence
    ↓
Multi-scale temporal sampling → [16, 16, 16] frames
    ↓
Patch embedding → [(B, 16, N, D), (B, 16, N, D), (B, 16, N, D)]
    ↓
Multi-scale temporal attention → target-guided weighted features
    ↓
Format conversion → [(B, 24*N, D), (B, 16*N, D), (B, 8*N, D)]
    ↓
Geo-temporal injection → conditionally enhanced features
    ↓
Encoder processing → multi-scale feature fusion and encoding
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import math
import logging
from einops import rearrange
from .loss import compute_dynamic_degree

logger = logging.getLogger(__name__)

from model.previous.sampler import AttentionGuidedPyramidSampling
from model.previous.decoder import MemoryEfficientDecoder, PerfectUNetDecoder


class PatchEmbed(nn.Module):
    """Memory-efficient patch embedding for video sequences"""
    
    def __init__(self, img_size=256, patch_size=8, in_chans=2, embed_dim=128):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.grid_size = img_size // patch_size
        
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)
    
    def forward(self, x):
        """Args: x: (B, T, C, H, W) video sequence
           Returns: x: (B, T, N, D) sequence of embedded patches"""
        B, T, C, H, W = x.shape
        
        x = x.flatten(0, 1)  # (B*T, C, H, W)
        x = self.proj(x)  # (B*T, D, H', W')
        
        D, H_p, W_p = x.shape[1], x.shape[2], x.shape[3]
        x = x.reshape(B, T, D, H_p, W_p)
        x = x.permute(0, 1, 3, 4, 2)  # (B, T, H', W', D)
        x = x.reshape(B, T, H_p*W_p, D)  # (B, T, N, D)
        
        return self.norm(x)


class WindowAttention(nn.Module):
    """Window-based multi-head self attention with relative position bias"""
    
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))

        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w]))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)
        
    def forward(self, x, mask=None):
        """Standard attention computation"""
        B_, N, C = x.shape
        
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        
        # Add relative position bias
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1], self.window_size[0] * self.window_size[1], -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)
        
        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
        
        attn = self.softmax(torch.clamp(attn, min=-80.0, max=80.0))
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


def window_partition(x, window_size):
    """Partitions a tensor into non-overlapping windows"""
    B, H, W, C = x.shape
    
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    
    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W
    
    x = x.view(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """Merges windows back to the original tensor shape"""
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w
    
    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.view(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_padded, W_padded, -1)
    
    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]
    
    return x


class TemporalSeparableAttention(nn.Module):
    """Memory-efficient temporal attention with separable operations and windowing"""
    
    def __init__(self, dim, num_heads, window_size=8, qkv_bias=False, dropout=0.):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        
        if isinstance(window_size, (list, tuple)):
            spatial_window_size = window_size[1]
        else:
            spatial_window_size = window_size
        self.window_size = spatial_window_size

        self.spatial_attn = WindowAttention(
            dim=dim, window_size=(self.window_size, self.window_size),
            num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=dropout, proj_drop=dropout
        )
        
        self.temporal_attn = nn.MultiheadAttention(
            embed_dim=dim, num_heads=num_heads, dropout=dropout, batch_first=True
        )
        
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(dropout)
    
    def forward(self, x, T, H, W):
        """Apply separable space-time attention"""
        B, L, C = x.shape
        assert L == T * H * W, f"Mismatched dimensions: L={L}, T*H*W={T*H*W}"
        
        # Spatial attention
        x_spatial = rearrange(x, 'b (t h w) c -> (b t) h w c', t=T, h=H, w=W)
        x_windows = window_partition(x_spatial, self.window_size)
        x_windows = x_windows.view(-1, self.window_size * self.window_size, C)
        attn_windows = self.spatial_attn(x_windows)
        
        attn_windows = attn_windows.view(-1, self.window_size, self.window_size, C)
        x_spatial = window_reverse(attn_windows, self.window_size, H, W)
        x_spatial = x_spatial.reshape(B*T, H * W, C)

        # Temporal attention
        x_temporal = rearrange(x_spatial, '(b t) (h w) c -> (b h w) t c', b=B, t=T, h=H, w=W)
        x_temporal, _ = self.temporal_attn(x_temporal, x_temporal, x_temporal)
        x_out = rearrange(x_temporal, '(b h w) t c -> b (t h w) c', b=B, h=H, w=W)
        
        return self.proj_drop(self.proj(x_out))


class TransformerBlock(nn.Module):
    """Memory-efficient transformer block with separable space-time attention"""
    
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, use_checkpoint=True, window_size=8):
        super().__init__()
        self.use_checkpoint = use_checkpoint
        self.norm1 = norm_layer(dim)
        
        self.attn = TemporalSeparableAttention(
            dim=dim, num_heads=num_heads, qkv_bias=qkv_bias, dropout=attn_drop, window_size=window_size
        )
        
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)
        
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim), act_layer(), nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim), nn.Dropout(drop)
        )
    
    def _forward_impl(self, x, T, H, W):
        x = x + self.drop_path(self.attn(self.norm1(x), T, H, W))
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        return x
    
    def forward(self, x, T, H, W):
        if self.use_checkpoint and self.training:
            return torch.utils.checkpoint.checkpoint(self._forward_impl, x, T, H, W, use_reentrant=False)
        else:
            return self._forward_impl(x, T, H, W)


class TemporalDownsample(nn.Module):
    """Temporal downsampling module"""
    
    def __init__(self, dim):
        super().__init__()
        self.maxpool = nn.MaxPool3d(kernel_size=(2,1,1), stride=(2,1,1))
        
    def forward(self, x, T, H, W):
        B, L, C = x.shape
        expected_L = T * H * W
        
        if L != expected_L:
            L_adjusted = (L // (H * W)) * H * W
            x = x[:, :L_adjusted, :]
            T = L_adjusted // (H * W)
        
        x = x.reshape(B, T, H, W, C).permute(0, 4, 1, 2, 3)
        x = self.maxpool(x)
        x = x.permute(0, 2, 3, 4, 1)
        new_T = x.shape[1]
        x = x.reshape(B, new_T * H * W, C)
        
        return x, new_T


class MultiScaleEncoder(nn.Module):
    """Multi-scale encoder with progressive temporal and spatial downsampling"""
    
    def __init__(self, embed_dim=128, depths=[2, 2, 6, 2], num_heads=[4, 8, 16, 32],
                 window_sizes=[8, 8, 8, 8], mlp_ratio=4., qkv_bias=False, drop_rate=0.,
                 attn_drop_rate=0., drop_path_rate=0.1, norm_layer=nn.LayerNorm, use_checkpoint=True):
        super().__init__()
        
        self.num_layers = len(depths)
        self.embed_dim = embed_dim
        
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        self.layers = nn.ModuleList()
        self.temporal_downsamples = nn.ModuleList()
        self.spatial_downsamples = nn.ModuleList()
        
        curr_dim = embed_dim
        
        for i_layer in range(self.num_layers):
            layer_depth = depths[i_layer]
            layer_dim = curr_dim
            layer_num_heads = num_heads[i_layer]
            
            blocks = nn.ModuleList([
                TransformerBlock(
                    dim=layer_dim, num_heads=layer_num_heads, mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias, drop=drop_rate, attn_drop=attn_drop_rate,
                    drop_path=dpr[sum(depths[:i_layer]) + i_block], norm_layer=norm_layer,
                    use_checkpoint=use_checkpoint, window_size=window_sizes[i_layer]
                )
                for i_block in range(layer_depth)
            ])
            self.layers.append(blocks)
            
            if i_layer < self.num_layers - 1:
                next_dim = curr_dim * 2
                self.temporal_downsamples.append(TemporalDownsample(curr_dim))
                # Spatial downsampling: reduce spatial dimensions by 2x2
                self.spatial_downsamples.append(nn.Linear(curr_dim, next_dim))
                curr_dim = next_dim

    def forward(self, x_pyramid, T_pyramid, H, W, temporal_indices_pyramid=None):
        """Forward pass through multi-scale encoder with direct concatenation of scales"""
        features = []
        
        # Direct concatenation of all scales along temporal dimension
        all_features = []
        all_T_values = []
        all_temporal_indices = []  # Track temporal indices for proper sorting
        
        for i, (x_scale, T_scale) in enumerate(zip(x_pyramid, T_pyramid)):
            # Reshape to separate temporal and spatial dimensions for sorting
            B, L, C = x_scale.shape
            target_patches = H * W
            T_actual = max(1, L // target_patches)
            
            # Ensure proper reshaping
            if L != T_actual * target_patches:
                if L > T_actual * target_patches:
                    x_scale = x_scale[:, :T_actual * target_patches, :]
                else:
                    padding_size = T_actual * target_patches - L
                    x_scale = F.pad(x_scale, (0, 0, 0, padding_size))
            
            # Reshape to (B, T, H*W, C) for temporal sorting
            x_reshaped = x_scale.reshape(B, T_actual, target_patches, C)
            all_features.append(x_reshaped)
            all_T_values.append(T_actual)
            
            # Use provided temporal indices if available, otherwise use sequential ordering
            if temporal_indices_pyramid is not None and i < len(temporal_indices_pyramid):
                # Use the actual temporal indices from the sampler
                temporal_indices = temporal_indices_pyramid[i]  # (B, T_actual)
            else:
                # Fallback to sequential ordering
                temporal_indices = torch.arange(T_actual, device=x_scale.device).unsqueeze(0).expand(B, -1)
            all_temporal_indices.append(temporal_indices)
        
        # Concatenate all scales along temporal dimension
        x_concat = torch.cat(all_features, dim=1)  # (B, T_total, H*W, C)
        T_total = sum(all_T_values)
        
        # Concatenate temporal indices for sorting
        temporal_indices_concat = torch.cat(all_temporal_indices, dim=1)  # (B, T_total)
        
        # Sort by temporal indices to ensure proper time ordering
        # Get sorting indices for each batch
        sorted_indices = torch.argsort(temporal_indices_concat, dim=1)  # (B, T_total)
        
        # Apply sorting to features
        B, T_total, N, C = x_concat.shape
        batch_indices = torch.arange(B, device=x_concat.device).unsqueeze(1).expand(-1, T_total)
        x_sorted = x_concat[batch_indices, sorted_indices]  # (B, T_total, H*W, C)
        
        # Flatten back to (B, T_total*H*W, C)
        x = x_sorted.reshape(x_sorted.shape[0], -1, x_sorted.shape[-1])
        
        curr_H, curr_W = H, W
        real_T_values = [T_total]
        spatial_sizes = [(curr_H, curr_W)]
        
        # Process through encoder layers
        for i_layer, blocks in enumerate(self.layers):
            # Process blocks
            for block in blocks:
                x = block(x, T_total, curr_H, curr_W)
            
            # Store features with spatial size info
            if i_layer < len(self.layers) - 1:
                features.append((x.detach(), curr_H, curr_W))
            else:
                features.append((x, curr_H, curr_W))
            
            # Downsampling
            if i_layer < self.num_layers - 1:
                # Temporal downsampling
                x, T_total = self.temporal_downsamples[i_layer](x, T_total, curr_H, curr_W)
                real_T_values.append(T_total)
                
                # Spatial downsampling: reduce H and W by 2
                curr_H, curr_W = curr_H // 2, curr_W // 2
                spatial_sizes.append((curr_H, curr_W))
                
                # Reshape and downsample spatially
                x = rearrange(x, 'b (t h w) c -> b c t h w', t=T_total, h=curr_H*2, w=curr_W*2)
                x = F.avg_pool3d(x, kernel_size=(1, 2, 2), stride=(1, 2, 2))
                x = rearrange(x, 'b c t h w -> b (t h w) c')
                
                # Channel expansion
                x = self.spatial_downsamples[i_layer](x)
        
        return features, real_T_values, spatial_sizes


class GeoTemporalEmbedding(nn.Module):
    """Efficient embedding for geographic and temporal conditioning"""
    
    def __init__(self, embed_dim=128, num_freq_bands=6):
        super().__init__()
        self.embed_dim = embed_dim
        
        self.register_buffer('freq_bands', 2.0 ** torch.linspace(0, num_freq_bands-1, num_freq_bands))
        
        geo_fourier_dim = 4 * num_freq_bands
        temporal_fourier_dim = 2 * num_freq_bands
        year_embed_dim = 16
        
        self.year_embed = nn.Embedding(200, year_embed_dim)
        
        total_dim = geo_fourier_dim + temporal_fourier_dim + year_embed_dim
        self.projector = nn.Sequential(
            nn.Linear(total_dim, embed_dim), nn.LayerNorm(embed_dim), nn.GELU()
        )
        
        self.scale_shift = nn.Linear(embed_dim, embed_dim * 2)
    
    def forward(self, lon, lat, year, month):
        """Encode geographic and temporal features"""
        B = lon.shape[0]
        
        # Geographic features
        lon_scaled = lon / 180.0 * math.pi
        lat_scaled = lat / 90.0 * math.pi
        
        freq_bands_expanded = self.freq_bands.unsqueeze(0)
        lon_expanded = lon_scaled.unsqueeze(1)
        lat_expanded = lat_scaled.unsqueeze(1)
        
        lon_freq = lon_expanded * freq_bands_expanded
        lat_freq = lat_expanded * freq_bands_expanded
        
        lon_sin = torch.sin(lon_freq)
        lon_cos = torch.cos(lon_freq)
        lat_sin = torch.sin(lat_freq)
        lat_cos = torch.cos(lat_freq)
        
        geo_features = torch.cat([lon_sin, lon_cos, lat_sin, lat_cos], dim=1)
        
        # Temporal features
        month_angle = (month - 1) / 12.0 * 2 * math.pi
        month_expanded = month_angle.unsqueeze(1)
        month_freq = month_expanded * freq_bands_expanded
        month_sin = torch.sin(month_freq)
        month_cos = torch.cos(month_freq)
        temporal_features = torch.cat([month_sin, month_cos], dim=1)
        
        # Year embedding
        year_idx = torch.clamp(year - 1900, 0, 199).long()
        year_features = self.year_embed(year_idx)
        
        # Combine and project
        combined = torch.cat([geo_features, temporal_features, year_features], dim=-1)
        return self.projector(combined)
    
    def modulate(self, x):
        """Apply FiLM-style modulation to features"""
        scale_shift = self.scale_shift(x)
        scale, shift = scale_shift.chunk(2, dim=-1)
        scale = 1 + 0.1 * torch.tanh(scale)
        return scale, shift


class MultiScaleTemporalAttention(nn.Module):
    """Multi-scale temporal attention module"""
    
    def __init__(self, embed_dim, num_heads, num_scales=3, max_frames=32):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_scales = num_scales
        self.max_frames = max_frames
        
        self.scale_q_proj = nn.ModuleDict({
            f'scale_{i}': nn.Linear(embed_dim, embed_dim) for i in range(num_scales)
        })
        self.scale_k_proj = nn.ModuleDict({
            f'scale_{i}': nn.Linear(embed_dim, embed_dim) for i in range(num_scales)
        })
        self.scale_v_proj = nn.ModuleDict({
            f'scale_{i}': nn.Linear(embed_dim, embed_dim) for i in range(num_scales)
        })
        
        self.pos_embed = nn.Parameter(torch.randn(1, max_frames, embed_dim) * 0.02)
        
        self.out_proj = nn.ModuleDict({
            f'scale_{i}': nn.Linear(embed_dim, embed_dim) for i in range(num_scales)
        })
        self.norm = nn.ModuleDict({
            f'scale_{i}': nn.LayerNorm(embed_dim) for i in range(num_scales)
        })
    
    def forward(self, multi_scale_features, target_features, water_frequency=None):
        """Multi-scale temporal attention forward pass"""
        if not isinstance(multi_scale_features, list):
            multi_scale_features = [multi_scale_features]
        
        weighted_features = []
        
        for scale_idx, scale_features in enumerate(multi_scale_features):
            B_i, T_i, N_i, D_i = scale_features.shape
            
            # Add positional encoding
            if T_i <= self.max_frames:
                pos_embed = self.pos_embed[:, :T_i, :].unsqueeze(2)
            else:
                pos_embed = F.interpolate(
                    self.pos_embed.transpose(1, 2), size=T_i, mode='linear', align_corners=False
                ).transpose(1, 2).unsqueeze(2)
            
            x_with_pos = scale_features + pos_embed
            
            # Reshape for attention
            x_reshaped = x_with_pos.permute(0, 2, 1, 3).reshape(B_i*N_i, T_i, D_i)
            target_reshaped = target_features.reshape(B_i*N_i, D_i)
            
            # Compute QKV
            Q = self.scale_q_proj[f'scale_{scale_idx}'](target_reshaped).unsqueeze(1)
            K = self.scale_k_proj[f'scale_{scale_idx}'](x_reshaped)
            V = self.scale_v_proj[f'scale_{scale_idx}'](x_reshaped)
            
            # Attention
            scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(D_i)
            scores = torch.clamp(scores, min=-80.0, max=80.0)
            attn_weights = F.softmax(scores, dim=-1)
            
            if torch.isnan(attn_weights).any() or torch.isinf(attn_weights).any():
                attn_weights = torch.ones_like(attn_weights) / T_i
            
            attn_out = torch.matmul(attn_weights, V).squeeze(1)
            attn_out = self.out_proj[f'scale_{scale_idx}'](attn_out)
            
            # Reshape and combine
            attn_out = attn_out.reshape(B_i, N_i, D_i).unsqueeze(1).expand(-1, T_i, -1, -1)
            weighted_x = x_with_pos + attn_out
            weighted_x = self.norm[f'scale_{scale_idx}'](weighted_x + scale_features)
            
            weighted_features.append(weighted_x)
        
        return weighted_features


class SwinWaterNet(nn.Module):
    """Optimized SwinWater model for video inpainting"""
    
    def __init__(self, img_size=256, patch_size=4, in_chans=2, out_chans=2, num_frames=120,
                 num_temporal_scales=3, frame_counts=[16, 16, 16], embed_dim=128,
                 depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], window_size=7, mlp_ratio=4.,
                 qkv_bias=True, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, use_checkpoint=False):
        super().__init__()
        
        self.num_frames = num_frames
        self.num_temporal_scales = num_temporal_scales
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_heads = num_heads
        self.window_size = window_size
        self.use_checkpoint = use_checkpoint
        
        # Temporal sampling
        self.temporal_sampling = AttentionGuidedPyramidSampling(
            num_frames=self.num_frames, num_scales=self.num_temporal_scales,
            frame_counts=frame_counts, feature_dim=32
        )
        
        # Patch embedding
        self.patch_embed = PatchEmbed(
            img_size=self.img_size, patch_size=self.patch_size,
            in_chans=self.in_chans, embed_dim=self.embed_dim
        )
        
        # Temporal attention
        self.temporal_attention = MultiScaleTemporalAttention(
            embed_dim=self.embed_dim, num_heads=self.num_heads[0],
            num_scales=self.num_temporal_scales, max_frames=self.num_frames
        )
        
        # Encoder
        self.encoder = MultiScaleEncoder(
            embed_dim=self.embed_dim, depths=self.depths, num_heads=self.num_heads,
            window_sizes=[self.window_size] * len(self.depths), mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias, drop_rate=drop_rate, attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate, use_checkpoint=self.use_checkpoint
        )
        
        # Geographic temporal encoding
        self.geo_temporal_encoder = GeoTemporalEmbedding(embed_dim=self.embed_dim)
        
        # Decoder - Using U-Net style decoder
        final_dim = self.embed_dim * (2 ** (len(self.depths) - 1))
        self.decoder = PerfectUNetDecoder(
            in_channels=final_dim,
            img_size=self.img_size, patch_size=self.patch_size,
            num_classes=self.out_chans, use_checkpoint=self.use_checkpoint
        )
        
        self.apply(self._init_weights)
        
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
            
    def forward(self, batch):
        """Forward pass"""
        x = batch['input_sequence']
        center_frame_idx = batch.get('center_frame_idx', None)
        
        B, T, C, H, W = x.shape
        device = x.device
        
        # Normalize center_frame_idx
        if center_frame_idx is None:
            center_frame_idx = torch.full((B,), T // 2, device=device, dtype=torch.long)
        elif isinstance(center_frame_idx, (int, float)):
            center_frame_idx = torch.tensor([int(center_frame_idx)] * B, device=device, dtype=torch.long)
        elif center_frame_idx.numel() == 1 and B > 1:
            center_frame_idx = center_frame_idx.expand(B)
        
        # Extract target frame
        batch_indices = torch.arange(B, device=device)
        target_frames = x[batch_indices, center_frame_idx]
        target_patches = self.patch_embed(target_frames.unsqueeze(1))
        target_features = target_patches.squeeze(1)
        
        # Get water frequency
        water_frequency = batch.get('occurrence', None)
        
        # Temporal sampling
        multi_scale_x, multi_scale_indices = self.temporal_sampling(x, center_frame_idx, water_frequency)
        
        # Process multi-scale features
        multi_scale_features = []
        multi_scale_T = []
        multi_scale_embeddings = []
        
        for scale_x in multi_scale_x:
            x_embed = self.patch_embed(scale_x)
            B_e, T_e, N_e, D_e = x_embed.shape
            multi_scale_embeddings.append(x_embed)
            x_flat = x_embed.reshape(B_e, T_e * N_e, D_e)
            multi_scale_features.append(x_flat)
            multi_scale_T.append(T_e)
        
        # Apply temporal attention
        weighted_embeddings = self.temporal_attention(multi_scale_embeddings, target_features, water_frequency)
        
        # Convert to encoder format
        multi_scale_features = []
        multi_scale_T = []
        
        for weighted_embed in weighted_embeddings:
            B_w, T_w, N_w, D_w = weighted_embed.shape
            weighted_flat = weighted_embed.reshape(B_w, T_w * N_w, D_w)
            multi_scale_features.append(weighted_flat)
            multi_scale_T.append(T_w)
        
        # Geographic temporal encoding
        geo_temporal_encoding = self.geo_temporal_encoder(
            batch['tile_lon'], batch['tile_lat'], batch['year'], batch['month']
        )
        
        # Add geographic encoding to features
        if len(multi_scale_features) > 0:
            B_f, L_f, D_f = multi_scale_features[0].shape
            geo_encoding_expanded = geo_temporal_encoding.unsqueeze(1).expand(
                B_f, max(L_f for _, L_f, _ in [f.shape for f in multi_scale_features]), D_f
            )
            
            for i in range(len(multi_scale_features)):
                B_i, L_i, D_i = multi_scale_features[i].shape
                multi_scale_features[i] = multi_scale_features[i] + 0.1 * geo_encoding_expanded[:, :L_i, :]
        
        # Multi-scale encoding
        H_p = W_p = self.img_size // self.patch_size
        encoder_features, real_T_values, spatial_sizes = self.encoder(multi_scale_features, multi_scale_T, H_p, W_p, multi_scale_indices)
        
        # Prepare decoder features - Handle concatenated multi-scale features
        def preprocess_features_for_decoder(feat_tuple):
            """
            Unified preprocessing function that converts concatenated multi-scale encoder features to spatial format
            Args:
                feat_tuple: (feat, H, W) - encoder feature with spatial dimensions
            Returns:
                processed_feat: (B, C, H, W) - spatial feature ready for decoder
            """
            feat, H, W = feat_tuple
            B_f, L_f, D_f = feat.shape
            
            # Calculate temporal and spatial dimensions
            target_patches = H * W
            T_s = max(1, L_f // target_patches)
            L_exact = T_s * target_patches
            
            # Handle dimension mismatch
            if L_f != L_exact:
                if L_f > L_exact:
                    feat = feat[:, :L_exact, :]  # Crop
                else:
                    padding_size = L_exact - L_f
                    feat = F.pad(feat, (0, 0, 0, padding_size))  # Pad
            
            # Reshape to separate temporal and spatial dimensions
            feat_reshaped = feat.reshape(B_f, T_s, H, W, D_f)
            
            # Temporal aggregation using mean pooling for concatenated multi-scale features
            # This averages across all temporal scales that were concatenated
            feat_spatial = feat_reshaped.mean(dim=1)  # (B, H, W, C)
            
            # Convert to convolution format
            feat_spatial = feat_spatial.permute(0, 3, 1, 2)  # (B, C, H, W)
            
            return feat_spatial
        
        # Preprocess all encoder features to spatial format
        decoder_features = [preprocess_features_for_decoder(feat_tuple) for feat_tuple in encoder_features]
        
        # Main decoder input (bottleneck feature)
        decoder_input = decoder_features[-1]  # (B, C, H, W)
        
        # Skip features for U-Net style connections (shallow to deep)
        skip_features = decoder_features[:-1]  # List of (B, C, H, W) features
        
        # Decode using U-Net style decoder
        logits, confidence = self.decoder(
            decoder_input, skip_features, None, water_frequency
        )
        
        return {'inpaint': {'logits': logits, 'confidence': confidence}}


def create_swin_water_net(config):
    """Create SwinWaterNet from configuration"""
    if hasattr(config, 'model'):
        model_config = config.model
    else:
        model_config = config.get('model', {})
    
    if hasattr(model_config, 'swin_config'):
        swin_config = model_config.swin_config
    else:
        swin_config = model_config.get('swin_config', {})
    
    if hasattr(model_config, 'get'):
        use_checkpoint = model_config.get('use_gradient_checkpoint', True)
    else:
        use_checkpoint = getattr(model_config, 'use_gradient_checkpoint', True)
        
    if isinstance(swin_config, dict):
        num_temporal_scales = swin_config.get('num_temporal_scales', 3)
        frame_counts = swin_config.get('frame_counts', [16, 16, 16])
        num_frames = swin_config.get('num_frames', 120)
        embed_dim = swin_config.get('embed_dim', 128)
        patch_size = swin_config.get('patch_size', 8)
        window_sizes = swin_config.get('window_sizes', [8, 8, 8, 8])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes
        depths = swin_config.get('depths', [2, 2, 6, 2])
        num_heads = swin_config.get('num_heads', [3, 6, 12, 24])
        img_size = swin_config.get('img_size', 256)
        in_chans = swin_config.get('in_chans', 2)
        out_chans = swin_config.get('out_chans', 2)
    else:
        num_temporal_scales = getattr(swin_config, 'num_temporal_scales', 3)
        frame_counts = getattr(swin_config, 'frame_counts', [16, 16, 16])
        num_frames = getattr(swin_config, 'num_frames', 120)
        embed_dim = getattr(swin_config, 'embed_dim', 128)
        patch_size = getattr(swin_config, 'patch_size', 8)
        window_sizes = getattr(swin_config, 'window_sizes', [8, 8, 8, 8])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes
        depths = getattr(swin_config, 'depths', [2, 2, 6, 2])
        num_heads = getattr(swin_config, 'num_heads', [3, 6, 12, 24])
        img_size = getattr(swin_config, 'img_size', 256)
        in_chans = getattr(swin_config, 'in_chans', 2)
        out_chans = getattr(swin_config, 'out_chans', 2)
    
    model = SwinWaterNet(
        img_size=img_size, patch_size=patch_size, in_chans=in_chans, out_chans=out_chans,
        embed_dim=embed_dim, depths=depths, num_heads=num_heads, window_size=window_size,
        num_frames=num_frames, num_temporal_scales=num_temporal_scales,
        frame_counts=frame_counts, use_checkpoint=use_checkpoint
    )
    
    return model