"""
Swin Water Net v16: Simplified Architecture with Configurable Patch Size

Key Features:
1. Center frame extraction using center_frame_idx indexing
2. Configurable patch size (default 8x8) for flexible processing
3. Center frame as Query, other frames as Key/Value in attention
4. Swin Transformer stack with [2, 2, 6, 2] blocks per stage
5. U-Net style decoder for final reconstruction
6. Fully end-to-end trainable with mixed precision support
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


from model.decoder_v8 import SwinUNetDecoder

logger = logging.getLogger(__name__)


# ============================================================================
# Core Swin Transformer Components (from v10/v13)
# ============================================================================

class CyclicShift(nn.Module):
    """Cyclic shift operation"""
    def __init__(self, displacement):
        super().__init__()
        self.displacement = displacement

    def forward(self, x):
        return torch.roll(x, shifts=(self.displacement, self.displacement), dims=(1, 2))


class Residual(nn.Module):
    """Residual connection"""
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(x, **kwargs) + x


class PreNorm(nn.Module):
    """Pre-normalization"""
    def __init__(self, dim, fn):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)


class FeedForward(nn.Module):
    """Feed forward network"""
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim),
        )

    def forward(self, x):
        return self.net(x)


def create_shifted_window_mask(H, W, window_size, shift_size):
    """Create attention mask for shifted windows"""
    if shift_size == 0:
        return None

    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    img_mask = torch.zeros((1, H_padded, W_padded, 1))

    # FULLY VECTORIZED: Create mask using tensor operations
    # Create region masks for each slice combination
    h_region = torch.zeros(H_padded, dtype=torch.long)
    w_region = torch.zeros(W_padded, dtype=torch.long)

    # Assign region indices based on slice boundaries
    h_region[:-window_size] = 0
    h_region[-window_size:-shift_size] = 1
    h_region[-shift_size:] = 2

    w_region[:-window_size] = 0
    w_region[-window_size:-shift_size] = 1
    w_region[-shift_size:] = 2

    # Create 2D region map
    h_grid, w_grid = torch.meshgrid(h_region, w_region, indexing='ij')
    region_map = h_grid * 3 + w_grid  # Combine h and w regions into single index

    # Set the mask
    img_mask[0, :, :, 0] = region_map

    mask_windows = window_partition(img_mask, window_size)
    mask_windows = mask_windows.view(-1, window_size * window_size)
    attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
    attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))

    return attn_mask


def window_partition(x, window_size):
    """Partition input into windows"""
    B, H, W, C = x.shape
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size

    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W

    x = x.view(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """Reverse window partition"""
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.view(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_padded, W_padded, -1)

    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]

    return x


# ============================================================================
# Simplified Patch Embedding with Configurable Patch Size
# ============================================================================

class DifferentiablePatchEmbed(nn.Module):
    """Simplified patch embedding with configurable patch size"""

    def __init__(self, img_size=256, patch_size=8, in_chans=2, embed_dim=96):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.embed_dim = embed_dim

        # Single patch embedding layer with configurable patch_size
        self.patch_embed = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)

        # Position embedding for the specified patch_size
        num_patches = (img_size // patch_size) ** 2
        self.position_embed = nn.Parameter(torch.zeros(1, num_patches, embed_dim))

        # Normalization
        self.norm = nn.LayerNorm(embed_dim)

        self._init_weights()

    def _init_weights(self):
        # Initialize position embedding
        nn.init.trunc_normal_(self.position_embed, std=.02)
    
    def forward(self, x):
        """
        Args:
            x: (B, T, C, H, W) video frames
            _water_frequency: (B,) mean water frequency per sample (ignored, kept for compatibility)
        Returns:
            patches: (B, T, N, D) embedded patches
            patch_size_weights: None (kept for compatibility)
        """
        B, T, C, H, W = x.shape

        # Process all frames at once: (B*T, C, H, W)
        x_reshaped = x.view(B * T, C, H, W)

        # Extract patches using fixed patch_size=8
        patches = self.patch_embed(x_reshaped)  # (B*T, D, H', W')
        _, D, H_p, W_p = patches.shape
        N = H_p * W_p

        # Reshape and add position embedding
        patches = patches.flatten(2).transpose(1, 2)  # (B*T, N, D)
        patches = patches + self.position_embed[:, :patches.size(1), :]

        # Normalize
        patches = self.norm(patches)

        # Reshape back to (B, T, N, D)
        patches = patches.view(B, T, N, D)

        return patches, None


# ============================================================================
# Multi-Scale Patch Attention (inspired by STTN)
# ============================================================================

class MultiScalePatchAttention(nn.Module):
    """Multi-scale patch attention with attention mask mechanism"""

    def __init__(self, dim, patch_sizes=[4, 8, 16, 32], num_heads=8, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.patch_sizes = patch_sizes
        self.num_heads = num_heads
        self.num_scales = len(patch_sizes)

        # Each scale gets equal portion of channels
        self.d_k = dim // self.num_scales
        head_dim = self.d_k // num_heads
        self.scale = head_dim ** -0.5

        # Separate embeddings for query, key, value
        self.query_embedding = nn.Conv2d(dim, dim, kernel_size=1, padding=0)
        self.key_embedding = nn.Conv2d(dim, dim, kernel_size=1, padding=0)
        self.value_embedding = nn.Conv2d(dim, dim, kernel_size=1, padding=0)

        # Output projection
        self.output_linear = nn.Sequential(
            nn.Conv2d(dim, dim, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2, inplace=True)
        )

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj_drop = nn.Dropout(proj_drop)

    def create_attention_mask(self, mask, patch_h, patch_w, b, t):
        """Create attention mask for missing regions following STTN approach"""
        if mask is None:
            return None

        # mask: (b*t, 1, H, W) or (b, 1, H, W) - 1 for missing, 0 for valid
        bt, c, h, w = mask.shape
        out_h, out_w = h // patch_h, w // patch_w

        if bt == b:
            # mask is (b, 1, H, W), expand to (b*t, 1, H, W)
            mask = mask.unsqueeze(1).expand(b, t, c, h, w).reshape(b*t, c, h, w)
            bt = b * t

        # Check if dimensions are valid
        if out_h == 0 or out_w == 0:
            return None

        # STTN-style mask processing: reshape mask to patch level directly
        # Following STTN's approach: m.view(b, t, 1, out_h, height, out_w, width)
        try:
            # Reshape mask: (b*t, 1, h, w) -> (b, t, 1, out_h, patch_h, out_w, patch_w)
            mask_reshaped = mask.view(b, t, c, out_h, patch_h, out_w, patch_w)

            # STTN permutation: (0, 1, 3, 5, 2, 4, 6) -> (b, t, out_h, out_w, 1, patch_h, patch_w)
            mask_patches = mask_reshaped.permute(0, 1, 3, 5, 2, 4, 6).contiguous()

            # Flatten to patch level: (b, t*out_h*out_w, patch_h*patch_w)
            mask_patches = mask_patches.view(b, t * out_h * out_w, patch_h * patch_w)

        except RuntimeError:
            logger.warning(f"Mask reshaping failed: {mask.shape} -> {out_h, out_w, patch_h, patch_w}")
            # If direct reshaping fails, fall back to interpolation
            expected_h = out_h * patch_h
            expected_w = out_w * patch_w

            if h != expected_h or w != expected_w:
                mask = F.interpolate(mask.float(), size=(expected_h, expected_w), mode='nearest')

            # Retry with interpolated mask
            mask_reshaped = mask.view(b, t, c, out_h, patch_h, out_w, patch_w)
            mask_patches = mask_reshaped.permute(0, 1, 3, 5, 2, 4, 6).contiguous()
            mask_patches = mask_patches.view(b, t * out_h * out_w, patch_h * patch_w)

        # STTN-style patch missing detection: mean > 0.5
        patch_missing = (mask_patches.mean(-1) > 0.5)  # (b, t*out_h*out_w)

        # STTN attention mask logic: All queries can only attend to valid (non-missing) keys
        # This prevents missing information propagation while allowing missing regions to learn from valid ones
        #
        # attn_mask[i, j] = True  -> query i CAN attend to key j
        # attn_mask[i, j] = False -> query i CANNOT attend to key j (will be masked to -inf)

        # Create mask: valid keys can be attended to by any query
        key_valid = ~patch_missing.unsqueeze(1)  # (b, 1, t*out_h*out_w) - True for valid keys
        attn_mask = key_valid.repeat(1, patch_missing.size(1), 1)  # (b, t*out_h*out_w, t*out_h*out_w)

        return attn_mask

    def forward(self, x, mask=None, b=None, t=None):
        """
        Args:
            x: (b*t, C, H, W) input features
            mask: (b*t, 1, H, W) attention mask (1 for missing, 0 for valid)
            b: batch size
            t: temporal dimension
        """
        bt, _, h, w = x.size()
        if b is None or t is None:
            # Assume single frame if not specified
            b, t = bt, 1

        output = []

        # Generate Q, K, V
        _query = self.query_embedding(x)  # (bt, C, H, W)
        _key = self.key_embedding(x)
        _value = self.value_embedding(x)

        # Split into multiple scales
        query_scales = torch.chunk(_query, self.num_scales, dim=1)
        key_scales = torch.chunk(_key, self.num_scales, dim=1)
        value_scales = torch.chunk(_value, self.num_scales, dim=1)

        for patch_size, query, key, value in zip(self.patch_sizes, query_scales, key_scales, value_scales):
            # Calculate output dimensions
            out_h, out_w = h // patch_size, w // patch_size

            # Skip this scale if dimensions are too small
            if out_h == 0 or out_w == 0:
                continue

            # Reshape to patches: (b, t, d_k, out_h, patch_size, out_w, patch_size)
            query = query.view(b, t, self.d_k, out_h, patch_size, out_w, patch_size)
            query = query.permute(0, 1, 3, 5, 2, 4, 6).contiguous()
            query = query.view(b, t * out_h * out_w, self.d_k * patch_size * patch_size)

            key = key.view(b, t, self.d_k, out_h, patch_size, out_w, patch_size)
            key = key.permute(0, 1, 3, 5, 2, 4, 6).contiguous()
            key = key.view(b, t * out_h * out_w, self.d_k * patch_size * patch_size)

            value = value.view(b, t, self.d_k, out_h, patch_size, out_w, patch_size)
            value = value.permute(0, 1, 3, 5, 2, 4, 6).contiguous()
            value = value.view(b, t * out_h * out_w, self.d_k * patch_size * patch_size)

            # Compute attention
            scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(query.size(-1))

            # Apply attention mask if provided
            if mask is not None:
                attn_mask = self.create_attention_mask(mask, patch_size, patch_size, out_h, out_w, b, t)
                if attn_mask is not None:
                    scores = scores.masked_fill(~attn_mask, -1e9)

            p_attn = F.softmax(scores, dim=-1)
            p_attn = self.attn_drop(p_attn)

            # Apply attention
            y = torch.matmul(p_attn, value)  # (b, t*out_h*out_w, d_k*patch_size*patch_size)

            # Reshape back to spatial format
            y = y.view(b, t, out_h, out_w, self.d_k, patch_size, patch_size)
            y = y.permute(0, 1, 4, 2, 5, 3, 6).contiguous()
            y = y.view(bt, self.d_k, h, w)

            output.append(y)

        # Concatenate multi-scale outputs
        output = torch.cat(output, 1)  # (bt, C, H, W)
        x = self.output_linear(output)
        x = self.proj_drop(x)

        return x


# ============================================================================
# Spatiotemporal Window Attention (Center as Query, Others as Key/Value)
# ============================================================================

class SpatiotemporalWindowAttention(nn.Module):
    """Window attention with center frame as Query, other frames as Key/Value"""

    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        # Separate projections for Query (center) and Key/Value (context)
        self.q_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv_proj = nn.Linear(dim, dim * 2, bias=qkv_bias)

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        # Relative position bias
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )

        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
    
    def forward(self, center_x, context_x, mask=None):
        """
        Args:
            center_x: (B_, N, C) center frame windows
            context_x: (B_, T_c*N, C) context frame windows
            mask: attention mask
        """
        B_, N, C = center_x.shape
        T_c = context_x.shape[1] // N
        
        # Query from center frame
        q = self.q_proj(center_x).reshape(B_, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)
        
        # Key/Value from context frames
        kv = self.kv_proj(context_x).reshape(B_, T_c * N, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]  # Each: (B_, heads, T_c*N, head_dim)
        
        # Attention computation
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))  # (B_, heads, N, T_c*N)
        
        # Add relative position bias (only for spatial dimensions within windows)
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        
        # For spatiotemporal attention, we tile the bias across temporal dimension
        if N == self.window_size * self.window_size:
            bias_expanded = relative_position_bias.unsqueeze(0).repeat(B_, 1, 1, T_c).view(B_, self.num_heads, N, T_c * N)
            attn = attn + bias_expanded
        
        if mask is not None:
            nW = mask.shape[0]
            # For spatiotemporal attention, we need to handle the mask differently
            # The mask is designed for spatial attention (N x N), but we have (N x T_c*N)
            # We expand the mask to cover the temporal dimension
            if T_c > 1:
                # Expand mask to temporal dimension: (nW, N, N) -> (nW, N, T_c*N)
                expanded_mask = mask.unsqueeze(-1).repeat(1, 1, 1, T_c).view(nW, N, T_c * N)
                attn = attn.view(B_ // nW, nW, self.num_heads, N, T_c * N) + expanded_mask.unsqueeze(1).unsqueeze(0)
            else:
                # Standard case for spatial attention
                attn = attn.view(B_ // nW, nW, self.num_heads, N, T_c * N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, T_c * N)
        
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


class SwinTransformerBlock(nn.Module):
    """Swin Transformer Block with spatiotemporal attention"""
    
    def __init__(self, dim, num_heads, window_size=8, shift_size=0, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm,
                 use_spatiotemporal=False, use_multiscale=False, patch_sizes=[4, 8, 16, 32]):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio
        self.use_spatiotemporal = use_spatiotemporal
        self.use_multiscale = use_multiscale

        self.norm1 = norm_layer(dim)

        if use_multiscale:
            # Multi-scale patch attention (inspired by STTN)
            self.attn = MultiScalePatchAttention(
                dim, patch_sizes=patch_sizes, num_heads=num_heads,
                attn_drop=attn_drop, proj_drop=drop)
        elif use_spatiotemporal:
            self.attn = SpatiotemporalWindowAttention(
                dim, window_size=window_size, num_heads=num_heads,
                qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        else:
            # Standard window attention from v10
            self.attn = WindowAttention(
                dim, window_size=window_size, num_heads=num_heads,
                qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        
        self.drop_path = nn.Identity() if drop_path <= 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )
    
    def forward(self, x, H, W, context_x=None, mask=None):
        """
        Args:
            x: (B, L, C) input features (center frame for spatiotemporal)
            H, W: spatial dimensions
            context_x: (B, T_c, L, C) context features for spatiotemporal attention
            mask: (B, 1, H, W) attention mask for multi-scale attention
        """
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"

        shortcut = x
        x = self.norm1(x)

        if self.use_multiscale:
            # Multi-scale patch attention (inspired by STTN)
            x = x.reshape(B, H, W, C).permute(0, 3, 1, 2)  # (B, C, H, W)

            # For multi-scale attention, we process each frame independently
            # The temporal dimension is handled differently than in STTN
            if context_x is not None:
                # Process center frame with multi-scale attention
                x = self.attn(x, mask=mask, b=B, t=1)

                # Note: In this implementation, we don't combine temporal frames
                # for multi-scale attention. This is different from STTN's approach
                # where temporal frames are processed together.
            else:
                # Single frame multi-scale attention
                x = self.attn(x, mask=mask, b=B, t=1)

            x = x.permute(0, 2, 3, 1).reshape(B, H * W, C)  # Back to (B, L, C)

        else:
            # Standard window-based attention
            x = x.reshape(B, H, W, C)

            # Create attention mask for shifted windows
            attn_mask = None
            if self.shift_size > 0:
                attn_mask = create_shifted_window_mask(H, W, self.window_size, self.shift_size)
                if attn_mask is not None:
                    attn_mask = attn_mask.to(x.device)

            # Cyclic shift
            if self.shift_size > 0:
                shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
            else:
                shifted_x = x

            # Window partition
            x_windows = window_partition(shifted_x, self.window_size)
            x_windows = x_windows.reshape(-1, self.window_size * self.window_size, C)

            if self.use_spatiotemporal and context_x is not None:
                # Process context frames
                B, T_c, L, C = context_x.shape
                context_x_norm = self.norm1(context_x.reshape(B * T_c, L, C)).reshape(B, T_c, L, C)

                # VECTORIZED: Process all context frames simultaneously
                # Reshape all context frames to spatial format: (B*T_c, H, W, C)
                context_frames = context_x_norm.reshape(B * T_c, H, W, C)

                # Apply cyclic shift to all frames at once if needed
                if self.shift_size > 0:
                    context_frames = torch.roll(context_frames, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))

                # Window partition for all context frames simultaneously
                context_windows = window_partition(context_frames, self.window_size)  # (B*T_c*nW, window_size, window_size, C)
                context_windows = context_windows.reshape(-1, self.window_size * self.window_size, C)  # (B*T_c*nW, window_size^2, C)

                # Reshape to group by spatial windows: (B*nW, T_c, window_size^2, C)
                nW = context_windows.shape[0] // (B * T_c)
                context_windows = context_windows.view(B, T_c, nW, self.window_size * self.window_size, C)
                context_windows = context_windows.permute(0, 2, 1, 3, 4).contiguous()  # (B, nW, T_c, window_size^2, C)
                context_windows = context_windows.view(B * nW, T_c * self.window_size * self.window_size, C)

                # Spatiotemporal attention
                attn_windows = self.attn(x_windows, context_windows, mask=attn_mask)
            else:
                # Standard window attention
                attn_windows = self.attn(x_windows, mask=attn_mask)

            # Merge windows
            attn_windows = attn_windows.reshape(-1, self.window_size, self.window_size, C)
            shifted_x = window_reverse(attn_windows, self.window_size, H, W)

            # Reverse cyclic shift
            if self.shift_size > 0:
                x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
            else:
                x = shifted_x

            x = x.reshape(B, H * W, C)

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class WindowAttention(nn.Module):
    """Standard window-based multi-head self attention (from v10)"""
    
    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
        
        # Relative position bias
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )
        
        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)
        
        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
    
    def forward(self, x, mask=None):
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)
        
        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = F.softmax(attn, dim=-1)
        else:
            attn = F.softmax(attn, dim=-1)
        
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


# ============================================================================
# Patch Merging Layer
# ============================================================================

class PatchMerging(nn.Module):
    """Patch merging layer"""
    
    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)
    
    def forward(self, x):
        """
        Args:
            x: (B, H*W, C)
        Returns:
            x: (B, (H/2)*(W/2), 2*C)
        """
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        assert H % 2 == 0 and W % 2 == 0, f"x size ({H}*{W}) are not even."
        
        x = x.view(B, H, W, C)
        
        # Extract 4 sub-patches
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C
        
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C
        
        x = self.norm(x)
        x = self.reduction(x)  # B H/2*W/2 2*C
        
        return x


# ============================================================================
# Swin Encoder with Spatiotemporal Processing
# ============================================================================

class SwinEncoderv16(nn.Module):
    """Swin encoder with [2, 2, 6, 2] structure and spatiotemporal processing"""
    
    def __init__(self, embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24],
                 window_size=8, mlp_ratio=4., drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 use_spatiotemporal_in_first_stage=True, use_multiscale_in_first_stage=False,
                 patch_sizes=[4, 8, 16, 32]):
        super().__init__()
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_layers = len(depths)
        self.use_spatiotemporal_in_first_stage = use_spatiotemporal_in_first_stage
        self.use_multiscale_in_first_stage = use_multiscale_in_first_stage

        # Build layers
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        self.layers = nn.ModuleList()
        self.downsample_layers = nn.ModuleList()

        # Initial spatial resolution (depends on patch embedding output)
        # Will be set dynamically during forward pass

        for i_layer in range(self.num_layers):
            # Determine attention type for this stage
            use_spatiotemporal = use_spatiotemporal_in_first_stage and (i_layer == 0) and not use_multiscale_in_first_stage
            use_multiscale = use_multiscale_in_first_stage and (i_layer == 0)

            layer_blocks = nn.ModuleList()
            for i in range(depths[i_layer]):
                layer_blocks.append(
                    SwinTransformerBlock(
                        dim=int(embed_dim * 2 ** i_layer),
                        num_heads=num_heads[i_layer],
                        window_size=window_size,
                        shift_size=0 if (i % 2 == 0) else window_size // 2,
                        mlp_ratio=mlp_ratio,
                        drop=drop_rate,
                        attn_drop=attn_drop_rate,
                        drop_path=dpr[sum(depths[:i_layer]) + i],
                        use_spatiotemporal=use_spatiotemporal,
                        use_multiscale=use_multiscale,
                        patch_sizes=patch_sizes
                    )
                )
            self.layers.append(layer_blocks)
            
            # Downsample layer
            if i_layer < self.num_layers - 1:
                # We'll set input_resolution dynamically
                downsample = PatchMerging(
                    input_resolution=None,  # Will be set during forward
                    dim=int(embed_dim * 2 ** i_layer),
                    norm_layer=nn.LayerNorm
                )
                self.downsample_layers.append(downsample)
            else:
                self.downsample_layers.append(None)
    
    def forward(self, center_features, context_features=None, mask=None):
        """
        Args:
            center_features: (B, N, D) center frame features
            context_features: (B, T_c, N, D) context frame features (optional)
            mask: (B, 1, H, W) attention mask for multi-scale attention (optional)
        Returns:
            features: List[(B, C, H, W)] multi-scale features
        """
        features = []
        B, N, _ = center_features.shape
        H = W = int(math.sqrt(N))
        assert H * W == N, f"Feature map must be square, got {N} patches"

        x = center_features

        for i_layer, layer_blocks in enumerate(self.layers):
            # Apply transformer blocks
            for block in layer_blocks:
                if block.use_multiscale:
                    # Use multi-scale patch attention
                    x = block(x, H, W, context_features, mask)
                elif block.use_spatiotemporal and context_features is not None:
                    # Use spatiotemporal attention
                    x = block(x, H, W, context_features)
                else:
                    # Standard self-attention
                    x = block(x, H, W)
            
            # Convert to conv format and save features
            x_conv = x.reshape(B, H, W, -1).permute(0, 3, 1, 2)  # (B, C, H, W)
            features.append(x_conv)
            
            # Downsample
            if i_layer < self.num_layers - 1:
                downsample = self.downsample_layers[i_layer]
                if downsample is not None:
                    # Set input resolution dynamically
                    downsample.input_resolution = (H, W)
                    x = downsample(x)
                    H, W = H // 2, W // 2
                    
                    # Update context features resolution if needed
                    if context_features is not None and i_layer == 0:
                        # After first stage, we don't use spatiotemporal attention anymore
                        context_features = None
        
        return features


# ============================================================================
# Main Model: SwinWaterNetV16
# ============================================================================

class SwinWaterNetv16(nn.Module):
    """
    Swin Water Net v16: Simplified Architecture with Configurable Patch Size

    Key features:
    1. Center frame extraction using center_frame_idx
    2. Configurable patch size (default 8x8) for flexible processing
    3. Center frame as Query, other frames as Key/Value
    4. Swin Transformer with [2, 2, 6, 2] blocks
    5. U-Net style decoder
    """
    
    def __init__(self, img_size=256, patch_size=8, in_chans=2, out_chans=2,
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], window_size=8,
                 num_frames=48, mlp_ratio=4.0, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 use_multiscale=False, multiscale_patch_sizes=[4, 8, 16, 32]):
        super().__init__()

        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.num_frames = num_frames
        self.use_multiscale = use_multiscale

        # Patch embedding with configurable patch_size
        self.patch_embed = DifferentiablePatchEmbed(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, embed_dim=embed_dim
        )

        # Swin encoder with spatiotemporal or multi-scale processing
        self.encoder = SwinEncoderv16(
            embed_dim=embed_dim, depths=depths, num_heads=num_heads,
            window_size=window_size, mlp_ratio=mlp_ratio,
            drop_rate=drop_rate, attn_drop_rate=attn_drop_rate, drop_path_rate=drop_path_rate,
            use_spatiotemporal_in_first_stage=not use_multiscale,
            use_multiscale_in_first_stage=use_multiscale,
            patch_sizes=multiscale_patch_sizes
        )
        
        # U-Net style decoder
        final_dim = embed_dim * (2 ** (len(depths) - 1))
        self.decoder = SwinUNetDecoder(
            in_channels=final_dim, img_size=img_size, patch_size=patch_size,
            num_classes=out_chans, use_checkpoint=False
        )
        
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward(self, batch):
        """
        Args:
            batch: dict containing:
                - 'input_sequence': (B, T, C, H, W) video sequence
                - 'center_frame_idx': (B,) center frame indices
                - 'occurrence': (B, H, W) water frequency (optional)
        Returns:
            dict with 'inpaint' containing 'logits'
        """
        video = batch['input_sequence']  # (B, T, C, H, W)
        center_frame_idx = batch['center_frame_idx']  # (B,)
        water_frequency = batch.get('occurrence')  # (B, H, W) optional

        B, T, _, _, _ = video.shape
        device = video.device

        # Ensure center_frame_idx is within valid range
        center_frame_idx = torch.clamp(center_frame_idx, 0, T - 1)
                
        # Extract center frame and context frames
        batch_indices = torch.arange(B, device=device)
        center_frames = video[batch_indices, center_frame_idx].unsqueeze(1)  # (B, 1, C, H, W)
        
        # Create mask to exclude center frame
        all_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, T)
        center_indices_expanded = center_frame_idx.unsqueeze(1)
        context_mask = all_indices != center_indices_expanded  # (B, T)
        
        # VECTORIZED: Extract context frames using advanced indexing
        # Create indices for context frames (excluding center frame)
        context_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, T)  # (B, T)
        context_indices = context_indices[context_mask]  # Flatten and get context indices

        # Reshape to (B, T-1) for proper indexing
        context_indices = context_indices.view(B, T-1)

        # Use advanced indexing to extract context frames efficiently
        batch_indices = torch.arange(B, device=device).unsqueeze(1).expand(B, T-1)  # (B, T-1)
        context_frames = video[batch_indices, context_indices]  # (B, T-1, C, H, W)

        # Apply simplified patch embedding with fixed patch_size=8
        center_patches, _ = self.patch_embed(center_frames)
        center_patches = center_patches.squeeze(1)  # (B, N, D)

        context_patches, _ = self.patch_embed(context_frames)  # (B, T-1, N, D)

        # Create attention mask for multi-scale attention if enabled
        attention_mask = None
        if self.use_multiscale:
            # Extract mask from the second channel of center frame
            # video: (B, T, 2, H, W) where channel 1 is the missing mask
            center_mask = center_frames[:, 0, 1:2, :, :]  # (B, 1, H, W) - extract channel 1
            attention_mask = center_mask  # 1 indicates missing regions

        # Encode with spatiotemporal or multi-scale attention
        encoder_features = self.encoder(center_patches, context_patches, attention_mask)

        # Decode
        logits = self.decoder(encoder_features[::-1])

        # Simplified output without patch size weights
        output = {
            'inpaint': {'logits': logits}
        }
        
        return output


def create_swin_water_net_v16(config):
    """Create SwinWaterNetv16 model from config"""
    model_config = config.model.swin_config
    
    def safe_get(obj, key, default):
        if hasattr(obj, 'get'):
            return obj.get(key, default)
        elif hasattr(obj, key):
            return getattr(obj, key)
        else:
            return default
    
    model = SwinWaterNetv16(
        img_size=safe_get(model_config, 'img_size', 256),
        patch_size=safe_get(model_config, 'patch_size', 8),
        in_chans=safe_get(model_config, 'in_chans', 2),
        out_chans=safe_get(model_config, 'out_chans', 2),
        embed_dim=safe_get(model_config, 'embed_dim', 96),
        depths=safe_get(model_config, 'depths', [2, 2, 6, 2]),
        num_heads=safe_get(model_config, 'num_heads', [3, 6, 12, 24]),
        window_size=safe_get(model_config, 'window_size', 8),
        num_frames=safe_get(config.data, 'num_frames', 48),
        mlp_ratio=safe_get(model_config, 'mlp_ratio', 4.0),
        drop_rate=safe_get(model_config, 'drop_rate', 0.),
        attn_drop_rate=safe_get(model_config, 'attn_drop_rate', 0.),
        drop_path_rate=safe_get(model_config, 'drop_path_rate', 0.1)
    )
    
    return model


# For compatibility
def create_swin_water_net(config):
    """Compatibility alias"""
    return create_swin_water_net_v16(config)


__all__ = [
    'SwinWaterNetv16',
    'create_swin_water_net_v16',
    'create_swin_water_net',
    'DifferentiablePatchEmbed',
    'SpatiotemporalWindowAttention',
    'SwinTransformerBlock',
    'SwinEncoderv16'
]


# ============================================================================
# Dimension Flow Demonstration
# ============================================================================

def demonstrate_model_dimension_flow():
    """
    演示 SwinWaterNetV16 模型的维度流动
    通过模拟数据展示每个关键步骤的张量维度变化
    """
    import torch

    print("🌊" * 50)
    print("SwinWaterNetV16 维度流动演示")
    print("🌊" * 50)

    # 设置随机种子以获得可重复的结果
    torch.manual_seed(42)

    # 模型配置
    config = {
        'img_size': 256,
        'patch_size': 8,
        'in_chans': 2,
        'out_chans': 2,
        'embed_dim': 96,
        'depths': [2, 2, 6, 2],
        'num_heads': [3, 6, 12, 24],
        'window_size': 8,
        'num_frames': 48
    }

    # 创建模型
    print("\n📋 模型配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    model = SwinWaterNetv16(**config)
    model.eval()

    # 模拟输入数据
    B, T, C, H, W = 2, 48, 2, 256, 256
    print(f"\n📥 输入数据维度:")
    print(f"  Batch Size: {B}")
    print(f"  Time Steps: {T}")
    print(f"  Channels: {C}")
    print(f"  Height: {H}")
    print(f"  Width: {W}")

    # 创建模拟数据
    video = torch.randn(B, T, C, H, W)
    water_frequency = torch.rand(B, H, W) * 0.8 + 0.1  # 0.1-0.9 范围

    # batch 数据准备完成

    print(f"\n🎯 模拟数据:")
    print(f"  Video Shape: {video.shape}")
    print(f"  Water Frequency Shape: {water_frequency.shape}")
    print(f"  Water Frequency Range: [{water_frequency.min():.3f}, {water_frequency.max():.3f}]")

    # 开始维度流动演示
    print("\n" + "="*80)
    print("🔄 维度流动演示开始")
    print("="*80)

    with torch.no_grad():
        # Step 1: 中心帧提取
        print("\n📍 Step 1: 中心帧提取")
        center_frame_idx = torch.full((B,), T // 2, dtype=torch.long)  # 模拟数据使用 T//2
        center_frame_idx = torch.clamp(center_frame_idx, 0, T - 1)  # 确保在有效范围内
        batch_indices = torch.arange(B)
        center_frames = video[batch_indices, center_frame_idx].unsqueeze(1)

        # 创建上下文帧掩码
        all_indices = torch.arange(T).unsqueeze(0).expand(B, T)
        center_indices_expanded = center_frame_idx.unsqueeze(1)
        context_mask = all_indices != center_indices_expanded

        # 提取上下文帧
        context_frames = []
        for b in range(B):
            ctx_frames = video[b, context_mask[b]]
            context_frames.append(ctx_frames)
        context_frames = torch.stack(context_frames, dim=0)

        print(f"  中心帧索引: {center_frame_idx.tolist()}")
        print(f"  中心帧维度: {center_frames.shape}")
        print(f"  上下文帧维度: {context_frames.shape}")

        # Step 2: 水体频率处理
        print("\n📍 Step 2: 水体频率处理")
        mean_water_freq = water_frequency.view(B, -1).mean(dim=1)
        print(f"  平均水体频率: {mean_water_freq}")
        print(f"  平均水体频率维度: {mean_water_freq.shape}")

        # Step 3: 可微分补丁嵌入
        print("\n📍 Step 3: 可微分补丁嵌入")
        print("  🔧 处理中心帧...")

        # 手动调用补丁嵌入以展示内部过程
        patch_embed = model.patch_embed

        # 使用固定的patch_size=8
        print(f"  使用固定补丁大小: 8x8")

        # 处理中心帧
        print("  📦 固定补丁大小的特征提取:")
        frame = center_frames[:, 0]  # (B, C, H, W)
        patches = patch_embed.patch_embed(frame)  # (B, D, H', W')

        patches = patches.flatten(2).transpose(1, 2)  # (B, N, D)
        patches = patches + patch_embed.position_embed[:, :patches.size(1), :]
        patches = patch_embed.norm(patches)

        print(f"    补丁大小 8x8: {patches.shape}")
        center_patches = patches
        print(f"  最终中心帧补丁维度: {center_patches.shape}")

        # 处理上下文帧
        print("  🔧 处理上下文帧...")
        context_patches_list = []
        for t in range(context_frames.shape[1]):
            frame = context_frames[:, t]
            patches = patch_embed.patch_embed(frame)
            patches = patches.flatten(2).transpose(1, 2)
            patches = patches + patch_embed.position_embed[:, :patches.size(1), :]
            patches = patch_embed.norm(patches)
            context_patches_list.append(patches.unsqueeze(1))

        context_patches = torch.cat(context_patches_list, dim=1)
        print(f"  上下文帧补丁维度: {context_patches.shape}")

        # Step 4: Swin 编码器处理
        print("\n📍 Step 4: Swin 编码器处理")
        encoder = model.encoder

        features = []
        B, N, _ = center_patches.shape
        H = W = int(math.sqrt(N))
        print(f"  初始空间分辨率: {H}x{W}")

        x = center_patches

        for i_layer, layer_blocks in enumerate(encoder.layers):
            print(f"\n  🏗️ 编码器阶段 {i_layer + 1}:")
            print(f"    输入维度: {x.shape}")
            print(f"    空间分辨率: {H}x{W}")
            print(f"    特征维度: {x.shape[-1]}")

            # 应用 Transformer 块
            for j, block in enumerate(layer_blocks):
                if block.use_spatiotemporal and context_patches is not None and i_layer == 0:
                    print(f"    🔄 块 {j+1}: 使用时空注意力")
                    x = block(x, H, W, context_patches)
                else:
                    print(f"    🔄 块 {j+1}: 使用标准注意力")
                    x = block(x, H, W)

            # 转换为卷积格式并保存特征
            x_conv = x.reshape(B, H, W, -1).permute(0, 3, 1, 2)
            features.append(x_conv)
            print(f"    输出特征维度: {x_conv.shape}")

            # 下采样
            if i_layer < encoder.num_layers - 1:
                downsample = encoder.downsample_layers[i_layer]
                if downsample is not None:
                    downsample.input_resolution = (H, W)
                    x = downsample(x)
                    H, W = H // 2, W // 2
                    print(f"    下采样后维度: {x.shape}")
                    print(f"    新空间分辨率: {H}x{W}")

                    # 第一阶段后停止使用时空注意力
                    if context_patches is not None and i_layer == 0:
                        context_patches = None
                        print(f"    ⚠️ 停止使用时空注意力")

        # Step 5: 解码器处理
        print("\n📍 Step 5: 解码器处理")
        decoder = model.decoder

        # 反转特征列表 (从高级到低级)
        reversed_features = features[::-1]
        print(f"  解码器输入特征数量: {len(reversed_features)}")

        for i, feat in enumerate(reversed_features):
            print(f"    特征 {i+1}: {feat.shape}")

        # 调用解码器
        logits = decoder(reversed_features)
        print(f"  解码器输出维度: {logits.shape}")

        # Step 6: 最终输出
        print("\n📍 Step 6: 最终输出")
        output = {
            'inpaint': {'logits': logits}
        }

        print(f"  最终 logits 维度: {output['inpaint']['logits'].shape}")

        # 输出统计信息
        print("\n📊 输出统计:")
        logits_stats = {
            'min': logits.min().item(),
            'max': logits.max().item(),
            'mean': logits.mean().item(),
            'std': logits.std().item()
        }

        for key, value in logits_stats.items():
            print(f"  Logits {key}: {value:.4f}")

        print(f"  使用固定补丁大小: 8x8")

    print("\n" + "="*80)
    print("✅ 维度流动演示完成")
    print("="*80)

    return output


def print_tensor_info(name, tensor, show_stats=True):
    """打印张量信息的辅助函数"""
    print(f"📊 {name}:")
    print(f"  Shape: {tensor.shape}")
    print(f"  Device: {tensor.device}")
    print(f"  Dtype: {tensor.dtype}")

    if show_stats and tensor.numel() > 0:
        print(f"  Min: {tensor.min().item():.4f}")
        print(f"  Max: {tensor.max().item():.4f}")
        print(f"  Mean: {tensor.mean().item():.4f}")
        print(f"  Std: {tensor.std().item():.4f}")


if __name__ == "__main__":
    # 运行维度流动演示
    demonstrate_model_dimension_flow()