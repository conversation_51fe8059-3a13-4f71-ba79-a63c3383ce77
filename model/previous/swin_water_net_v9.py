#!/usr/bin/env python3
"""
Model v8 目标帧引导注意力集成
将验证有效的目标帧引导注意力机制集成到现有的Model v8中
"""

import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import logging
import math
from typing import Dict, Tuple, Optional, List

# 导入动态程度计算函数
from model.loss import compute_dynamic_degree

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 直接在此文件中定义必要的类，避免导入问题

class TargetGuidedAttention(nn.Module):
    """目标帧引导注意力机制 - 对整个时序进行增强，维度形状不变"""

    def __init__(self, embed_dim, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim

        # 目标帧特征投影
        self.target_query_proj = nn.Linear(embed_dim, embed_dim)

        # 时序特征投影
        self.temporal_key_proj = nn.Linear(embed_dim, embed_dim)
        self.temporal_value_proj = nn.Linear(embed_dim, embed_dim)

        self.attention_scale = embed_dim ** -0.5

        # 输出投影
        self.output_proj = nn.Linear(embed_dim, embed_dim)

        # Dropout和归一化
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(embed_dim)

        # 数值稳定性参数 - 使用自适应策略
        self.eps = 1e-8

    def forward(self, temporal_features, target_position, missing_mask=None, water_frequency=None):
        """
        以目标帧为索引，对整个输入时序进行注意力增强

        Args:
            temporal_features: (B, T, N, D) - 输入时序特征
            target_position: (B,) - 目标帧在时序中的位置索引
            missing_mask: (B, N) - 缺失掩码 (True表示缺失)
            water_frequency: (B, H, W) - 水体频率图，用于计算动态程度

        Returns:
            enhanced_temporal_features: (B, T, N, D) - 增强后的时序特征（维度不变）
            attention_weights: (B, T, N) - 注意力权重
        """
        B, T, N, D = temporal_features.shape
        device = temporal_features.device

        # 1. 提取目标帧特征作为Query
        batch_indices = torch.arange(B, device=device)
        target_features = temporal_features[batch_indices, target_position]  # (B, N, D)

        # 2. 投影目标帧和时序特征 - 添加数值稳定性检查
        target_query = self.target_query_proj(target_features)  # (B, N, D)
        temporal_keys = self.temporal_key_proj(temporal_features)  # (B, T, N, D)
        temporal_values = self.temporal_value_proj(temporal_features)  # (B, T, N, D)

        # 检查投影后的数值稳定性
        for tensor, name in [(target_query, 'target_query'), (temporal_keys, 'temporal_keys'), (temporal_values, 'temporal_values')]:
            if torch.isnan(tensor).any() or torch.isinf(tensor).any():
                logger.warning(f"NaN/Inf detected in {name}, applying correction")
                tensor = torch.nan_to_num(tensor, nan=0.0, posinf=1.0, neginf=-1.0)
                tensor = torch.clamp(tensor, min=-5.0, max=5.0)

        # 3. 计算注意力权重：目标帧查询所有时序帧
        # 扩展目标帧Query以匹配时序维度
        target_query_expanded = target_query.unsqueeze(1).expand(-1, T, -1, -1)  # (B, T, N, D)

        # 计算注意力分数 (scaled dot-product attention) - 添加数值稳定性
        attention_scores = torch.sum(
            target_query_expanded * temporal_keys, dim=-1
        ) * self.attention_scale  # (B, T, N)

        # 自适应注意力分数裁剪 - 基于输入特征的动态调整
        feature_norm = temporal_features.norm(dim=-1).max()
        adaptive_clip_value = feature_norm * 2.0  # 注意力分数用2倍，更保守

        # 检查注意力分数的数值稳定性
        if torch.isnan(attention_scores).any() or torch.isinf(attention_scores).any():
            logger.warning("NaN/Inf detected in attention_scores, applying correction")
            attention_scores = torch.nan_to_num(attention_scores, nan=0.0, posinf=adaptive_clip_value, neginf=-adaptive_clip_value)

        # 自适应裁剪注意力分数防止softmax溢出
        attention_scores = torch.clamp(attention_scores, min=-adaptive_clip_value, max=adaptive_clip_value)

        # 4. 缺失感知调整 - 在softmax后进行增强（修复平移不变性问题）
        # 原问题：在softmax前对所有时间步T统一加增强值，由于softmax平移不变性无效
        # 解决方案：在softmax后直接调整权重并重新归一化
        # if missing_mask is not None:
        #     if water_frequency is not None:
        #         # 计算动态程度 (B, H, W)
        #         dynamic_degree = compute_dynamic_degree(water_frequency, alpha=2.0, sigma=0.25, min_weight=0.01)

        #         # 将动态程度调整到patch维度 (B, N)
        #         B, H, W = water_frequency.shape
        #         N = missing_mask.shape[1]  # patch数量

        #         if H * W != N:
        #             # 需要调整动态程度的空间分辨率以匹配patch数量
        #             patch_size = int((H * W / N) ** 0.5)  # 估算patch大小
        #             if patch_size > 1:
        #                 # 下采样到patch分辨率
        #                 dynamic_degree_resized = F.avg_pool2d(
        #                     dynamic_degree.unsqueeze(1),
        #                     kernel_size=patch_size,
        #                     stride=patch_size
        #                 ).squeeze(1)  # (B, H_p, W_p)
        #                 dynamic_degree_flat = dynamic_degree_resized.flatten(1)  # (B, N)
        #             else:
        #                 # 上采样到patch分辨率
        #                 target_size = int(N ** 0.5)
        #                 dynamic_degree_resized = F.interpolate(
        #                     dynamic_degree.unsqueeze(1),
        #                     size=(target_size, target_size),
        #                     mode='bilinear',
        #                     align_corners=False
        #                 ).squeeze(1)  # (B, H_p, W_p)
        #                 dynamic_degree_flat = dynamic_degree_resized.flatten(1)  # (B, N)
        #         else:
        #             dynamic_degree_flat = dynamic_degree.flatten(1)  # (B, N)

        #         # 计算增强因子：1 + 动态程度 * 缺失掩码
        #         enhancement_factor = 1.0 + 0.1 * dynamic_degree_flat * missing_mask.float()  # (B, N)
        #     else:
        #         # 如果没有水体频率信息，使用固定增强因子
        #         enhancement_factor = 1.0 + 0.1 * missing_mask.float()  # (B, N)

        # 5. 应用softmax得到注意力权重 - 添加数值稳定性
        attention_weights = F.softmax(attention_scores, dim=1)  # (B, T, N)

        # 检查softmax后的数值稳定性
        if torch.isnan(attention_weights).any() or torch.isinf(attention_weights).any():
            logger.warning("NaN/Inf detected in attention_weights, using uniform weights")
            attention_weights = torch.ones_like(attention_weights) / T

        # 确保注意力权重在合理范围内
        attention_weights = torch.clamp(attention_weights, min=self.eps, max=1.0)

        attention_weights = self.dropout(attention_weights)

        # 6. 向量化的注意力增强操作 - 添加数值稳定性
        # 计算全局加权上下文（所有帧共享）
        weighted_context = torch.sum(
            attention_weights.unsqueeze(-1) * temporal_values, dim=1
        )  # (B, N, D)

        # 检查加权上下文的数值稳定性
        if torch.isnan(weighted_context).any() or torch.isinf(weighted_context).any():
            logger.warning("NaN/Inf detected in weighted_context, applying correction")
            weighted_context = torch.nan_to_num(weighted_context, nan=0.0, posinf=1.0, neginf=-1.0)

        # 扩展加权上下文以匹配时序维度
        weighted_context_expanded = weighted_context.unsqueeze(1).expand(-1, T, -1, -1)  # (B, T, N, D)

        # 当前帧的注意力权重（向量化）
        current_attention = attention_weights.unsqueeze(-1)  # (B, T, N, 1)

        # 注意力加权的上下文表示（标准注意力机制）
        enhancement_term = current_attention * weighted_context_expanded

        # 自适应增强项裁剪 - 基于特征分布动态调整
        feature_std = temporal_features.std()
        adaptive_enhancement_clip = max(1.0, feature_std * 3.0)  # 至少1.0，增强项用3倍标准差，允许更大变化

        # 检查增强项的数值稳定性
        if torch.isnan(enhancement_term).any() or torch.isinf(enhancement_term).any():
            logger.warning("NaN/Inf detected in enhancement_term, applying correction")
            # 使用更合理的修正值
            enhancement_term = torch.nan_to_num(
                enhancement_term,
                nan=0.0,
                posinf=adaptive_enhancement_clip * 0.5,
                neginf=-adaptive_enhancement_clip * 0.5
            )

        # 自适应裁剪增强项防止梯度爆炸
        enhancement_term = torch.clamp(enhancement_term, min=-adaptive_enhancement_clip, max=adaptive_enhancement_clip)

        # 线性投影变换
        projected_features = self.output_proj(enhancement_term)

        # 检查投影后的特征 - 使用自适应修正策略
        if torch.isnan(projected_features).any() or torch.isinf(projected_features).any():
            logger.warning("NaN/Inf detected in projected_features, applying correction")
            # 基于原始特征分布的自适应修正
            feature_mean = temporal_features.mean()
            feature_std = temporal_features.std()
            projected_features = torch.nan_to_num(
                projected_features,
                nan=0.0,
                posinf=feature_mean + 3*feature_std,  # 更合理的上界
                neginf=feature_mean - 3*feature_std   # 更合理的下界
            )

        # 标准Transformer风格：LayerNorm(x + SubLayer(x))
        # 这里 x = temporal_features, SubLayer(x) = projected_features
        final_features = self.layer_norm(temporal_features + projected_features)

        # 最终检查
        if torch.isnan(final_features).any() or torch.isinf(final_features).any():
            logger.warning("NaN/Inf detected in final TargetGuidedAttention output, applying correction")
            final_features = torch.nan_to_num(final_features, nan=0.0, posinf=1.0, neginf=-1.0)

        return final_features, attention_weights

# 删除对比时序损失，使用原来的损失函数
from model.swin_water_net_v8 import create_swin_water_net as create_swin_water_net_original
from configs import get_config

class EnhancedSwinWaterNetV8(nn.Module):
    """
    增强版Swin Water Net v8
    集成目标帧引导注意力机制
    """

    def __init__(self, base_model, embed_dim=96):
        super().__init__()
        self.base_model = base_model

        # 添加目标帧引导注意力模块
        self.target_guided_attention = TargetGuidedAttention(embed_dim, dropout=0.1)

    def forward(self, batch):
        """增强的前向传播，集成目标帧引导注意力"""
        x = batch['input_sequence']
        center_frame_idx = batch.get('center_frame_idx', None)

        B, T = x.shape[:2]
        device = x.device

        # 标准化center_frame_idx
        if center_frame_idx is None:
            center_frame_idx = torch.full((B,), T // 2, device=device, dtype=torch.long)
        elif isinstance(center_frame_idx, (int, float)):
            center_frame_idx = torch.tensor([int(center_frame_idx)] * B, device=device, dtype=torch.long)
        elif center_frame_idx.numel() == 1 and B > 1:
            center_frame_idx = center_frame_idx.expand(B)

        # 确保索引在有效范围内
        center_frame_idx = torch.clamp(center_frame_idx, 0, T - 1)

        # 1. 直接使用原始输入进行Patch embedding
        x_embed = self.base_model.patch_embed(x)  # (B, T, N, D)

        # 2. 应用目标帧引导注意力到整个时序
        missing_mask = batch.get('missing_mask', None)
        if missing_mask is not None:
            # 将2D缺失掩码转换为1D patch掩码
            missing_mask_flat = missing_mask.flatten(1)  # (B, H*W)
            # 如果patch数量不匹配，需要调整
            N = x_embed.shape[2]  # patch数量
            if missing_mask_flat.shape[1] != N:
                # 简单的下采样或上采样
                missing_mask_resized = F.interpolate(
                    missing_mask.float().unsqueeze(1),
                    size=(int(N**0.5), int(N**0.5)),
                    mode='nearest'
                ).squeeze(1).flatten(1).bool()
                missing_mask_flat = missing_mask_resized
        else:
            missing_mask_flat = None

        # 获取水体频率信息
        water_frequency = batch.get('occurrence', None)

        # 对整个时序特征进行目标帧引导的注意力增强
        x_enhanced, attention_weights = self.target_guided_attention(
            x_embed, center_frame_idx, missing_mask_flat, water_frequency
        )

        # 3. 地理时间编码（使用原有逻辑）
        geo_temporal_encoding = self.base_model.geo_temporal_encoder(
            batch['tile_lon'], batch['tile_lat'], batch['year'], batch['month']
        )

        # 添加地理编码
        B_f, T_f, N_f, D_f = x_enhanced.shape
        geo_encoding_expanded = geo_temporal_encoding.unsqueeze(1).unsqueeze(1).expand(
            B_f, T_f, N_f, D_f
        )
        geo_encoding_expanded = geo_encoding_expanded.to(x_enhanced.dtype)
        x_enhanced = x_enhanced + 0.1 * geo_encoding_expanded

        # 4. 编码和解码（使用原有逻辑）
        water_frequency = batch.get('occurrence', None)
        encoder_features = self.base_model.encoder(x_enhanced)
        logits = self.base_model.decoder(encoder_features[::-1], water_frequency)

        outputs = {
            'inpaint': {
                'logits': logits,
            },
            'attention_weights': attention_weights,  # 用于分析和可视化
        }

        return outputs

# 删除增强的训练损失类，使用原来的损失函数

def create_swin_water_net(config):
    """创建增强版的Swin Water Net（保持原函数名）"""
    # 创建基础模型
    base_model = create_swin_water_net_original(config)

    # 获取嵌入维度
    embed_dim = config.model.swin_config.embed_dim

    # 创建增强模型
    enhanced_model = EnhancedSwinWaterNetV8(base_model, embed_dim)

    return enhanced_model

# 删除测试代码，保持文件简洁
