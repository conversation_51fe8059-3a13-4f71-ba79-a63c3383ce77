"""
Optimized Training Script for Inpainting-Only Mode
Designed for 4x NVIDIA L40 GPUs with geographic modulation

Features:
- Efficient distributed training across 4 GPUs
- Mixed precision training
- Optimized data loading
- Geographic and temporal context encoding
- Memory-efficient implementation
"""

import argparse
import logging
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.cuda.amp import GradScaler
from torch import autocast
from torch.utils.data import DataLoader, DistributedSampler
from torch.utils.tensorboard import SummaryWriter
import yaml
import numpy as np
import torch.multiprocessing as mp

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data.dataset import WaterBodyDataset
from model.previous.swin_water_net_v5 import create_swin_water_net
from model.loss import InpaintingLossWithWaterWeight
from configs import get_config
from evaluation.visualization import save_visualization

logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 也设置模型模块的logger为DEBUG级别
model_logger = logging.getLogger('__main__')  # 训练期间的主模块
model_logger.setLevel(logging.INFO)

# 如果模型在单独的模块中
swin_logger = logging.getLogger('model.swin_water_net')
swin_logger.setLevel(logging.INFO)


def count_parameters(model):
    """Count model parameters"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params


def format_param_count(count):
    """Format parameter count in human readable format"""
    if count >= 1e6:
        return f"{count/1e6:.1f}M"
    elif count >= 1e3:
        return f"{count/1e3:.1f}K"
    else:
        return str(count)


class InpaintingTrainer:
    """Optimized trainer for inpainting-only mode on 4x L40 GPUs"""
    
    def __init__(self, config, local_rank=0, world_size=1, index_file=None, missing_db=None):
        self.config = config
        self.local_rank = local_rank
        self.world_size = world_size
        self.device = torch.device(f'cuda:{local_rank}')
        
        # Store data paths directly
        self.index_file = index_file
        self.missing_db = missing_db
        
        # Initialize common attributes for all ranks
        logging_config = self.config.get('logging', {})
        
        # Get experiment name with fallback to timestamp
        if hasattr(logging_config, 'get'):
            self.experiment_name = logging_config.get('experiment_name', f"waternet_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        else:
            self.experiment_name = getattr(logging_config, 'experiment_name', f"waternet_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
        # Get checkpoint directory with fallback
        if hasattr(logging_config, 'get'):
            self.res_dir = Path(logging_config.get('res_dir', 'Results'))
        else:
            self.res_dir = Path(getattr(logging_config, 'res_dir', 'Results'))
            
        # Create directory paths for all ranks (actual creation only happens on rank 0)
        self.log_dir = self.res_dir / 'logs' / self.experiment_name
        self.checkpoint_dir = self.res_dir / 'checkpoints' / self.experiment_name
        self.metrics_dir = self.res_dir / 'metrics' / self.experiment_name
        self.vis_dir = self.res_dir / 'visualizations' / self.experiment_name
        self.val_index_path = self.res_dir / 'val_indices' / self.experiment_name / 'val_indices.json'

        if local_rank == 0:
            self.setup_logging()

        # Set up model
        self.model = self._setup_model()
        
        # Set up data loaders
        self.train_loader, self.val_loader = self._setup_data_loaders()
        
        # Setup loss function with combined BCE + DICE + Focal Loss
        self.loss_fn = InpaintingLossWithWaterWeight(
            bce_weight=config.training.loss_config.get('bce_weight', 0.3),
            dice_weight=config.training.loss_config.get('dice_weight', 0.4),
            focal_weight=config.training.loss_config.get('focal_weight', 0.3),
            use_frequency_weight=config.training.loss_config.get('use_frequency_weight', True),
            use_adaptive_dice=config.training.loss_config.get('use_adaptive_dice', True),
            focal_alpha=config.training.loss_config.get('focal_alpha', 1.0),
            focal_gamma=config.training.loss_config.get('focal_gamma', 2.0)
        ).to(self.device)
        
        self.optimizer = self._setup_optimizer()
        self.scheduler = self._setup_scheduler()
        self.scaler = GradScaler() if config.training.mixed_precision else None
                
        # Logging
        self.global_step = 0
        self.epoch = 0
        
        # Track best values for all three metrics in 0.4-0.6 frequency range
        self.best_metrics_0_4_0_6 = {
            'accuracy_0_4_0_6': 0.0,
            'f1_score_0_4_0_6': 0.0,
            'iou_0_4_0_6': 0.0
        }
        
        # Early stopping
        self.early_stopping_patience = self.config.training.get('early_stopping_patience', 10)
        self.patience_counter = 0
        
        # 添加历史指标跟踪
        self.train_history = {
            'loss': [],
            'metrics': []
        }
        
        self.val_history = {
            'loss': [],
            'metrics': []
        }
    
    def _setup_model(self):
        """Initialize and prepare model for distributed training"""
        # Create model
        config_copy = self.config
        
        # 确保配置中包含distance_aware采样策略设置
        if not hasattr(config_copy, 'model'):
            # 创建model属性
            class ModelConfig:
                pass
            config_copy.model = ModelConfig()
        
        # 设置采样策略为distance_aware以优化内存使用
        if not hasattr(config_copy.model, 'sampling_strategy'):
            config_copy.model.sampling_strategy = 'distance_aware'
        else:
            config_copy.model.sampling_strategy = 'distance_aware'
            
        # 创建模型 —— 多 GPU 时禁用梯度检查点以避免 reentrant backward 冲突
        if self.world_size > 1:
            if hasattr(config_copy, 'model'):
                setattr(config_copy.model, 'use_gradient_checkpoint', False)
            else:
                config_copy.model = type('ModelCfg', (), {'use_gradient_checkpoint': False})

        model = create_swin_water_net(config_copy)

        # 若仍存在 use_checkpoint 属性的模块，将其关闭
        if self.world_size > 1:
            for m in model.modules():
                if hasattr(m, 'use_gradient_checkpoint'):
                    m.use_gradient_checkpoint = False
        
        model = model.to(self.device)
        
        # Distributed training
        if self.world_size > 1:
            model = DDP(
                model,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=False,  # 设置为False避免同步问题
                broadcast_buffers=True,  # 确保所有缓冲区同步
                bucket_cap_mb=self.config.hardware.get('ddp_bucket_cap_mb', 25),
                static_graph=True  # 使用静态计算图以优化性能
            )
        
        # Enable cudnn benchmarking for performance
        if self.config.performance.cudnn_benchmark:
            torch.backends.cudnn.benchmark = True
        
        # Report model parameters (only on rank 0)
        if self.local_rank == 0:
            trainable_params, total_params = count_parameters(model)
            logger.info(f"Model: {format_param_count(trainable_params)} trainable / {format_param_count(total_params)} total parameters")
        
        return model
    
    def _setup_data_loaders(self):
        """Create optimized data loaders for distributed training"""
                
        # Create datasets
        train_dataset = WaterBodyDataset(
            index_file=self.index_file,
            missing_db_file=self.missing_db,
            config=self.config,
            mode='train',  # 改为train模式，避免debug模式数据量少导致的问题
            device='a100',
            use_missing_augmentation=True
        )
        
        val_dataset = WaterBodyDataset(
            index_file=self.index_file,
            missing_db_file=self.missing_db,
            config=self.config,
            mode='val',
            device='a100',
            use_missing_augmentation=True
        )
        
        # ---------------- Curriculum learning sampler logic ----------------
        curriculum_cfg = self.config.training.get('curriculum_learning', {}) if hasattr(self.config, 'training') else {}
        curriculum_enabled = curriculum_cfg.get('enabled', False)

        # Store flag for later use
        self.curriculum_enabled = curriculum_enabled

        if curriculum_enabled:
            from data.dataset import CurriculumSampler, CurriculumDistributedSampler  # Local import to avoid circular

            start_frac = curriculum_cfg.get('start_fraction', 0.3)
            end_frac = curriculum_cfg.get('end_fraction', 1.0)
            total_epochs_cl = curriculum_cfg.get('total_epochs', self.config.training.total_epochs)

            if self.world_size > 1:
                train_sampler = CurriculumDistributedSampler(
                    train_dataset,
                    num_replicas=self.world_size,
                    rank=self.local_rank,
                    start_fraction=start_frac,
                    end_fraction=end_frac,
                    total_epochs=total_epochs_cl,
                    shuffle=True,
                    seed=self.config.seed if hasattr(self.config, 'seed') else 42
                )
            else:
                train_sampler = CurriculumSampler(
                    train_dataset,
                    start_fraction=start_frac,
                    end_fraction=end_frac,
                    total_epochs=total_epochs_cl,
                    shuffle_within=True
                )
        else:
            # Default sampler logic (existing)
            train_sampler = DistributedSampler(
                train_dataset,
                num_replicas=self.world_size,
                rank=self.local_rank,
                shuffle=True,
                drop_last=True
            ) if self.world_size > 1 else None
        
        val_sampler = DistributedSampler(
            val_dataset,
            num_replicas=self.world_size,
            rank=self.local_rank,
            shuffle=False,
            drop_last=True  # 改为True，确保验证时也丢弃不完整的batch
        ) if self.world_size > 1 else None
        
        # 使用单个GPU的batch size
        batch_size = self.config.training.batch_size
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=(train_sampler is None),
            sampler=train_sampler,
            num_workers=self.config.training.num_workers,
            pin_memory=self.config.training.pin_memory,
            drop_last=True,
            persistent_workers=self.config.training.get('persistent_workers', True),
            prefetch_factor=self.config.training.get('prefetch_factor', 2)
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,  # 使用相同的batch size
            shuffle=False,
            sampler=val_sampler,
            num_workers=self.config.inference.num_workers,
            pin_memory=True,
            drop_last=True  # 确保batch维度一致
        )
        
        if self.local_rank == 0:
            logger.info(f"Train samples: {len(train_dataset)}")
            logger.info(f"Val samples: {len(val_dataset)}")
            logger.info(f"Per-GPU batch sizes - Train: {batch_size}, Val: {batch_size}")
        
        # ------------------ NEW: Save validation indices ------------------
        if self.local_rank == 0:
            try:
                # Convert samples (list[dict]) to JSON serializable format
                with open(self.val_index_path, 'w') as f:
                    json.dump(val_dataset.samples, f, indent=2, default=lambda o: o if isinstance(o, (int, float, str, bool, list, dict)) else str(o))
                logger.info(f"Saved validation indices to {self.val_index_path}")
            except Exception as e:
                logger.warning(f"Failed to save validation indices: {e}")
        # -----------------------------------------------------------------
        
        return train_loader, val_loader
    
    def _setup_optimizer(self):
        """Create optimizer with parameter groups"""
        # Ensure learning_rate is a float
        learning_rate = float(self.config.training.learning_rate)
        
        # Ensure other parameters are of correct type
        weight_decay = float(self.config.training.weight_decay)
        betas = tuple(map(float, self.config.training.betas)) if isinstance(self.config.training.betas, (list, tuple)) else (0.9, 0.999)
        eps = float(self.config.training.eps)
        
        # Parameter groups for different learning rates - adapted for Video Swin
        try:
            # Try to set up parameter groups for the new Video Swin architecture
            if hasattr(self.model, 'module'):
                model = self.model.module
            else:
                model = self.model
            
            param_groups = []
            
            # Check which components exist and add their parameters
            if hasattr(model, 'encoder'):
                param_groups.append({
                    'params': model.encoder.parameters(),
                    'lr': learning_rate,
                    'name': 'encoder'
                })
            elif hasattr(model, 'backbone'):
                param_groups.append({
                    'params': model.backbone.parameters(),
                    'lr': learning_rate,
                    'name': 'backbone'
                })
            
            if hasattr(model, 'patch_embed'):
                param_groups.append({
                    'params': model.patch_embed.parameters(),
                    'lr': learning_rate * 1.5,
                    'name': 'patch_embed'
                })
            
            if hasattr(model, 'inpainting_head'):
                param_groups.append({
                    'params': model.inpainting_head.parameters(),
                    'lr': learning_rate * 2,  # Higher LR for task head
                    'name': 'inpainting_head'
                })
            
            if hasattr(model, 'geo_temporal_modulator'):
                param_groups.append({
                    'params': model.geo_temporal_modulator.parameters(),
                    'lr': learning_rate * 1.5,
                    'name': 'geo_temporal'
                })
            elif hasattr(model, 'geo_temporal_encoder'):
                param_groups.append({
                    'params': model.geo_temporal_encoder.parameters(),
                    'lr': learning_rate * 1.5,
                    'name': 'geo_temporal'
                })
            
            # If no specific parameter groups were added, use all parameters
            if not param_groups:
                param_groups = [{'params': model.parameters(), 'lr': learning_rate}]
                
        except Exception as e:
            logger.warning(f"Error setting up parameter groups: {e}. Using all parameters.")
            param_groups = [{'params': self.model.parameters(), 'lr': learning_rate}]
        
        optimizer = torch.optim.AdamW(
            param_groups,
            lr=learning_rate,
            betas=betas,
            weight_decay=weight_decay,
            eps=eps
        )
        
        return optimizer
    
    def _setup_scheduler(self):
        """Create learning rate scheduler"""
        scheduler_config = self.config.training.lr_scheduler_params
        
        # Ensure eta_min is float
        eta_min = float(scheduler_config.eta_min)
        
        if self.config.training.scheduler == 'cosine_annealing_warm_restarts':
            # Ensure T_0 and T_mult are integers
            T_0 = int(scheduler_config.T_0)
            T_mult = int(scheduler_config.T_mult)
            
            scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=T_0,
                T_mult=T_mult,
                eta_min=eta_min
            )
        else:
            # Ensure T_max is integer
            T_max = int(self.config.training.total_epochs)
            
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=T_max,
                eta_min=eta_min
            )
        
        return scheduler
    
    def setup_logging(self):
        """Set up logging and checkpointing"""
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.metrics_dir.mkdir(parents=True, exist_ok=True)
        self.vis_dir.mkdir(parents=True, exist_ok=True)
        self.val_index_path.parent.mkdir(parents=True, exist_ok=True)
                
        # TensorBoard writer
        logging_config = self.config.get('logging', {})
        use_tensorboard = False
        if hasattr(logging_config, 'get'):
            use_tensorboard = logging_config.get('use_tensorboard', False)
        else:
            use_tensorboard = getattr(logging_config, 'use_tensorboard', False)
            
        if use_tensorboard:
            self.writer = SummaryWriter(self.log_dir)
        
        # Save config
        with open(self.checkpoint_dir / 'config.yaml', 'w') as f:
            yaml.dump(self.config, f)
    
    def train_epoch(self):
        """Train for one epoch with enhanced numerical stability monitoring"""
        self.model.train()
        
        # Ensure sampler epoch sync (works for curriculum samplers too)
        if self.world_size > 1 or getattr(self, 'curriculum_enabled', False):
            self.train_loader.sampler.set_epoch(self.epoch)
        
        epoch_loss = 0.0
        epoch_metrics = {}  # 初始化指标字典
        
        num_batches = len(self.train_loader)

        # Get log interval with fallback
        logging_config = self.config.get('logging', {})
        log_interval = 10  # Default value
        if hasattr(logging_config, 'get'):
            log_interval = logging_config.get('log_interval', 10)
        else:
            log_interval = getattr(logging_config, 'log_interval', 10)
            
        # Check if tensorboard is enabled
        use_tensorboard = False
        if hasattr(logging_config, 'get'):
            use_tensorboard = logging_config.get('use_tensorboard', False)
        else:
            use_tensorboard = getattr(logging_config, 'use_tensorboard', False)
            
        # Get empty cache frequency with fallback
        performance_config = self.config.get('performance', {})
        empty_cache_freq = 100  # Default value
        if hasattr(performance_config, 'get'):
            empty_cache_freq = performance_config.get('empty_cache_freq', 100)
        else:
            empty_cache_freq = getattr(performance_config, 'empty_cache_freq', 100)
            
        # Get mixed precision setting with fallback
        training_config = self.config.get('training', {})
        mixed_precision = False  # Default value
        
        if hasattr(training_config, 'get'):
            mixed_precision = training_config.get('mixed_precision', False)
        else:
            mixed_precision = getattr(training_config, 'mixed_precision', False)
        
        # 保存最后一个批次的数据和输出用于可视化
        last_batch = None
        last_outputs = None
        
        # 同步所有进程开始训练
        if self.world_size > 1:
            torch.cuda.synchronize(self.device)
            dist.barrier()
        
        for batch_idx, batch in enumerate(self.train_loader):
            metrics = {}  # 确保指标字典在训练循环开始前初始化
            try:
                # 强制转换数据类型并修正范围
                for k, v in batch.items():
                    if torch.is_tensor(v):
                        if v.dtype == torch.int64:
                            v = v.clamp(min=-2147483648, max=2147483647).to(torch.int32)
                        batch[k] = v.to(self.device)

                # 检查数据类型和范围
                for k, v in batch.items():
                    if torch.is_tensor(v):
                        logger.info(f"Batch {batch_idx}: Input {k} - Min: {v.min().item()}, Max: {v.max().item()}, Type: {v.dtype}, Device: {v.device}")

                # 前向传播
                outputs = self.model(batch)
                loss = self.loss_fn(outputs, batch['targets'])

                # 打印损失值的详细信息
                logger.info(f"Batch {batch_idx}: Loss - Value: {loss.item()}, Type: {loss.dtype}, Device: {loss.device}")

                # 检查损失值
                if not torch.isfinite(loss):
                    raise ValueError(f"Batch {batch_idx}: Loss value is not finite: {loss.item()}")

                # 反向传播
                self.optimizer.zero_grad(set_to_none=True)
                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                # 更新指标
                epoch_loss += loss.item()
                for k, v in metrics.items():
                    epoch_metrics[k] = epoch_metrics.get(k, 0) + v

                # 日志记录
                if self.local_rank == 0 and (batch_idx + 1) % log_interval == 0:
                    logger.info(f"Batch {batch_idx + 1}/{num_batches}: Loss={loss.item():.4f}")

                self.global_step += 1

                # 保存最后一个批次的数据和输出用于可视化
                if batch_idx == len(self.train_loader) - 1:
                    last_batch = batch
                    last_outputs = outputs

            except Exception as e:
                # 捕获异常时打印更多上下文信息
                logger.error(f"Rank {self.local_rank}: Error in training batch {batch_idx}: {str(e)}")
                for k, v in batch.items():
                    if torch.is_tensor(v):
                        logger.error(f"Batch {batch_idx}: Input {k} - Min: {v.min().item()}, Max: {v.max().item()}, Type: {v.dtype}, Device: {v.device}")
                continue

            # 定期清理缓存
            if batch_idx % empty_cache_freq == 0:
                torch.cuda.empty_cache()
        
        # Ensure all processes finish epoch together
        if self.world_size > 1:
            torch.cuda.synchronize(self.device)
            dist.barrier()
        
        
        # Average metrics
        avg_loss = epoch_loss / max(1, num_batches)  # Avoid division by zero
        avg_metrics = {k: v / max(1, num_batches) for k, v in epoch_metrics.items()}
        
        return avg_loss, avg_metrics, last_batch, last_outputs
    
    @torch.no_grad()
    def validate(self):
        """Validate the model with extended metrics from evaluation module"""
        self.model.eval()
        val_loss = 0.0
        # Accumulators that persist across validation batches
        total_metrics = {}
        num_valid_batches = {}
        n_batches = 0
        
        # Get mixed precision setting with fallback
        training_config = self.config.get('training', {})
        mixed_precision = False  # Default value
        
        if hasattr(training_config, 'get'):
            mixed_precision = training_config.get('mixed_precision', False)
        else:
            mixed_precision = getattr(training_config, 'mixed_precision', False)
        
        # 同步所有进程开始验证
        if self.world_size > 1:
            torch.cuda.synchronize(self.device)
            dist.barrier()
            
        for batch_idx, batch in enumerate(self.val_loader):
            try:
                # Move batch to device
                batch = {k: v.to(self.device) if torch.is_tensor(v) else v for k, v in batch.items()}
                with autocast('cuda', enabled=mixed_precision):
                    outputs = self.model(batch)
                    loss, metrics = self.loss_fn(outputs, batch)
                    
                # Skip non-finite loss values
                if not torch.isfinite(loss):
                    logger.warning(f"Rank {self.local_rank}: Non-finite validation loss: {loss.item()}. Skipping batch.")
                    continue
                    
                val_loss += loss.item()
                n_batches += 1
                
                # Aggregate metrics across batches
                for range_key, value in metrics.items():
                    if not np.isnan(value):
                        total_metrics[range_key] = total_metrics.get(range_key, 0.0) + value
                        num_valid_batches[range_key] = num_valid_batches.get(range_key, 0.0) + 1
                        
                # Clear memory periodically
                if batch_idx % 20 == 0:  # Every 20 batches
                    torch.cuda.empty_cache()
                    
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Error in validation batch {batch_idx}: {str(e)}")
                continue

        # 确保所有进程完成验证
        if self.world_size > 1:
            torch.cuda.synchronize(self.device)
            dist.barrier()

        # Average metrics
        avg_loss = val_loss / max(1, n_batches)
        avg_metrics = {key: total_metrics[key] / max(1, num_valid_batches.get(key, 0)) 
                      for key in total_metrics}

        if self.world_size > 1:
            avg_loss = self._gather_metric(avg_loss)
            avg_metrics = {k: self._gather_metric(v) for k, v in avg_metrics.items()}

        return avg_loss, avg_metrics
    
    def _gather_metric(self, metric):
        """Gather metric from all processes"""
        if self.world_size == 1:
            return metric
        
        metric_tensor = torch.tensor(metric).to(self.device)
        dist.all_reduce(metric_tensor, op=dist.ReduceOp.SUM)
        return metric_tensor.item() / self.world_size
    
    def save_checkpoint(self, is_best=False):
        """Save model checkpoint"""
        if self.local_rank != 0:
            return
            
        # Get save options with fallbacks
        validation_config = self.config.get('validation', {})
        save_last = True
        save_best_only = True
        
        if hasattr(validation_config, 'get'):
            save_last = validation_config.get('save_last', True)
            save_best_only = validation_config.get('save_best_only', True)
        else:
            save_last = getattr(validation_config, 'save_last', True)
            save_best_only = getattr(validation_config, 'save_best_only', True)
            
        # Get save interval with fallback
        logging_config = self.config.get('logging', {})
        save_interval = 5  # Default value
        
        if hasattr(logging_config, 'get'):
            save_interval = logging_config.get('save_interval', 5)
        else:
            save_interval = getattr(logging_config, 'save_interval', 5)
        
        checkpoint = {
            'epoch': self.epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model.module.state_dict() if hasattr(self.model, 'module') 
                               else self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_metrics_0_4_0_6': self.best_metrics_0_4_0_6,
            'config': self.config
        }
        
        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()
        
        # Save last checkpoint
        if save_last:
            torch.save(checkpoint, self.checkpoint_dir / 'last.pt')
        
        # Save best checkpoint
        if is_best and save_best_only:
            torch.save(checkpoint, self.checkpoint_dir / 'best.pt')
        
        # Save periodic checkpoint
        if self.epoch % save_interval == 0:
            torch.save(checkpoint, self.checkpoint_dir / f'epoch_{self.epoch}.pt')
    
    def save_metrics_history(self):
        """保存历史指标到JSON文件"""
        if self.local_rank != 0:
            return
            
        with open(self.metrics_dir / f'train_history_epoch_{self.epoch}.json', 'w') as f:
            json.dump({
                'loss': self.train_history['loss'],
                'metrics': self.train_history['metrics']
            }, f, indent=2)
            
        with open(self.metrics_dir / f'val_history_epoch_{self.epoch}.json', 'w') as f:
            json.dump({
                'loss': self.val_history['loss'],
                'metrics': self.val_history['metrics']
            }, f, indent=2)
            
        # 保存最新的完整历史记录
        with open(self.metrics_dir / 'train_history.json', 'w') as f:
            json.dump({
                'loss': self.train_history['loss'],
                'metrics': self.train_history['metrics']
            }, f, indent=2)
            
        with open(self.metrics_dir / 'val_history.json', 'w') as f:
            json.dump({
                'loss': self.val_history['loss'],
                'metrics': self.val_history['metrics']
            }, f, indent=2)

    def train(self):
        """Main training loop"""
        if self.local_rank == 0:
            logger.info(f"Training: {self.config.training.total_epochs} epochs | {self.world_size} GPUs | BS={self.config.training.batch_size}/GPU")
        # Get validation frequency with fallback
        validation_config = self.config.get('validation', {})
        validate_every_n_epochs = 1  # Default value
        if hasattr(validation_config, 'get'):
            validate_every_n_epochs = validation_config.get('validate_every_n_epochs', 1)
        else:
            validate_every_n_epochs = getattr(validation_config, 'validate_every_n_epochs', 1)
            
        # Get logging config
        logging_config = self.config.get('logging', {})
        use_tensorboard = False
        
        if hasattr(logging_config, 'get'):
            use_tensorboard = logging_config.get('use_tensorboard', False)
        else:
            use_tensorboard = getattr(logging_config, 'use_tensorboard', False)
            
        # 获取可视化保存频率
        vis_config = self.config.get('visualization', {})
        vis_interval = 1  # 默认每个epoch都保存可视化
        if hasattr(vis_config, 'get'):
            vis_interval = vis_config.get('vis_interval', 1)
        else:
            vis_interval = getattr(vis_config, 'vis_interval', 1)
                
        # 确保vis_config是字典类型并设置必要的键值
        if not isinstance(vis_config, dict):
            vis_config = {}
        
        # 设置可视化目录
        vis_config['vis_dir'] = self.vis_dir
        
        # 确保其他必要的键存在
        if 'enabled' not in vis_config:
            vis_config['enabled'] = True
        if 'num_vis_samples' not in vis_config:
            vis_config['num_vis_samples'] = 4
        if 'freq_min' not in vis_config:
            vis_config['freq_min'] = 0.2
        if 'freq_max' not in vis_config:
            vis_config['freq_max'] = 0.8
        
        for epoch in range(self.config.training.total_epochs):
            self.epoch = epoch
            
            # Train
            train_loss, train_metrics, last_train_batch, last_train_outputs = self.train_epoch()
            
            # 添加训练指标到历史记录
            if self.local_rank == 0:
                self.train_history['loss'].append(train_loss)
                self.train_history['metrics'].append(train_metrics)
            
            # Validate
            if epoch % validate_every_n_epochs == 0:
                val_loss, val_metrics = self.validate()
                
                # 添加验证指标到历史记录
                if self.local_rank == 0:
                    self.val_history['loss'].append(val_loss)
                    self.val_history['metrics'].append(val_metrics)
                
                # Check if best model - all three metrics in 0.4-0.6 frequency range must improve
                current_metrics = {
                    'accuracy_0_4_0_6': val_metrics.get('accuracy_0_4_0_6', 0),
                    'f1_score_0_4_0_6': val_metrics.get('f1_score_0_4_0_6', 0),
                    'iou_0_4_0_6': val_metrics.get('iou_0_4_0_6', 0)
                }
                
                # Check if ALL three metrics in 0.4-0.6 range have improved
                all_improved = all(
                    current_metrics[metric] > self.best_metrics_0_4_0_6[metric] 
                    for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']
                )
                
                is_best = all_improved
                if is_best:
                    # Update all best metrics for 0.4-0.6 frequency range
                    for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']:
                        self.best_metrics_0_4_0_6[metric] = current_metrics[metric]
                    self.patience_counter = 0
                    
                    if self.local_rank == 0:
                        logger.info(f"New best model (0.4-0.6 freq)! Acc={current_metrics['accuracy_0_4_0_6']:.4f}, "
                                  f"F1={current_metrics['f1_score_0_4_0_6']:.4f}, "
                                  f"IoU={current_metrics['iou_0_4_0_6']:.4f}")
                else:
                    self.patience_counter += 1
                
                # Log validation results (simplified)
                if self.local_rank == 0:
                    logger.info(f"Epoch {epoch}: Val Loss={val_loss:.4f}, \n"
                        f"Acc (0-1) = {val_metrics.get('accuracy_0_1', float('nan')):.4f}, F1 (0-1)={val_metrics.get('f1_score_0_1', float('nan')):.4f}, IoU (0-1)={val_metrics.get('iou_0_1', float('nan')):.4f}, \n"
                        f"Acc (0.2-0.8) = {val_metrics.get('accuracy_0_2_0_8', float('nan')):.4f}, F1 (0.2-0.8)={val_metrics.get('f1_score_0_2_0_8', float('nan')):.4f}, IoU (0.2-0.8)={val_metrics.get('iou_0_2_0_8', float('nan')):.4f}, \n"
                        f"Acc (0.4-0.6) = {val_metrics.get('accuracy_0_4_0_6', float('nan')):.4f}, F1 (0.4-0.6)={val_metrics.get('f1_score_0_4_0_6', float('nan')):.4f}, IoU (0.4-0.6)={val_metrics.get('iou_0_4_0_6', float('nan')):.4f}")
                    
                    if use_tensorboard:
                        self.writer.add_scalar('val/loss', val_loss, epoch)
                        for k, v in val_metrics.items():
                            self.writer.add_scalar(f'val/{k}', v, epoch)
                
                # Save checkpoint
                self.save_checkpoint(is_best)
            
            # 保存指标历史
            if self.local_rank == 0:
                self.save_metrics_history()
                
            # 保存可视化图像
            if self.local_rank == 0 and last_train_batch is not None and last_train_outputs is not None and epoch % vis_interval == 0:
                save_visualization(last_train_batch, last_train_outputs, epoch, vis_config, logger)
                logger.info(f"Saved visualization for epoch {epoch}")
                
            # Update learning rate
            self.scheduler.step()
            
            # Log learning rates (simplified)
            if self.local_rank == 0:  # Only every 10 epochs
                current_lr = self.optimizer.param_groups[0]['lr']
                logger.info(f"LR: {current_lr:.6f}")
                
            if self.local_rank == 0 and use_tensorboard:
                for i, param_group in enumerate(self.optimizer.param_groups):
                    self.writer.add_scalar(
                        f"lr/{param_group.get('name', i)}", 
                        param_group['lr'], 
                        epoch
                    )

            # Check for early stopping
            if self.early_stopping_patience > 0 and self.patience_counter >= self.early_stopping_patience:
                if self.local_rank == 0:
                    logger.info(f"Early stopping triggered after {self.early_stopping_patience} epochs with no improvement.")
                break
        
        if self.local_rank == 0:
            logger.info(f"Training completed! Best metrics (0.4-0.6 freq) - "
                       f"Accuracy: {self.best_metrics_0_4_0_6['accuracy_0_4_0_6']:.4f}, "
                       f"F1: {self.best_metrics_0_4_0_6['f1_score_0_4_0_6']:.4f}, "
                       f"IoU: {self.best_metrics_0_4_0_6['iou_0_4_0_6']:.4f}")


def setup_distributed():
    """Initialize distributed training"""
    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
        world_size = int(os.environ['WORLD_SIZE'])

        # 设置NCCL调试和优化选项
        os.environ.setdefault('NCCL_DEBUG', 'WARN')
        os.environ.setdefault('NCCL_IB_DISABLE', '1')
        os.environ.setdefault('NCCL_P2P_DISABLE', '1')
        os.environ.setdefault('TORCH_DISTRIBUTED_DEBUG', 'INFO')

        torch.cuda.set_device(local_rank)
        dist.init_process_group(backend='nccl')

        return local_rank, world_size
    else:
        return 0, 1


def _distributed_worker(local_rank: int):
    """Entry point for each spawned GPU process."""
    # Get world_size from environment or calculate from GPU count
    world_size = int(os.environ.get('WORLD_SIZE', torch.cuda.device_count()))

    os.environ["LOCAL_RANK"] = str(local_rank)
    os.environ["RANK"] = str(local_rank)
    os.environ["WORLD_SIZE"] = str(world_size)
    torch.cuda.set_device(local_rank)
    main()


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Optimized Inpainting Training')
    parser.add_argument('--config', type=str, required=True,
                        help='Path to configuration file')
    parser.add_argument('--index_file', type=str, required=True,
                        help='Path to data index file')
    parser.add_argument('--missing_db', type=str, required=True,
                        help='Path to missing data database')
    parser.add_argument('--resume', type=str, default=None,
                        help='Path to checkpoint to resume from')
    
    args = parser.parse_args()
    
    # Setup distributed training
    local_rank, world_size = setup_distributed()
    
    # Load configuration
    config = get_config(args.config)
    
    # Log essential paths only
    if local_rank == 0:
        logger.info(f"Data: {args.index_file} | Missing DB: {args.missing_db}")
    
    # Set random seed
    torch.manual_seed(config.seed)
    np.random.seed(config.seed)
    
    # Create trainer
    trainer = InpaintingTrainer(config, local_rank, world_size, args.index_file, args.missing_db)
    
    # Resume from checkpoint if specified
    if args.resume:
        if local_rank == 0:
            logger.info(f"Loading checkpoint from {args.resume}")

        # Always load the checkpoint on every rank so that model/optimizer states are consistent.
        checkpoint = torch.load(args.resume, map_location='cpu', weights_only=False)

        # -------------------------------------------------------------
        # 1. Load model weights – handle both DDP-wrapped and single-GPU cases
        # -------------------------------------------------------------
        target_model = trainer.model.module if hasattr(trainer.model, 'module') else trainer.model

        # Get the checkpoint state dict
        checkpoint_state_dict = checkpoint['model_state_dict']

        # Handle key mismatch between enhanced model (base_model.*) and direct model
        if any(key.startswith('base_model.') for key in checkpoint_state_dict.keys()):
            # Checkpoint was saved with EnhancedSwinWaterNetV8 structure
            # Extract base_model keys and remove the prefix
            filtered_state_dict = {}
            for key, value in checkpoint_state_dict.items():
                if key.startswith('base_model.'):
                    new_key = key.replace('base_model.', '')
                    filtered_state_dict[new_key] = value
                elif not key.startswith('target_guided_attention.'):
                    # Keep non-base_model keys that aren't attention-related
                    filtered_state_dict[key] = value

            if local_rank == 0:
                logger.info(f"Detected enhanced model checkpoint. Extracted {len(filtered_state_dict)} base model keys.")

            checkpoint_state_dict = filtered_state_dict

        missing_keys, unexpected_keys = target_model.load_state_dict(
            checkpoint_state_dict, strict=False
        )

        # Optional: print key mismatch information only on rank-0 to avoid log spam
        if local_rank == 0:
            if missing_keys:
                logger.warning(f"Missing keys when loading checkpoint (showing up to 20): {missing_keys[:20]}")
            if unexpected_keys:
                logger.warning(f"Unexpected keys when loading checkpoint (showing up to 20): {unexpected_keys[:20]}")

        # -------------------------------------------------------------
        # 2. Optimizer / Scheduler / Scaler states – safe to load on each rank
        # -------------------------------------------------------------
        try:
            trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        except KeyError as e:
            if local_rank == 0:
                logger.warning(f"Optimizer/Scheduler state not found in checkpoint: {e}")

        if trainer.scaler and 'scaler_state_dict' in checkpoint:
            trainer.scaler.load_state_dict(checkpoint['scaler_state_dict'])

        # -------------------------------------------------------------
        # 3. Training progress & best metrics
        # -------------------------------------------------------------
        trainer.epoch = checkpoint.get('epoch', 0)
        trainer.global_step = checkpoint.get('global_step', 0)

        if 'best_metrics_0_4_0_6' in checkpoint:
            trainer.best_metrics_0_4_0_6 = checkpoint['best_metrics_0_4_0_6']
        else:
            # Backward compatibility with very old checkpoints
            best_val_acc = checkpoint.get('best_metrics_0_4_0_6', 0.0)
            trainer.best_metrics_0_4_0_6 = {
                'accuracy_0_4_0_6': best_val_acc,
                'f1_score_0_4_0_6': best_val_acc,
                'iou_0_4_0_6': best_val_acc
            }

        if local_rank == 0:
            logger.info(f"Checkpoint loaded. Resuming from epoch {trainer.epoch}, step {trainer.global_step}.")

    # Allowlist custom globals in older checkpoints for safe unpickling (PyTorch >=2.6)
    try:
        import torch.serialization as _serialization
        _serialization.add_safe_globals({
            'configs.get_config': get_config
        })
    except Exception as e:
        logger.warning(f"Unable to register safe globals for deserialization: {e}")

    # Start training
    trainer.train()
    
    # Cleanup
    if world_size > 1:
        dist.destroy_process_group()


def _distributed_worker(local_rank: int, world_size: int):
    """Entry point for each spawned GPU process."""
    os.environ["LOCAL_RANK"] = str(local_rank)
    os.environ["RANK"] = str(local_rank)
    os.environ["WORLD_SIZE"] = str(world_size)
    torch.cuda.set_device(local_rank)
    main()


if __name__ == "__main__":
    # Parse arguments to check if distributed training is requested
    parser = argparse.ArgumentParser(description='Swin Transformer v8 Training')
    parser.add_argument('--distributed', action='store_true', default=False,
                        help='Enable distributed training (requires torchrun or mp.spawn)')
    parser.add_argument('--num_gpus', type=int, default=None,
                        help='Number of GPUs to use for distributed training (auto-detect if not specified)')
    
    # Parse only the distributed-related arguments first
    args, _ = parser.parse_known_args()
    
    if args.distributed:
        # User explicitly requested distributed training
        if 'LOCAL_RANK' in os.environ:
            # Already running under a distributed launcher (e.g. torchrun)
            main()
        else:
            # Auto-launch distributed training
            gpu_count = torch.cuda.device_count()
            if args.num_gpus is not None:
                world_size = min(args.num_gpus, gpu_count)
            else:
                world_size = gpu_count
                
            if world_size > 1:
                # Default rendezvous parameters
                os.environ.setdefault("MASTER_ADDR", "127.0.0.1")
                os.environ.setdefault("MASTER_PORT", "29500")
                
                print(f"Launching distributed training with {world_size} GPUs")
                mp.spawn(_distributed_worker, args=(world_size,), nprocs=world_size, join=True)
            else:
                print("Warning: Distributed training requested but only 1 GPU available. Running single-GPU training.")
                main()
    else:
        # Single-GPU training (default)
        main()