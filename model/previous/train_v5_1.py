"""
Enhanced Training Script for Swin Transformer v5.1 Video Water Body Detection
Optimized for multi-GPU training with improved data type compatibility and numerical stability

Key improvements over v5:
1. Enhanced data type handling and compatibility for int64/float issues
2. Improved numerical stability in mixed precision training
3. Better error handling and recovery mechanisms
4. Enhanced distributed training synchronization
5. Improved gradient handling and NaN/Inf detection
6. Better memory management and cleanup
7. Enhanced checkpoint loading and saving
8. Improved validation and metrics computation
"""

import argparse
import logging
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.amp.grad_scaler import GradScaler
from torch.amp.autocast_mode import autocast
from torch.utils.data import DataLoader, DistributedSampler
from torch.utils.tensorboard import SummaryWriter
import yaml
import numpy as np
import multiprocessing as mp
from typing import Optional, Dict, Any, Union

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data.dataset import WaterBodyDataset
from model.previous.swin_water_net_v5_1 import create_swin_water_net  # Use v5.1
from model.loss import InpaintingLossWithWaterWeight
from model.mixed_precision_utils import (
    convert_batch_for_mixed_precision, 
    handle_loss_dtype, 
    initialize_mixed_precision_model, 
    check_gradient_health, 
    clean_nan_inf_gradients
)
from configs import get_config
from evaluation.visualization import save_visualization

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Set specific loggers to appropriate levels
logging.getLogger('torch.distributed').setLevel(logging.WARNING)
logging.getLogger('torch.nn.parallel').setLevel(logging.WARNING)
logging.getLogger('PIL').setLevel(logging.WARNING)
logging.getLogger('matplotlib').setLevel(logging.WARNING)


def count_parameters(model):
    """Count model parameters with enhanced reporting"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params


def format_param_count(count):
    """Format parameter count in human readable format"""
    if count >= 1e9:
        return f"{count/1e9:.1f}B"
    elif count >= 1e6:
        return f"{count/1e6:.1f}M"
    elif count >= 1e3:
        return f"{count/1e3:.1f}K"
    else:
        return str(count)


def safe_tensor_to_device(tensor, device, dtype=None, non_blocking=False):
    """Safely move tensor to device with optional dtype conversion"""
    try:
        if tensor is None:
            return None
        
        if not isinstance(tensor, torch.Tensor):
            # Convert to tensor if needed
            tensor = torch.tensor(tensor)
        
        # Move to device
        tensor = tensor.to(device, non_blocking=non_blocking)
        
        # Convert dtype if specified
        if dtype is not None and tensor.dtype != dtype:
            tensor = tensor.to(dtype)
        
        return tensor
        
    except Exception as e:
        logger.error(f"Error moving tensor to device: {e}")
        logger.error(f"Tensor info: shape={tensor.shape if hasattr(tensor, 'shape') else 'unknown'}, "
                    f"dtype={tensor.dtype if hasattr(tensor, 'dtype') else 'unknown'}")
        raise


def convert_batch_to_device(batch, device, mixed_precision=False):
    """Enhanced batch conversion with robust data type handling"""
    processed_batch = {}
    
    for key, value in batch.items():
        try:
            if torch.is_tensor(value):
                # Determine appropriate dtype based on content and key
                if key in ['center_frame_idx', 'year', 'month']:
                    # These should be integer types
                    target_dtype = torch.long
                elif key in ['tile_lon', 'tile_lat']:
                    # Geographic coordinates should be float
                    target_dtype = torch.float32
                elif key == 'input_sequence':
                    # Main input data
                    target_dtype = torch.float16 if mixed_precision else torch.float32
                elif key == 'occurrence':
                    # Occurrence data
                    target_dtype = torch.float32
                else:
                    # Default: keep original dtype but ensure it's compatible
                    target_dtype = value.dtype
                    if target_dtype not in [torch.float32, torch.float16, torch.long, torch.int32, torch.int64]:
                        target_dtype = torch.float32
                
                processed_batch[key] = safe_tensor_to_device(value, device, target_dtype, non_blocking=True)
                
            elif isinstance(value, (list, tuple)):
                # Handle sequences
                if len(value) > 0 and torch.is_tensor(value[0]):
                    processed_list = []
                    for item in value:
                        processed_list.append(safe_tensor_to_device(item, device, non_blocking=True))
                    processed_batch[key] = processed_list
                else:
                    processed_batch[key] = value
            else:
                # Non-tensor values
                processed_batch[key] = value
                
        except Exception as e:
            logger.error(f"Error processing batch key '{key}': {e}")
            logger.error(f"Value info: type={type(value)}, "
                        f"shape={value.shape if hasattr(value, 'shape') else 'unknown'}")
            # Skip problematic keys to avoid crash
            continue
    
    return processed_batch


class SwinV8Trainer:
    """Enhanced trainer for Swin Transformer v5.1 with improved stability"""
    
    def __init__(self, config, local_rank=0, world_size=1, index_file=None, missing_db=None):
        """Initialize trainer with enhanced error handling"""
        self.config = config
        self.local_rank = local_rank
        self.rank = local_rank
        self.world_size = world_size
        self.device = torch.device(f'cuda:{local_rank}' if torch.cuda.is_available() else 'cpu')
        
        # Setup logging first
        self._setup_logging()
        
        # Enhanced data type handling
        self.mixed_precision = config.training.mixed_precision
        self.dtype_map = {
            'float_keys': ['tile_lon', 'tile_lat', 'occurrence'],
            'int_keys': ['center_frame_idx', 'year', 'month'],
            'main_keys': ['input_sequence']
        }
        
        # Setup data loaders
        self.index_file = Path(index_file) if index_file else None
        self.missing_db = Path(missing_db) if missing_db else None
        self.train_loader, self.val_loader = self._setup_data_loaders()
        
        # Setup model
        self._setup_model()
        
        # Enhanced loss function
        self.loss_fn = InpaintingLossWithWaterWeight(
            bce_weight=config.training.loss_config.get('bce_weight', 0.3),
            dice_weight=config.training.loss_config.get('dice_weight', 0.4),
            focal_weight=config.training.loss_config.get('focal_weight', 0.3),
            use_frequency_weight=config.training.loss_config.get('use_frequency_weight', True),
            use_adaptive_dice=config.training.loss_config.get('use_adaptive_dice', True),
            focal_alpha=config.training.loss_config.get('focal_alpha', 1.0),
            focal_gamma=config.training.loss_config.get('focal_gamma', 2.0)
        ).to(self.device)
        
        # Setup optimizer and scheduler
        self.optimizer = self._setup_optimizer()
        self.scheduler = self._setup_scheduler()
        
        # Enhanced GradScaler
        if self.mixed_precision:
            self.scaler = GradScaler(
                init_scale=2.**16,
                growth_factor=2.0,
                backoff_factor=0.5,
                growth_interval=2000,
                enabled=True
            )
        else:
            self.scaler = None
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        
        # Enhanced metrics tracking
        self.best_metrics_0_4_0_6 = {
            'accuracy_0_4_0_6': 0.0,
            'f1_score_0_4_0_6': 0.0,
            'iou_0_4_0_6': 0.0,
        }
        
        # Early stopping
        self.early_stopping_patience = config.validation.get('early_stopping', {}).get('patience', 15)
        self.patience_counter = 0
        
        # Enhanced history tracking
        self.train_history = {'loss': [], 'metrics': []}
        self.val_history = {'loss': [], 'metrics': []}
        
        # Error recovery settings
        self.max_consecutive_errors = 10
        self.consecutive_errors = 0
        
        if self.local_rank == 0:
            logger.info("Enhanced SwinV8Trainer v5.1 initialized successfully")
    
    def _setup_logging(self):
        """Enhanced logging setup"""
        logging_config = self.config.logging
        
        self.experiment_name = logging_config.get(
            'experiment_name', 
            f"swin_v8_1_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        self.res_dir = Path(logging_config.get('res_dir', 'Results'))
        
        # Create directories
        self.log_dir = self.res_dir / 'logs' / self.experiment_name
        self.checkpoint_dir = self.res_dir / 'checkpoints' / self.experiment_name
        self.metrics_dir = self.res_dir / 'metrics' / self.experiment_name
        self.vis_dir = self.res_dir / 'visualizations' / self.experiment_name
        self.val_index_path = self.res_dir / 'val_indices' / self.experiment_name / 'val_indices.json'
        
        if self.local_rank == 0:
            for directory in [self.log_dir, self.checkpoint_dir, self.metrics_dir, 
                            self.vis_dir, self.val_index_path.parent]:
                directory.mkdir(parents=True, exist_ok=True)
            
            # Enhanced TensorBoard writer
            if logging_config.get('use_tensorboard', True):
                self.writer = SummaryWriter(self.log_dir)
            
            # Save enhanced config
            config_path = self.checkpoint_dir / 'config.yaml'
            try:
                with open(config_path, 'w') as f:
                    yaml.dump(self.config, f)
                logger.info(f"Config saved to {config_path}")
            except Exception as e:
                logger.warning(f"Failed to save config: {e}")
    
    def _setup_model(self):
        """Enhanced model setup with better error handling"""
        try:
            model = create_swin_water_net(self.config)
            
            # Initialize model for mixed precision
            model = initialize_mixed_precision_model(model, self.device)
            
            # Enhanced distributed training setup
            if self.world_size > 1:
                # Ensure model is on correct device before DDP
                model = model.to(self.device)
                
                model = DDP(
                    model,
                    device_ids=[self.local_rank],
                    output_device=self.local_rank,
                    find_unused_parameters=False,
                    broadcast_buffers=True,
                    bucket_cap_mb=self.config.hardware.get('ddp_bucket_cap_mb', 25),
                    static_graph=True
                )
            
            # Enhanced CUDNN settings
            if self.config.performance.cudnn_benchmark:
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
            
            # Report model info
            if self.local_rank == 0:
                trainable_params, total_params = count_parameters(model)
                logger.info(f"Model v5.1: {format_param_count(trainable_params)} trainable / " 
                          f"{format_param_count(total_params)} total parameters")
            
            self.model = model
            
        except Exception as e:
            logger.error(f"Model setup failed: {e}")
            raise
    
    def _setup_data_loaders(self):
        """Enhanced data loader setup with better error handling"""
        try:
            # Create datasets with enhanced error handling
            train_dataset = WaterBodyDataset(
                index_file=str(self.index_file) if self.index_file else "",
                missing_db_file=str(self.missing_db) if self.missing_db else None,
                config=self.config,
                mode='test',
                device='a100',
                use_missing_augmentation=True
            )
            
            val_dataset = WaterBodyDataset(
                index_file=str(self.index_file) if self.index_file else "",
                missing_db_file=str(self.missing_db) if self.missing_db else None,
                config=self.config,
                mode='debug',
                device='a100',
                use_missing_augmentation=True
            )
            
            # Enhanced curriculum learning setup
            curriculum_cfg = self.config.training.get('curriculum_learning', {})
            curriculum_enabled = curriculum_cfg.get('enabled', False)
            self.curriculum_enabled = curriculum_enabled
            
            if curriculum_enabled:
                from data.dataset_v5_1 import CurriculumSampler, CurriculumDistributedSampler
                
                start_frac = curriculum_cfg.get('start_fraction', 0.3)
                end_frac = curriculum_cfg.get('end_fraction', 1.0)
                total_epochs_cl = curriculum_cfg.get('total_epochs', self.config.training.total_epochs)
                
                if self.world_size > 1:
                    train_sampler = CurriculumDistributedSampler(
                        train_dataset,
                        num_replicas=self.world_size,
                        rank=self.local_rank,
                        start_fraction=start_frac,
                        end_fraction=end_frac,
                        total_epochs=total_epochs_cl,
                        shuffle=True,
                        seed=getattr(self.config, 'seed', 42)
                    )
                else:
                    train_sampler = CurriculumSampler(
                        train_dataset,
                        start_fraction=start_frac,
                        end_fraction=end_frac,
                        total_epochs=total_epochs_cl,
                        shuffle_within=True
                    )
            else:
                train_sampler = DistributedSampler(
                    train_dataset,
                    num_replicas=self.world_size,
                    rank=self.local_rank,
                    shuffle=True,
                    drop_last=True
                ) if self.world_size > 1 else None
            
            val_sampler = DistributedSampler(
                val_dataset,
                num_replicas=self.world_size,
                rank=self.local_rank,
                shuffle=False,
                drop_last=True
            ) if self.world_size > 1 else None
            
            # Enhanced data loaders
            batch_size = self.config.training.batch_size
            
            train_loader = DataLoader(
                train_dataset,
                batch_size=batch_size,
                shuffle=(train_sampler is None),
                sampler=train_sampler,
                num_workers=self.config.training.num_workers,
                pin_memory=self.config.training.pin_memory,
                drop_last=True,
                persistent_workers=self.config.training.get('persistent_workers', True),
                prefetch_factor=self.config.training.get('prefetch_factor', 2),
                timeout=300  # Enhanced timeout
            )
            
            val_loader = DataLoader(
                val_dataset,
                batch_size=batch_size,
                shuffle=False,
                sampler=val_sampler,
                num_workers=self.config.inference.num_workers,
                pin_memory=True,
                drop_last=True,
                timeout=300
            )
            
            if self.local_rank == 0:
                logger.info(f"Train samples: {len(train_dataset)}")
                logger.info(f"Val samples: {len(val_dataset)}")
                logger.info(f"Batch sizes - Train: {batch_size}, Val: {batch_size}")
                
                # Enhanced curriculum learning info
                if curriculum_enabled:
                    logger.info(f"Curriculum Learning v5.1 enabled:")
                    logger.info(f"  Start: {start_frac:.1%} ({int(len(train_dataset) * start_frac)} samples)")
                    logger.info(f"  End: {end_frac:.1%} ({int(len(train_dataset) * end_frac)} samples)")
                    logger.info(f"  Epochs: {total_epochs_cl}")
            
            # Enhanced validation indices saving
            if self.local_rank == 0:
                try:
                    val_indices_data = {
                        'samples': val_dataset.samples,
                        'dataset_info': {
                            'mode': 'val',
                            'total_samples': len(val_dataset),
                            'config_version': 'v5.1'
                        }
                    }
                    
                    with open(self.val_index_path, 'w') as f:
                        json.dump(val_indices_data, f, indent=2, 
                                default=lambda o: str(o) if not isinstance(o, (int, float, str, bool, list, dict)) else o)
                    logger.info(f"Enhanced validation indices saved to {self.val_index_path}")
                    
                except Exception as e:
                    logger.warning(f"Failed to save validation indices: {e}")
            
            return train_loader, val_loader
            
        except Exception as e:
            logger.error(f"Data loader setup failed: {e}")
            raise
    
    def _setup_optimizer(self):
        """Enhanced optimizer setup with better parameter grouping"""
        try:
            learning_rate = float(self.config.training.learning_rate)
            weight_decay = float(self.config.training.weight_decay)
            betas = tuple(map(float, self.config.training.betas))
            eps = float(self.config.training.eps)
            
            # Enhanced parameter groups
            if hasattr(self.model, 'module'):
                model = self.model.module
            else:
                model = self.model
            
            if not isinstance(model, nn.Module):
                logger.warning(f"Model is not nn.Module: {type(model)}")
                param_groups = [{'params': self.model.parameters(), 'lr': learning_rate}]
            else:
                param_groups = []
                
                # Enhanced parameter grouping for v5.1 model
                components = [
                    ('encoder', 1.0),
                    ('patch_embed', 1.5),
                    ('decoder', 2.0),
                    ('geo_temporal_encoder', 1.5),
                    ('temporal_attention', 1.2),
                ]
                
                for comp_name, lr_mult in components:
                    if hasattr(model, comp_name):
                        component = getattr(model, comp_name)
                        if isinstance(component, nn.Module):
                            param_groups.append({
                                'params': component.parameters(),
                                'lr': learning_rate * lr_mult,
                                'name': comp_name,
                                'weight_decay': weight_decay
                            })
                
                # Add remaining parameters
                if not param_groups:
                    param_groups = [{'params': model.parameters(), 'lr': learning_rate}]
            
            # Enhanced optimizer
            optimizer = torch.optim.AdamW(
                param_groups,
                lr=learning_rate,
                betas=betas if len(betas) >= 2 else (0.9, 0.999),
                weight_decay=weight_decay,
                eps=eps,
                amsgrad=True  # Enhanced optimization
            )
            
            if self.local_rank == 0:
                logger.info(f"Enhanced optimizer v5.1 created with {len(param_groups)} parameter groups")
            
            return optimizer
            
        except Exception as e:
            logger.error(f"Optimizer setup failed: {e}")
            raise
    
    def _setup_scheduler(self):
        """Enhanced scheduler setup"""
        try:
            scheduler_config = self.config.training.lr_scheduler_params
            
            if self.config.training.scheduler == 'cosine_annealing_warm_restarts':
                scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
                    self.optimizer,
                    T_0=int(scheduler_config.T_0),
                    T_mult=int(scheduler_config.T_mult),
                    eta_min=float(scheduler_config.eta_min)
                )
            else:
                scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                    self.optimizer,
                    T_max=int(self.config.training.total_epochs),
                    eta_min=float(scheduler_config.eta_min)
                )
            
            if self.local_rank == 0:
                logger.info(f"Enhanced scheduler v5.1 created: {type(scheduler).__name__}")
            
            return scheduler
            
        except Exception as e:
            logger.error(f"Scheduler setup failed: {e}")
            raise
    
    def train_epoch(self):
        """Enhanced epoch training with improved error handling"""
        self.model.train()
        
        # Enhanced sampler epoch setting
        if self.train_loader.sampler is not None:
            sampler = self.train_loader.sampler
            if hasattr(sampler, 'set_epoch'):
                sampler.set_epoch(self.epoch)
        
        epoch_loss = 0.0
        epoch_metrics = {}
        num_batches = len(self.train_loader)
        
        # Enhanced configuration
        log_interval = self.config.logging.get('log_interval', 10)
        empty_cache_freq = self.config.performance.get('empty_cache_freq', 100)
        
        last_batch = None
        last_outputs = None
        
        # Enhanced synchronization
        if self.world_size > 1:
            try:
                torch.cuda.synchronize(self.device)
                dist.barrier(timeout=datetime.timedelta(seconds=300))
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Synchronization error: {e}")
        
        for batch_idx, batch in enumerate(self.train_loader):
            try:
                # Enhanced batch processing with data type handling
                processed_batch = convert_batch_to_device(batch, self.device, self.mixed_precision)
                
                # Enhanced forward pass
                with autocast('cuda', enabled=self.mixed_precision):
                    outputs = self.model(processed_batch)
                    loss, metrics = self.loss_fn(outputs, processed_batch)
                
                # Enhanced loss handling
                loss = handle_loss_dtype(loss, self.mixed_precision)
                
                # Enhanced NaN/Inf checking
                if not torch.isfinite(loss):
                    logger.warning(f"Rank {self.local_rank}: Non-finite loss: {loss.item()}, skipping batch")
                    self.consecutive_errors += 1
                    if self.consecutive_errors >= self.max_consecutive_errors:
                        logger.error(f"Too many consecutive errors ({self.consecutive_errors}), stopping training")
                        raise RuntimeError("Training stopped due to consecutive errors")
                    continue
                
                # Reset error counter on successful batch
                self.consecutive_errors = 0
                
                # Enhanced backward pass
                if self.scaler is not None:
                    # Mixed precision training
                    self.scaler.scale(loss).backward()
                    
                    if (batch_idx + 1) % self.config.training.accumulation_steps == 0:
                        # Enhanced gradient handling
                        self.scaler.unscale_(self.optimizer)
                        
                        # Check gradient health
                        if self.global_step % 500 == 0:
                            grad_stats = check_gradient_health(self.model, threshold=100.0)
                            if grad_stats['nan_count'] > 0 or grad_stats['inf_count'] > 0:
                                logger.warning(f"Gradient issues: NaN={grad_stats['nan_count']}, "
                                             f"Inf={grad_stats['inf_count']}")
                                self.optimizer.zero_grad()
                                self.scaler.update()
                                continue
                        
                        # Clean gradients
                        nan_inf_count = clean_nan_inf_gradients(self.model)
                        if nan_inf_count > 10:
                            logger.warning(f"Cleaned {nan_inf_count} problematic gradients")
                            self.optimizer.zero_grad()
                            self.scaler.update()
                            continue
                        
                        # Enhanced gradient clipping
                        if self.config.training.gradient_clipping > 0:
                            torch.nn.utils.clip_grad_norm_(
                                self.model.parameters(),
                                self.config.training.gradient_clipping
                            )
                        
                        # Optimizer step
                        self.scaler.step(self.optimizer)
                        self.scaler.update()
                        self.optimizer.zero_grad()
                else:
                    # Standard training
                    loss.backward()
                    
                    if (batch_idx + 1) % self.config.training.accumulation_steps == 0:
                        # Enhanced gradient health check
                        if self.global_step % 1000 == 0:
                            grad_stats = check_gradient_health(self.model, threshold=500.0)
                            if grad_stats['nan_count'] + grad_stats['inf_count'] > grad_stats['param_count'] * 0.1:
                                logger.warning(f"Too many problematic gradients, skipping step")
                                self.optimizer.zero_grad()
                                continue
                        
                        # Clean gradients
                        nan_inf_count = clean_nan_inf_gradients(self.model)
                        if nan_inf_count > 20:
                            logger.warning(f"Too many NaN/Inf gradients ({nan_inf_count}), skipping step")
                            self.optimizer.zero_grad()
                            continue
                        
                        # Gradient clipping
                        if self.config.training.gradient_clipping > 0:
                            torch.nn.utils.clip_grad_norm_(
                                self.model.parameters(),
                                self.config.training.gradient_clipping
                            )
                        
                        self.optimizer.step()
                        self.optimizer.zero_grad()
                
                # Update metrics
                epoch_loss += loss.item()
                for k, v in metrics.items():
                    epoch_metrics[k] = epoch_metrics.get(k, 0) + v
                
                # Enhanced logging
                if self.local_rank == 0 and (batch_idx + 1) % log_interval == 0:
                    progress = (batch_idx + 1) / num_batches * 100
                    acc = metrics.get('accuracy_0_1', 0)
                    f1 = metrics.get('f1_score_0_1', 0)
                    iou = metrics.get('iou_0_1', 0)
                    
                    logger.info(f"Epoch {self.epoch} [{progress:5.1f}%] "
                              f"Loss={loss.item():.4f} Acc={acc:.4f} F1={f1:.4f} IoU={iou:.4f}")
                    
                    if hasattr(self, 'writer'):
                        self.writer.add_scalar('train/loss', loss.item(), self.global_step)
                        for k, v in metrics.items():
                            self.writer.add_scalar(f'train/{k}', v, self.global_step)
                
                self.global_step += 1
                
                # Save last batch for visualization
                if batch_idx == len(self.train_loader) - 1:
                    last_batch = {k: v.detach() if torch.is_tensor(v) else v 
                                for k, v in processed_batch.items()}
                    last_outputs = {k: {sk: sv.detach() if torch.is_tensor(sv) else sv 
                                      for sk, sv in v.items()} if isinstance(v, dict) 
                                  else v.detach() if torch.is_tensor(v) else v
                                  for k, v in outputs.items()}
                
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Error in training batch {batch_idx}: {str(e)}")
                self.optimizer.zero_grad()
                if self.scaler is not None:
                    self.scaler.update()
                
                self.consecutive_errors += 1
                if self.consecutive_errors >= self.max_consecutive_errors:
                    logger.error("Too many consecutive errors, stopping training")
                    raise
                continue
            
            # Enhanced cache cleanup
            if batch_idx % empty_cache_freq == 0:
                torch.cuda.empty_cache()
        
        # Enhanced epoch end synchronization
        if self.world_size > 1:
            try:
                torch.cuda.synchronize(self.device)
                dist.barrier(timeout=datetime.timedelta(seconds=300))
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Epoch end sync error: {e}")
        
        # Calculate averages
        avg_loss = epoch_loss / max(1, num_batches)
        avg_metrics = {k: v / max(1, num_batches) for k, v in epoch_metrics.items()}
        
        return avg_loss, avg_metrics, last_batch, last_outputs
    
    @torch.no_grad()
    def validate(self):
        """Enhanced validation with improved error handling"""
        self.model.eval()
        val_loss = 0.0
        total_metrics = {}
        num_valid_batches = {}
        n_batches = 0
        
        # Enhanced validation start sync
        if self.world_size > 1:
            try:
                torch.cuda.synchronize(self.device)
                dist.barrier(timeout=datetime.timedelta(seconds=300))
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Validation start sync error: {e}")
        
        for batch_idx, batch in enumerate(self.val_loader):
            try:
                # Enhanced batch processing
                processed_batch = convert_batch_to_device(batch, self.device, self.mixed_precision)
                
                with autocast('cuda', enabled=self.mixed_precision):
                    outputs = self.model(processed_batch)
                    loss, metrics = self.loss_fn(outputs, processed_batch)
                
                # Enhanced loss validation
                if torch.isfinite(loss):
                    val_loss += loss.item()
                    n_batches += 1
                    
                    # Enhanced metrics aggregation
                    for range_key, value in metrics.items():
                        if not np.isnan(value) and np.isfinite(value):
                            total_metrics[range_key] = total_metrics.get(range_key, 0.0) + value
                            num_valid_batches[range_key] = num_valid_batches.get(range_key, 0) + 1
                else:
                    logger.warning(f"Non-finite validation loss: {loss.item()}")
                
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Error in validation batch {batch_idx}: {str(e)}")
                continue
        
        # Enhanced validation end sync
        if self.world_size > 1:
            try:
                torch.cuda.synchronize(self.device)
                dist.barrier(timeout=datetime.timedelta(seconds=300))
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Validation end sync error: {e}")
        
        # Calculate averages
        avg_loss = val_loss / max(1, n_batches)
        avg_metrics = {key: total_metrics[key] / max(1, num_valid_batches.get(key, 1)) 
                      for key in total_metrics}
        
        # Enhanced distributed gathering
        if self.world_size > 1:
            avg_loss = self._gather_metric(avg_loss)
            avg_metrics = {k: self._gather_metric(v) for k, v in avg_metrics.items()}
        
        return avg_loss, avg_metrics
    
    def _gather_metric(self, metric):
        """Enhanced metric gathering with error handling"""
        if self.world_size == 1:
            return metric
        
        try:
            metric_tensor = torch.tensor(metric, dtype=torch.float32).to(self.device)
            dist.all_reduce(metric_tensor, op=dist.ReduceOp.SUM)
            return metric_tensor.item() / self.world_size
        except Exception as e:
            logger.error(f"Metric gathering failed: {e}")
            return metric
    
    def save_checkpoint(self, is_best=False):
        """Enhanced checkpoint saving with better error handling"""
        if self.local_rank != 0:
            return
        
        try:
            # Enhanced save options
            validation_config = self.config.get('validation', {})
            save_last = validation_config.get('save_last', True)
            save_best_only = validation_config.get('save_best_only', True)
            
            logging_config = self.config.get('logging', {})
            save_interval = logging_config.get('save_interval', 5)
            
            # Get model state dict
            if hasattr(self.model, 'module'):
                model = self.model.module
            else:
                model = self.model
            
            model_state_dict = model.state_dict() if isinstance(model, nn.Module) else {}
            
            # Enhanced checkpoint data
            checkpoint = {
                'epoch': self.epoch,
                'global_step': self.global_step,
                'model_state_dict': model_state_dict,
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict(),
                'best_metrics_0_4_0_6': self.best_metrics_0_4_0_6,
                'train_history': self.train_history,
                'val_history': self.val_history,
                'config': self.config,
                'patience_counter': self.patience_counter,
                'curriculum_enabled': getattr(self, 'curriculum_enabled', False),
                'consecutive_errors': self.consecutive_errors,
                'timestamp': datetime.now().isoformat(),
                'pytorch_version': torch.__version__,
                'cuda_version': torch.version.cuda if torch.cuda.is_available() else None,
                'model_version': 'v5.1'  # Version tracking
            }
            
            if self.scaler:
                checkpoint['scaler_state_dict'] = self.scaler.state_dict()
            
            # Enhanced saving with backup
            if save_last:
                last_path = self.checkpoint_dir / 'last.pt'
                backup_path = self.checkpoint_dir / f'backup_epoch_{self.epoch}.pt'
                
                # Create backup if last checkpoint exists
                if last_path.exists():
                    try:
                        last_path.rename(backup_path)
                    except Exception as e:
                        logger.warning(f"Failed to create backup: {e}")
                
                torch.save(checkpoint, last_path)
                logger.info(f"Enhanced checkpoint v5.1 saved: {last_path}")
            
            # Save best checkpoint
            if is_best:
                best_path = self.checkpoint_dir / ('best.pt' if save_best_only else f'best_epoch_{self.epoch}.pt')
                torch.save(checkpoint, best_path)
                logger.info(f"Best checkpoint v5.1 saved: {best_path}")
            
            # Periodic checkpoint
            if self.epoch % save_interval == 0:
                periodic_path = self.checkpoint_dir / f'epoch_{self.epoch}.pt'
                torch.save(checkpoint, periodic_path)
                logger.info(f"Periodic checkpoint v5.1 saved: {periodic_path}")
            
            # Enhanced cleanup
            self._cleanup_old_checkpoints()
            
        except Exception as e:
            logger.error(f"Checkpoint saving failed: {e}")
    
    def _cleanup_old_checkpoints(self):
        """Enhanced checkpoint cleanup with better error handling"""
        try:
            # Clean epoch checkpoints (keep last 5)
            epoch_checkpoints = sorted(self.checkpoint_dir.glob('epoch_*.pt'), 
                                     key=lambda x: int(x.stem.split('_')[1]))
            
            if len(epoch_checkpoints) > 5:
                for old_checkpoint in epoch_checkpoints[:-5]:
                    try:
                        old_checkpoint.unlink()
                        logger.debug(f"Cleaned old checkpoint: {old_checkpoint}")
                    except Exception as e:
                        logger.warning(f"Failed to remove {old_checkpoint}: {e}")
            
            # Clean backup checkpoints (keep last 3)
            backup_checkpoints = sorted(self.checkpoint_dir.glob('backup_epoch_*.pt'), 
                                      key=lambda x: int(x.stem.split('_')[2]))
            
            if len(backup_checkpoints) > 3:
                for old_backup in backup_checkpoints[:-3]:
                    try:
                        old_backup.unlink()
                        logger.debug(f"Cleaned old backup: {old_backup}")
                    except Exception as e:
                        logger.warning(f"Failed to remove {old_backup}: {e}")
                        
        except Exception as e:
            logger.error(f"Checkpoint cleanup failed: {e}")
    
    def save_metrics_history(self):
        """Enhanced metrics history saving"""
        if self.local_rank != 0:
            return
        
        try:
            # Enhanced epoch-specific metrics
            epoch_data = {
                'epoch': self.epoch,
                'global_step': self.global_step,
                'train_loss': self.train_history['loss'][-1] if self.train_history['loss'] else None,
                'train_metrics': self.train_history['metrics'][-1] if self.train_history['metrics'] else {},
                'val_loss': self.val_history['loss'][-1] if self.val_history['loss'] else None,
                'val_metrics': self.val_history['metrics'][-1] if self.val_history['metrics'] else {},
                'timestamp': datetime.now().isoformat()
            }
            
            with open(self.metrics_dir / f'epoch_{self.epoch}_metrics.json', 'w') as f:
                json.dump(epoch_data, f, indent=2)
            
            # Enhanced complete history
            complete_history = {
                'train': {
                    'loss': self.train_history['loss'],
                    'metrics': self.train_history['metrics']
                },
                'val': {
                    'loss': self.val_history['loss'],
                    'metrics': self.val_history['metrics']
                },
                'best_metrics': self.best_metrics_0_4_0_6,
                'metadata': {
                    'total_epochs': self.epoch + 1,
                    'total_steps': self.global_step,
                    'model_version': 'v5.1',
                    'last_updated': datetime.now().isoformat()
                }
            }
            
            with open(self.metrics_dir / 'complete_history.json', 'w') as f:
                json.dump(complete_history, f, indent=2)
                
        except Exception as e:
            logger.error(f"Metrics history saving failed: {e}")
    
    def train(self):
        """Enhanced main training loop with comprehensive error handling"""
        if self.local_rank == 0:
            logger.info(f"Starting enhanced training v5.1 | Epochs: {self.config.training.total_epochs} | "
                       f"GPUs: {self.world_size} | Batch size per GPU: {self.config.training.batch_size}")
        
        validate_every_n_epochs = self.config.validation.get('validate_every_n_epochs', 1)
        use_tensorboard = self.config.logging.get('use_tensorboard', True)
        vis_interval = self.config.visualization.get('vis_interval', 1)
        
        vis_config = self.config.visualization.copy()
        vis_config['vis_dir'] = self.vis_dir
        
        start_time = time.time()
        
        try:
            for epoch in range(self.config.training.total_epochs):
                self.epoch = epoch
                epoch_start_time = time.time()
                
                try:
                    # Enhanced training
                    train_loss, train_metrics, last_train_batch, last_train_outputs = self.train_epoch()
                    
                    # Enhanced timing
                    epoch_time = time.time() - epoch_start_time
                    total_time = time.time() - start_time
                    
                    if self.local_rank == 0:
                        avg_epoch_time = total_time / (epoch + 1)
                        remaining_epochs = self.config.training.total_epochs - epoch - 1
                        eta = remaining_epochs * avg_epoch_time
                        
                        # Enhanced memory monitoring
                        if torch.cuda.is_available():
                            gpu_memory = torch.cuda.max_memory_allocated(self.device) / 1024**3
                            gpu_cached = torch.cuda.max_memory_reserved(self.device) / 1024**3
                            
                            logger.info(f"TIMING v5.1 | Epoch: {epoch:3d} | Time: {epoch_time:.1f}s | "
                                       f"Total: {total_time/3600:.1f}h | ETA: {eta/3600:.1f}h")
                            logger.info(f"MEMORY | GPU: {gpu_memory:.1f}GB alloc, {gpu_cached:.1f}GB cached")
                    
                    # Enhanced curriculum logging
                    if self.local_rank == 0 and hasattr(self, 'curriculum_enabled') and self.curriculum_enabled:
                        sampler = self.train_loader.sampler
                        if hasattr(sampler, '_current_cutoff'):
                            current_samples = sampler._current_cutoff()
                            total_samples = len(sampler.dataset)
                            progress_pct = (current_samples / total_samples) * 100
                            logger.info(f"CURRICULUM v5.1 | Epoch: {epoch:3d} | "
                                       f"Using {current_samples}/{total_samples} samples ({progress_pct:.1f}%)")
                    
                    # Add to history
                    if self.local_rank == 0:
                        self.train_history['loss'].append(train_loss)
                        self.train_history['metrics'].append(train_metrics)
                    
                    # Enhanced validation
                    if epoch % validate_every_n_epochs == 0:
                        val_loss, val_metrics = self.validate()
                        
                        if self.local_rank == 0:
                            self.val_history['loss'].append(val_loss)
                            self.val_history['metrics'].append(val_metrics)
                        
                        # Enhanced best model checking
                        current_metrics = {
                            'accuracy_0_4_0_6': val_metrics.get('accuracy_0_4_0_6', 0),
                            'f1_score_0_4_0_6': val_metrics.get('f1_score_0_4_0_6', 0),
                            'iou_0_4_0_6': val_metrics.get('iou_0_4_0_6', 0)
                        }
                        
                        all_improved = all(
                            current_metrics[metric] > self.best_metrics_0_4_0_6[metric] 
                            for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']
                        )
                        
                        is_best = all_improved
                        if is_best:
                            for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']:
                                self.best_metrics_0_4_0_6[metric] = current_metrics[metric]
                            self.patience_counter = 0
                            
                            if self.local_rank == 0:
                                logger.info(f"New best model v5.1! Acc={current_metrics['accuracy_0_4_0_6']:.4f}, "
                                          f"F1={current_metrics['f1_score_0_4_0_6']:.4f}, "
                                          f"IoU={current_metrics['iou_0_4_0_6']:.4f}")
                        else:
                            self.patience_counter += 1
                        
                        # Enhanced validation logging
                        if self.local_rank == 0:
                            logger.info(f"Validation v5.1 | Epoch {epoch}: Loss={val_loss:.4f}")
                            logger.info(f"Metrics (0-1): Acc={val_metrics.get('accuracy_0_1', 0):.4f}, "
                                       f"F1={val_metrics.get('f1_score_0_1', 0):.4f}, "
                                       f"IoU={val_metrics.get('iou_0_1', 0):.4f}")
                            logger.info(f"Metrics (0.2-0.8): Acc={val_metrics.get('accuracy_0_2_0_8', 0):.4f}, "
                                       f"F1={val_metrics.get('f1_score_0_2_0_8', 0):.4f}, "
                                       f"IoU={val_metrics.get('iou_0_2_0_8', 0):.4f}")
                            logger.info(f"Metrics (0.4-0.6): Acc={val_metrics.get('accuracy_0_4_0_6', 0):.4f}, "
                                       f"F1={val_metrics.get('f1_score_0_4_0_6', 0):.4f}, "
                                       f"IoU={val_metrics.get('iou_0_4_0_6', 0):.4f}")
                            
                            if use_tensorboard:
                                self.writer.add_scalar('val/loss', val_loss, epoch)
                                for k, v in val_metrics.items():
                                    self.writer.add_scalar(f'val/{k}', v, epoch)
                        
                        # Save checkpoint
                        self.save_checkpoint(is_best)
                    else:
                        # Save regular checkpoint
                        if self.local_rank == 0:
                            self.save_checkpoint(is_best=False)
                    
                    # Enhanced metrics history saving
                    if self.local_rank == 0:
                        self.save_metrics_history()
                    
                    # Enhanced visualization
                    if (self.local_rank == 0 and last_train_batch is not None and 
                        last_train_outputs is not None and epoch % vis_interval == 0):
                        try:
                            save_visualization(last_train_batch, last_train_outputs, epoch, vis_config, logger)
                            logger.info(f"VISUAL v5.1 | Epoch: {epoch:3d} | Visualization saved")
                        except Exception as e:
                            logger.warning(f"Visualization failed: {e}")
                    
                    # Update learning rate
                    self.scheduler.step()
                    
                    # Enhanced learning rate logging
                    if self.local_rank == 0 and epoch % 10 == 0:
                        current_lr = self.optimizer.param_groups[0]['lr']
                        logger.info(f"Learning Rate v5.1 | Epoch: {epoch:3d} | LR: {current_lr:.8f}")
                    
                    if self.local_rank == 0 and use_tensorboard:
                        for i, param_group in enumerate(self.optimizer.param_groups):
                            self.writer.add_scalar(
                                f"lr/{param_group.get('name', i)}", 
                                param_group['lr'], 
                                epoch
                            )
                    
                    # Enhanced early stopping
                    if (self.early_stopping_patience > 0 and 
                        self.patience_counter >= self.early_stopping_patience):
                        if self.local_rank == 0:
                            logger.info(f"Early stopping v5.1 triggered after {self.early_stopping_patience} epochs")
                        break
                        
                except Exception as e:
                    logger.error(f"Epoch {epoch} failed: {e}")
                    if epoch < 2:  # Critical failure in early epochs
                        raise
                    else:
                        logger.warning(f"Continuing after epoch {epoch} failure")
                        continue
            
            # Final checkpoint and summary
            if self.local_rank == 0:
                self.save_checkpoint(is_best=False)
                logger.info("Final checkpoint v5.1 saved")
                
                logger.info(f"Training v5.1 completed! Best metrics (0.4-0.6 freq) - "
                           f"Accuracy: {self.best_metrics_0_4_0_6['accuracy_0_4_0_6']:.4f}, "
                           f"F1: {self.best_metrics_0_4_0_6['f1_score_0_4_0_6']:.4f}, "
                           f"IoU: {self.best_metrics_0_4_0_6['iou_0_4_0_6']:.4f}")
                
                # Enhanced final summary
                final_summary = {
                    'training_completed': True,
                    'model_version': 'v5.1',
                    'total_epochs': self.epoch + 1,
                    'total_steps': self.global_step,
                    'best_metrics': self.best_metrics_0_4_0_6,
                    'final_train_loss': self.train_history['loss'][-1] if self.train_history['loss'] else None,
                    'final_val_loss': self.val_history['loss'][-1] if self.val_history['loss'] else None,
                    'total_training_time': time.time() - start_time,
                    'consecutive_errors': self.consecutive_errors,
                    'completion_timestamp': datetime.now().isoformat()
                }
                
                with open(self.metrics_dir / 'final_summary_v5_1.json', 'w') as f:
                    json.dump(final_summary, f, indent=2)
                logger.info("Enhanced final summary v5.1 saved")
                
        except Exception as e:
            logger.error(f"Training loop failed: {e}")
            raise


def setup_distributed():
    """Enhanced distributed training setup"""
    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        
        # Enhanced NCCL configuration for v5.1
        nccl_config = {
            'NCCL_DEBUG': 'WARN',
            'NCCL_IB_DISABLE': '1',
            'NCCL_P2P_DISABLE': '1',
            'TORCH_DISTRIBUTED_DEBUG': 'INFO',
            'NCCL_TIMEOUT': '1800',
            'TORCH_NCCL_BLOCKING_WAIT': '1',
            'TORCH_NCCL_ASYNC_ERROR_HANDLING': '1',
            'NCCL_TREE_THRESHOLD': '0',
            'NCCL_ALGO': 'Ring',
            # Enhanced stability options for v5.1
            'NCCL_MAX_NCHANNELS': '2',
            'NCCL_MIN_NCHANNELS': '1',
            'NCCL_BUFFSIZE': '2097152',  # 2MB buffer
        }
        
        for key, value in nccl_config.items():
            os.environ.setdefault(key, value)
        
        torch.cuda.set_device(local_rank)
        
        try:
            dist.init_process_group(
                backend='nccl',
                timeout=datetime.timedelta(seconds=1800)  # 30 minutes
            )
            logger.info(f"Enhanced distributed v5.1 initialized: rank {local_rank}/{world_size}")
            
        except Exception as e:
            logger.error(f"Distributed initialization failed: {e}")
            raise
        
        return local_rank, world_size
    else:
        return 0, 1


def main():
    """Enhanced main training function with comprehensive error handling"""
    parser = argparse.ArgumentParser(description='Enhanced Swin Transformer v5.1 Training')
    parser.add_argument('--config', type=str, required=True, help='Configuration file path')
    parser.add_argument('--index_file', type=str, required=True, help='Data index file path')
    parser.add_argument('--missing_db', type=str, required=True, help='Missing data database path')
    parser.add_argument('--resume', type=str, default=None, help='Checkpoint path to resume from')
    parser.add_argument('--distributed', action='store_true', default=False, 
                       help='Enable distributed training')
    parser.add_argument('--num_gpus', type=int, default=None, 
                       help='Number of GPUs for distributed training')
    
    args = parser.parse_args()
    
    try:
        # Enhanced distributed setup
        local_rank, world_size = setup_distributed()
        
        # Enhanced configuration loading
        config = get_config(args.config)
        
        # Enhanced random seed setup
        seed = getattr(config, 'seed', 42)
        torch.manual_seed(seed)
        np.random.seed(seed)
        
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
        
        # Enhanced trainer creation
        trainer = SwinV8Trainer(config, local_rank, world_size, args.index_file, args.missing_db)
        
        # Enhanced checkpoint resuming
        if args.resume:
            if local_rank == 0:
                logger.info(f"Loading enhanced checkpoint v5.1 from {args.resume}")
            
            try:
                checkpoint = torch.load(args.resume, map_location='cpu', weights_only=False)
                
                # Enhanced model loading
                target_model = trainer.model.module if hasattr(trainer.model, 'module') else trainer.model
                
                if isinstance(target_model, nn.Module):
                    missing_keys, unexpected_keys = target_model.load_state_dict(
                        checkpoint['model_state_dict'], strict=False
                    )
                    
                    if local_rank == 0:
                        if missing_keys:
                            logger.warning(f"Missing keys (first 10): {missing_keys[:10]}")
                        if unexpected_keys:
                            logger.warning(f"Unexpected keys (first 10): {unexpected_keys[:10]}")
                
                # Enhanced state loading
                try:
                    trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                    trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                except KeyError as e:
                    if local_rank == 0:
                        logger.warning(f"Optimizer/Scheduler state missing: {e}")
                
                if trainer.scaler and 'scaler_state_dict' in checkpoint:
                    trainer.scaler.load_state_dict(checkpoint['scaler_state_dict'])
                
                # Enhanced training state restoration
                trainer.epoch = checkpoint.get('epoch', 0)
                trainer.global_step = checkpoint.get('global_step', 0)
                trainer.consecutive_errors = checkpoint.get('consecutive_errors', 0)
                
                # Enhanced metrics restoration
                if 'best_metrics_0_4_0_6' in checkpoint:
                    trainer.best_metrics_0_4_0_6 = checkpoint['best_metrics_0_4_0_6']
                
                # Enhanced history restoration
                if 'train_history' in checkpoint:
                    trainer.train_history = checkpoint['train_history']
                if 'val_history' in checkpoint:
                    trainer.val_history = checkpoint['val_history']
                
                if local_rank == 0:
                    model_version = checkpoint.get('model_version', 'unknown')
                    logger.info(f"Enhanced checkpoint v5.1 loaded (was {model_version})")
                    logger.info(f"Resuming from epoch {trainer.epoch}, step {trainer.global_step}")
                    
            except Exception as e:
                logger.error(f"Enhanced checkpoint loading failed: {e}")
                if local_rank == 0:
                    logger.info("Starting training from scratch")
        
        # Enhanced training start
        if local_rank == 0:
            logger.info("Starting enhanced training v5.1")
        
        trainer.train()
        
    except Exception as e:
        logger.error(f"Enhanced training v5.1 failed: {e}")
        raise
    finally:
        # Enhanced cleanup
        if world_size > 1:
            try:
                dist.destroy_process_group()
            except Exception as e:
                logger.warning(f"Process group cleanup failed: {e}")


def _distributed_worker(local_rank: int, world_size: int):
    """Enhanced entry point for spawned GPU processes"""
    os.environ["LOCAL_RANK"] = str(local_rank)
    os.environ["RANK"] = str(local_rank)
    os.environ["WORLD_SIZE"] = str(world_size)
    torch.cuda.set_device(local_rank)
    main()


if __name__ == "__main__":
    # Enhanced distributed training handling
    parser = argparse.ArgumentParser(description='Enhanced Swin Transformer v5.1 Training')
    parser.add_argument('--distributed', action='store_true', default=False,
                       help='Enable distributed training')
    parser.add_argument('--num_gpus', type=int, default=None,
                       help='Number of GPUs for distributed training')
    
    args, _ = parser.parse_known_args()
    
    if args.distributed:
        if 'LOCAL_RANK' in os.environ:
            # Already running under distributed launcher
            main()
        else:
            # Enhanced auto-launch
            gpu_count = torch.cuda.device_count()
            world_size = min(args.num_gpus or gpu_count, gpu_count)
            
            if world_size > 1:
                # Enhanced rendezvous
                os.environ.setdefault("MASTER_ADDR", "127.0.0.1")
                os.environ.setdefault("MASTER_PORT", "29500")
                
                print(f"Launching enhanced distributed training v5.1 with {world_size} GPUs")
                try:
                    mp.spawn(_distributed_worker, args=(world_size,), nprocs=world_size, join=True)
                except Exception as e:
                    logger.error(f"Distributed spawn failed: {e}")
                    raise
            else:
                print("Warning: Distributed requested but only 1 GPU available. Running single-GPU.")
                main()
    else:
        # Enhanced single-GPU training
        main()