"""
Swin Water Net v13: Enhanced Spatiotemporal Architecture with STTN-inspired Multi-scale Patches

Key Features:
1. Target frame extraction using center_frame_index
2. Temporal context extraction (excluding target frame)
3. Multi-scale patch embedding with dynamic degree-based patch sizes
4. Spatiotemporal attention using Swin Transformer mechanics
5. [2,2,6,2] depth structure encoder
6. Unet-style decoder for 2×256×256 output
7. Full compatibility with dataset.py and train_v8.py
8. Retained geo-temporal modulation mechanism
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging
from typing import List, Tuple, Optional, Dict

from model.decoder_v8 import SwinUNetDecoder
from model.loss import compute_dynamic_degree

logger = logging.getLogger(__name__)


# ============================================================================
# Reuse core Swin Transformer components from v10
# ============================================================================

class CyclicShift(nn.Module):
    """Cyclic shift operation"""
    def __init__(self, displacement):
        super().__init__()
        self.displacement = displacement

    def forward(self, x):
        return torch.roll(x, shifts=(self.displacement, self.displacement), dims=(1, 2))


class Residual(nn.Module):
    """Residual connection"""
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(x, **kwargs) + x


class PreNorm(nn.Module):
    """Pre-normalization"""
    def __init__(self, dim, fn):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)


class FeedForward(nn.Module):
    """Feed forward network"""
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim),
        )

    def forward(self, x):
        return self.net(x)


def create_shifted_window_mask(H, W, window_size, shift_size):
    """Create attention mask for shifted windows"""
    if shift_size == 0:
        return None

    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    img_mask = torch.zeros((1, H_padded, W_padded, 1))
    h_slices = [slice(0, -window_size),
                slice(-window_size, -shift_size),
                slice(-shift_size, None)]
    w_slices = [slice(0, -window_size),
                slice(-window_size, -shift_size),
                slice(-shift_size, None)]

    cnt = 0
    for h in h_slices:
        for w in w_slices:
            img_mask[:, h, w, :] = cnt
            cnt += 1

    mask_windows = window_partition(img_mask, window_size)
    mask_windows = mask_windows.view(-1, window_size * window_size)
    attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
    attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))

    return attn_mask


def window_partition(x, window_size):
    """Partition input into windows"""
    B, H, W, C = x.shape
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size

    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W

    x = x.view(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """Reverse window partition"""
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w

    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.view(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H_padded, W_padded, -1)

    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]

    return x


class WindowAttention(nn.Module):
    """Window-based multi-head self attention"""

    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )

        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)

    def forward(self, x, mask=None):
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = F.softmax(attn, dim=-1)
        else:
            attn = F.softmax(attn, dim=-1)

        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class SwinTransformerBlock(nn.Module):
    """Swin Transformer Block"""

    def __init__(self, dim, num_heads, window_size=8, shift_size=0, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio

        self.norm1 = norm_layer(dim)
        self.attn = WindowAttention(
            dim, window_size=window_size, num_heads=num_heads,
            qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)

        self.drop_path = nn.Identity() if drop_path <= 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )

    def forward(self, x, H, W):
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"

        shortcut = x
        x = self.norm1(x)
        x = x.reshape(B, H, W, C)

        attn_mask = None
        if self.shift_size > 0:
            attn_mask = create_shifted_window_mask(H, W, self.window_size, self.shift_size)
            if attn_mask is not None:
                attn_mask = attn_mask.to(x.device)

        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        x_windows = window_partition(shifted_x, self.window_size)
        x_windows = x_windows.reshape(-1, self.window_size * self.window_size, C)

        attn_windows = self.attn(x_windows, mask=attn_mask)

        attn_windows = attn_windows.reshape(-1, self.window_size, self.window_size, C)
        shifted_x = window_reverse(attn_windows, self.window_size, H, W)

        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x

        x = x.reshape(B, H * W, C)

        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


# ============================================================================
# New Multi-scale Patch Embedding with Dynamic Degree
# ============================================================================

class MultiScalePatchEmbed(nn.Module):
    """Multi-scale patch embedding with dynamic degree-based patch size selection"""

    def __init__(self, img_size=256, patch_sizes=[4, 8, 16], in_chans=2, embed_dim=96):
        super().__init__()
        self.img_size = img_size
        self.patch_sizes = patch_sizes
        self.in_chans = in_chans
        self.embed_dim = embed_dim
        
        # Create patch embedding layers for each scale
        self.patch_embeds = nn.ModuleList()
        for patch_size in patch_sizes:
            self.patch_embeds.append(
                nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
            )
        
        # Normalization layers
        self.norms = nn.ModuleList([nn.LayerNorm(embed_dim) for _ in patch_sizes])
        
        # Scale selection network based on dynamic degree
        self.scale_selector = nn.Sequential(
            nn.Linear(1, 32),
            nn.GELU(),
            nn.Linear(32, len(patch_sizes)),
            nn.Softmax(dim=-1)
        )

    def forward(self, x, water_frequency=None):
        """
        Args:
            x: (B, T, C, H, W) video sequence
            water_frequency: (B, T, H, W) water frequency for dynamic degree calculation
        Returns:
            patches: (B, T, N, D) multi-scale embedded patches
        """
        B, T, C, H, W = x.shape
        
        # Calculate dynamic degree if water frequency is provided
        if water_frequency is not None:
            # Compute mean dynamic degree for each frame
            dynamic_degrees = compute_dynamic_degree(water_frequency)  # (B, T, H, W)
            frame_degrees = dynamic_degrees.mean(dim=[2, 3])  # (B, T)
        else:
            # Default to medium scale
            frame_degrees = torch.ones(B, T, device=x.device) * 0.5
        
        # Process each frame
        all_patches = []
        for t in range(T):
            frame = x[:, t]  # (B, C, H, W)
            frame_degree = frame_degrees[:, t:t+1]  # (B, 1)
            
            # Get scale weights from dynamic degree
            scale_weights = self.scale_selector(frame_degree)  # (B, num_scales)
            
            # Extract patches at all scales
            scale_patches = []
            for i, (patch_embed, norm) in enumerate(zip(self.patch_embeds, self.norms)):
                # Extract patches
                patches = patch_embed(frame)  # (B, D, H', W')
                D, H_p, W_p = patches.shape[1], patches.shape[2], patches.shape[3]
                patches = patches.permute(0, 2, 3, 1).reshape(B, H_p * W_p, D)  # (B, N, D)
                patches = norm(patches)  # (B, N, D)
                scale_patches.append(patches)
            
            # Weighted combination of scales
            # For simplicity, select the scale with highest weight
            scale_indices = torch.argmax(scale_weights, dim=1)  # (B,)
            
            # Select patches based on scale indices
            frame_patches = []
            for b in range(B):
                selected_scale = scale_indices[b].item()
                frame_patches.append(scale_patches[selected_scale][b:b+1])  # (1, N, D)
            
            # Stack patches for this frame
            frame_patches = torch.cat(frame_patches, dim=0)  # (B, N, D)
            all_patches.append(frame_patches.unsqueeze(1))  # (B, 1, N, D)
        
        # Concatenate all frames
        patches = torch.cat(all_patches, dim=1)  # (B, T, N, D)
        
        return patches


# ============================================================================
# Spatiotemporal Attention with Swin Mechanics
# ============================================================================

class SpatiotemporalWindowAttention(nn.Module):
    """Spatiotemporal attention using target frame as Query, context frames as Key/Value"""

    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        # Separate projections for query (target) and key/value (context)
        self.q_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv_proj = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        # Relative position bias
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads)
        )

        coords_h = torch.arange(window_size)
        coords_w = torch.arange(window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += window_size - 1
        relative_coords[:, :, 1] += window_size - 1
        relative_coords[:, :, 0] *= 2 * window_size - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)

    def forward(self, target_x, context_x, mask=None):
        """
        Args:
            target_x: (B, N, D) target frame features 
            context_x: (B, T_c*N, D) context frames features (flattened)
            mask: attention mask
        """
        B, N, D = target_x.shape
        _, TcN, _ = context_x.shape
        T_c = TcN // N

        # Project to Q, K, V
        q = self.q_proj(target_x).reshape(B, N, self.num_heads, D // self.num_heads).permute(0, 2, 1, 3)  # (B, heads, N, head_dim)
        kv = self.kv_proj(context_x).reshape(B, TcN, 2, self.num_heads, D // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]  # Each: (B, heads, T_c*N, head_dim)

        # Scale query
        q = q * self.scale
        
        # Attention computation
        attn = (q @ k.transpose(-2, -1))  # (B, heads, N, T_c*N)

        # Add relative position bias
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()  # (heads, N_window, N_window)
        
        # For spatiotemporal attention, we need to handle bias differently
        # For now, let's skip the relative position bias for cross-temporal attention
        # TODO: Implement proper spatiotemporal relative position bias
        if N == self.window_size * self.window_size:
            # Standard window attention case
            attn = attn + relative_position_bias.unsqueeze(0)
        # else: skip bias for cross-temporal attention with different patch sizes

        # Apply mask if provided
        if mask is not None:
            attn = attn + mask.unsqueeze(1)

        attn = F.softmax(attn, dim=-1)
        attn = self.attn_drop(attn)

        # Apply attention to values
        x = (attn @ v).transpose(1, 2).reshape(B, N, D)  # (B, N, D)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


class SpatiotemporalSwinBlock(nn.Module):
    """Swin Transformer block with spatiotemporal attention"""

    def __init__(self, dim, num_heads, window_size=8, shift_size=0, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio

        self.norm1 = norm_layer(dim)
        self.attn = SpatiotemporalWindowAttention(
            dim, window_size=window_size, num_heads=num_heads,
            qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)

        self.drop_path = nn.Identity() if drop_path <= 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(drop)
        )

    def forward(self, target_x, context_x):
        """
        Args:
            target_x: (B, N, D) target frame features
            context_x: (B, T_c, N, D) context frames features
        """
        B, N, D = target_x.shape
        _, T_c, _, _ = context_x.shape
        
        shortcut = target_x
        target_x = self.norm1(target_x)
        
        # Flatten context frames for attention computation
        context_x_flat = context_x.view(B, T_c * N, D)  # (B, T_c*N, D)
        context_x_flat = self.norm1(context_x_flat)
        
        # Apply spatiotemporal attention
        x = self.attn(target_x, context_x_flat)
        
        # Residual connection and MLP
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        
        return x


# ============================================================================
# Enhanced Encoder with [2,2,6,2] Structure
# ============================================================================

class PatchMerging(nn.Module):
    """Patch merging layer"""

    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)

    def forward(self, x):
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        assert H % 2 == 0 and W % 2 == 0, f"x size ({H}*{W}) are not even."

        x = x.view(B, H, W, C)

        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C

        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C

        x = self.norm(x)
        x = self.reduction(x)

        return x


class EnhancedSwinEncoder(nn.Module):
    """Enhanced Swin encoder with [2,2,6,2] depth structure"""

    def __init__(self, embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24],
                 window_size=8, mlp_ratio=4., drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 img_size=256, patch_size=4):
        super().__init__()
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_layers = len(depths)
        self.img_size = img_size
        self.patch_size = patch_size

        patches_resolution = img_size // patch_size
        self.patches_resolution = patches_resolution

        # Build layers
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        self.layers = nn.ModuleList()
        self.downsample_layers = nn.ModuleList()

        for i_layer in range(self.num_layers):
            layer_resolution = patches_resolution // (2 ** i_layer)

            layer_blocks = nn.ModuleList()
            for i in range(depths[i_layer]):
                layer_blocks.append(
                    SwinTransformerBlock(
                        dim=int(embed_dim * 2 ** i_layer),
                        num_heads=num_heads[i_layer],
                        window_size=window_size,
                        shift_size=0 if (i % 2 == 0) else window_size // 2,
                        mlp_ratio=mlp_ratio,
                        drop=drop_rate,
                        attn_drop=attn_drop_rate,
                        drop_path=dpr[sum(depths[:i_layer]) + i]
                    )
                )
            self.layers.append(layer_blocks)

            if i_layer < self.num_layers - 1:
                downsample = PatchMerging(
                    input_resolution=(layer_resolution, layer_resolution),
                    dim=int(embed_dim * 2 ** i_layer),
                    norm_layer=nn.LayerNorm
                )
                self.downsample_layers.append(downsample)
            else:
                self.downsample_layers.append(None)

    def forward(self, x):
        """
        Args: 
            x: (B, N, D) input features
        Returns: 
            features: List[(B, C, H, W)] multi-scale features
        """
        features = []
        B, N, _ = x.shape
        H = W = int(math.sqrt(N))
        
        # Update patch resolution based on actual input
        self.patches_resolution = H

        for i_layer, layer_blocks in enumerate(self.layers):
            # Transformer blocks
            for block in layer_blocks:
                x = block(x, H, W)

            # Convert to conv format and save features
            x_conv = x.reshape(B, H, W, -1).permute(0, 3, 1, 2)  # (B, C, H, W)
            features.append(x_conv)

            # Downsample
            if i_layer < self.num_layers - 1:
                downsample = self.downsample_layers[i_layer]
                if downsample is not None:
                    # Update input resolution for this downsampling layer
                    downsample.input_resolution = (H, W)
                    x = downsample(x)
                    H, W = H // 2, W // 2

        return features


# ============================================================================
# Enhanced Context Aggregator
# ============================================================================

class EnhancedContextAggregator(nn.Module):
    """Enhanced context aggregator using spatiotemporal attention"""

    def __init__(self, embed_dim, num_heads=8, window_size=8, depths=[2], mlp_ratio=4.):
        super().__init__()
        self.embed_dim = embed_dim
        self.window_size = window_size

        # Spatiotemporal attention layers
        self.layers = nn.ModuleList()
        for i in range(sum(depths)):
            shifted = (i % 2 == 1)
            self.layers.append(
                SpatiotemporalSwinBlock(
                    dim=embed_dim,
                    num_heads=num_heads,
                    window_size=window_size,
                    shift_size=window_size // 2 if shifted else 0,
                    mlp_ratio=mlp_ratio
                )
            )

        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, target_features, context_features):
        """
        Args:
            target_features: (B, N, D) target frame features
            context_features: (B, T-1, N, D) context frame features
        Returns:
            enhanced_features: (B, N, D) enhanced target features
        """
        B, N, D = target_features.shape
        _, T_c, _, _ = context_features.shape

        H = W = int(N ** 0.5)
        assert H * W == N, f"Feature map must be square, got {N} patches"

        # Normalize inputs
        target_x = self.norm(target_features)
        context_x = self.norm(context_features.view(B * T_c, N, D)).view(B, T_c, N, D)

        # Apply spatiotemporal attention layers
        enhanced_target = target_x
        for layer in self.layers:
            enhanced_target = layer(enhanced_target, context_x)

        # Residual connection
        enhanced_features = enhanced_target + target_features

        return enhanced_features


# ============================================================================
# Main Model: SwinWaterNetV13
# ============================================================================

class SwinWaterNetV13(nn.Module):
    """
    Swin Water Net v13: Enhanced Spatiotemporal Architecture
    
    Key features:
    1. Target frame extraction using center_frame_index
    2. Temporal context extraction (excluding target frame)
    3. Multi-scale patch embedding with dynamic degree calculation
    4. Spatiotemporal attention with Swin Transformer mechanics
    5. [2,2,6,2] depth structure encoder
    6. Unet-style decoder for 2×256×256 output
    """

    def __init__(self, img_size=256, patch_sizes=[4, 8, 16], in_chans=2, out_chans=2, embed_dim=96,
                 depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], window_size=8,
                 num_frames=48, mlp_ratio=4.0, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1):
        super().__init__()

        self.img_size = img_size
        self.patch_sizes = patch_sizes
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.num_frames = num_frames

        # Multi-scale patch embedding
        self.patch_embed = MultiScalePatchEmbed(
            img_size=img_size, patch_sizes=patch_sizes,
            in_chans=in_chans, embed_dim=embed_dim
        )

        # Enhanced context aggregator
        self.context_aggregator = EnhancedContextAggregator(
            embed_dim=embed_dim, num_heads=num_heads[0],
            window_size=window_size, depths=[2], mlp_ratio=mlp_ratio
        )

        # Enhanced Swin encoder with [2,2,6,2] structure
        self.encoder = EnhancedSwinEncoder(
            embed_dim=embed_dim, depths=depths, num_heads=num_heads,
            window_size=window_size, mlp_ratio=mlp_ratio,
            drop_rate=drop_rate, attn_drop_rate=attn_drop_rate, drop_path_rate=drop_path_rate,
            img_size=img_size, patch_size=min(patch_sizes)  # Use smallest patch size for resolution calculation
        )

        # Unet-style decoder
        final_dim = embed_dim * (2 ** (len(depths) - 1))
        self.decoder = SwinUNetDecoder(
            in_channels=final_dim, img_size=img_size, patch_size=min(patch_sizes),
            num_classes=out_chans, use_checkpoint=False
        )

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def extract_target_and_context_frames(self, video, center_frame_idx):
        """
        Extract target frame and context frames
        
        Args:
            video: (B, T, C, H, W) video sequence
            center_frame_idx: (B,) center frame indices
        Returns:
            target_frames: (B, 1, C, H, W) target frames
            context_frames: (B, T-1, C, H, W) context frames
        """
        B, T, C, H, W = video.shape
        device = video.device

        # Ensure center_frame_idx is in valid range
        center_frame_idx = torch.clamp(center_frame_idx, 0, T - 1)

        # Extract target frames
        batch_indices = torch.arange(B, device=device)
        # 在这里center_frame_idx 索引的真的是dataset中center_frame_idx那帧吗
        target_frames = video[batch_indices, center_frame_idx].unsqueeze(1)  # (B, 1, C, H, W)

        # Extract context frames (excluding target frame)
        all_indices = torch.arange(T, device=device).unsqueeze(0).expand(B, T)  # (B, T)
        center_indices_expanded = center_frame_idx.unsqueeze(1)  # (B, 1)

        # Create mask to exclude target frame
        context_mask = all_indices != center_indices_expanded  # (B, T)

        # Reshape video for easier indexing
        video_flat = video.view(B * T, C, H, W)

        # Create flat indices for context frames
        batch_indices_expanded = torch.arange(B, device=device).unsqueeze(1).expand(B, T)  # (B, T)
        flat_indices = batch_indices_expanded * T + all_indices  # (B, T)

        # Select context frame indices
        context_flat_indices = flat_indices[context_mask]  # (B*(T-1),)

        # Extract context frames
        context_frames_flat = video_flat[context_flat_indices]  # (B*(T-1), C, H, W)
        context_frames = context_frames_flat.view(B, T-1, C, H, W)  # (B, T-1, C, H, W)

        return target_frames, context_frames

    def forward(self, batch):
        """
        Forward pass
        
        Args:
            batch: dict containing:
                - 'input_sequence': (B, T, C, H, W) video sequence
                - 'center_frame_idx': (B,) center frame indices
                - 'occurrence': (B, H, W) water frequency (optional)
        Returns:
            dict with 'inpaint' containing 'logits'
        """
        video = batch['input_sequence']  # (B, T, C, H, W)
        center_frame_idx = batch['center_frame_idx']  # (B,)
        water_frequency = batch.get('occurrence')  # (B, H, W) optional

        B, T = video.shape[:2]

        # Extract target and context frames
        target_frames, context_frames = self.extract_target_and_context_frames(video, center_frame_idx)

        # Expand water frequency to match frame dimensions if provided
        if water_frequency is not None:
            # Expand to match video temporal dimension
            target_freq = water_frequency.unsqueeze(1)  # (B, 1, H, W)
            context_freq = water_frequency.unsqueeze(1).expand(-1, T-1, -1, -1)  # (B, T-1, H, W)
        else:
            target_freq = None
            context_freq = None

        # Multi-scale patch embedding
        target_patches = self.patch_embed(target_frames, target_freq)  # (B, 1, N, D)
        target_patches = target_patches.squeeze(1)  # (B, N, D)

        context_patches = self.patch_embed(context_frames, context_freq)  # (B, T-1, N, D)

        # Enhanced context aggregation
        aggregated_features = self.context_aggregator(target_patches, context_patches)

        # Encoder (with [2,2,6,2] structure)
        encoder_features = self.encoder(aggregated_features)

        # Decoder (Unet-style for 2×256×256 output)
        logits = self.decoder(encoder_features[::-1])

        return {'inpaint': {'logits': logits}}


def create_swin_water_net_v13(config):
    """Create SwinWaterNetV13 model"""
    model_config = config.model.swin_config

    def safe_get(obj, key, default):
        if hasattr(obj, 'get'):
            return obj.get(key, default)
        elif hasattr(obj, key):
            return getattr(obj, key)
        else:
            return default

    model = SwinWaterNetV13(
        img_size=safe_get(model_config, 'img_size', 256),
        patch_sizes=safe_get(model_config, 'patch_sizes', [4, 8, 16]),
        in_chans=safe_get(model_config, 'in_chans', 2),
        out_chans=safe_get(model_config, 'out_chans', 2),
        embed_dim=safe_get(model_config, 'embed_dim', 96),
        depths=safe_get(model_config, 'depths', [2, 2, 6, 2]),
        num_heads=safe_get(model_config, 'num_heads', [3, 6, 12, 24]),
        window_size=safe_get(model_config, 'window_size', 8),
        num_frames=safe_get(config.data, 'num_frames', 48),
        mlp_ratio=safe_get(model_config, 'mlp_ratio', 4.0),
        drop_rate=safe_get(model_config, 'drop_rate', 0.),
        attn_drop_rate=safe_get(model_config, 'attn_drop_rate', 0.),
        drop_path_rate=safe_get(model_config, 'drop_path_rate', 0.1)
    )

    return model


# For compatibility, provide alias
def create_swin_water_net(config):
    """Compatibility alias"""
    return create_swin_water_net_v13(config)


__all__ = [
    'SwinWaterNetV13',
    'create_swin_water_net_v13',
    'create_swin_water_net',
    'MultiScalePatchEmbed',
    'SpatiotemporalWindowAttention',
    'SpatiotemporalSwinBlock',
    'EnhancedSwinEncoder',
    'EnhancedContextAggregator'
]