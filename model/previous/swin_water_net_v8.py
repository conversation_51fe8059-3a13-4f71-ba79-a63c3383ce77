"""
Standard Swin Transformer-based Video Water Body Detection Model with 3D Swin Transformer
Parameters: ~80M, Memory: <40GB

Architecture:
Input: (B, 120, 2, 256, 256) video sequence
    ↓
Temporal Sampling → [16, 16, 16] frames
    ↓
3D Patch Embedding → (B, T, N, D)
    ↓
3D Swin Transformer Encoder → Multi-scale spatiotemporal features
    ↓
Geographic-Temporal Conditioning → Enhanced features
    ↓
U-Net Decoder → Water body segmentation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import math
import logging
from einops import rearrange
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)


from model.decoder_v8 import SwinUNetDecoder
from model.swin_3d_transformer import SwinTransformerEncoder3D
from model.mixed_precision_utils import ensure_dtype_compatibility, initialize_mixed_precision_model


class PatchEmbed(nn.Module):
    """Standard patch embedding for video sequences"""

    def __init__(self, img_size=256, patch_size=4, in_chans=2, embed_dim=96):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2

        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, x):
        """Args: x: (B, T, C, H, W) video sequence
           Returns: x: (B, T, N, D) sequence of embedded patches"""
        B, T, C, H, W = x.shape

        # Reshape to process all frames at once
        x = x.flatten(0, 1)  # (B*T, C, H, W)
        x = self.proj(x)  # (B*T, D, H', W')

        D, H_p, W_p = x.shape[1], x.shape[2], x.shape[3]
        x = x.reshape(B, T, D, H_p, W_p)
        x = x.permute(0, 1, 3, 4, 2)  # (B, T, H', W', D)
        x = x.reshape(B, T, H_p*W_p, D)  # (B, T, N, D)

        # 确保输出类型与输入一致（支持混合精度）
        return self.norm(x)


class WindowAttention(nn.Module):
    """Standard window-based multi-head self attention with relative position bias"""
    
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        # Relative position bias table
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))

        # Generate relative position index
        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing='ij'))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        # QKV projection
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)
        
    def forward(self, x, mask=None):
        """Standard attention computation"""
        B_, N, C = x.shape

        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        # Add relative position bias - 确保类型匹配
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1], self.window_size[0] * self.window_size[1], -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        # 确保bias与attn类型一致
        relative_position_bias = relative_position_bias.to(attn.dtype)
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            # 确保mask与attn类型一致
            mask = mask.to(attn.dtype)
            attn = attn.reshape(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.reshape(-1, self.num_heads, N, N)

        attn = self.softmax(attn)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        return x


def window_partition(x, window_size):
    """Partition input into non-overlapping windows"""
    B, H, W, C = x.shape
    
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    
    if pad_h > 0 or pad_w > 0:
        x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))
        H_padded, W_padded = H + pad_h, W + pad_w
    else:
        H_padded, W_padded = H, W
    
    x = x.reshape(B, H_padded // window_size, window_size, W_padded // window_size, window_size, C)
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().reshape(-1, window_size, window_size, C)
    return windows


def window_reverse(windows, window_size, H, W):
    """Reverse window partition"""
    pad_h = (window_size - H % window_size) % window_size
    pad_w = (window_size - W % window_size) % window_size
    H_padded, W_padded = H + pad_h, W + pad_w
    
    B = int(windows.shape[0] / (H_padded * W_padded / window_size / window_size))
    x = windows.reshape(B, H_padded // window_size, W_padded // window_size, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().reshape(B, H_padded, W_padded, -1)
    
    if pad_h > 0 or pad_w > 0:
        x = x[:, :H, :W, :]
    
    return x


class SwinTransformerBlock(nn.Module):
    """Standard Swin Transformer block with window attention"""
    
    def __init__(self, dim, num_heads, window_size=7, shift_size=0, mlp_ratio=4., qkv_bias=True, 
                 drop=0., attn_drop=0., drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio
        
        if self.window_size <= 0:
            raise ValueError(f"window_size must be positive, got {window_size}")
        
        self.norm1 = norm_layer(dim)
        self.attn = WindowAttention(
            dim, window_size=(self.window_size, self.window_size), num_heads=num_heads,
            qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim), act_layer(), nn.Dropout(drop),
            nn.Linear(mlp_hidden_dim, dim), nn.Dropout(drop)
        )

    def calculate_mask(self, H, W, device, dtype=None):
        img_mask = torch.zeros((1, H, W, 1), device=device, dtype=dtype or torch.float32)
        h_slices = (slice(0, -self.window_size), slice(-self.window_size, -self.shift_size), slice(-self.shift_size, None))
        w_slices = (slice(0, -self.window_size), slice(-self.window_size, -self.shift_size), slice(-self.shift_size, None))
        cnt = 0
        for h in h_slices:
            for w in w_slices:
                img_mask[:, h, w, :] = cnt
                cnt += 1
        mask_windows = window_partition(img_mask, self.window_size)
        mask_windows = mask_windows.reshape(-1, self.window_size * self.window_size)
        attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
        attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(attn_mask == 0, float(0.0))
        return attn_mask

    def forward(self, x, H, W):
        B, L, C = x.shape
        assert L == H * W, f"Input feature has wrong size, expected {H*W}, got {L}"

        shortcut = x
        x = self.norm1(x)
        x = x.reshape(B, H, W, C)

        # Cyclic shift
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        # Partition windows
        x_windows = window_partition(shifted_x, self.window_size)
        x_windows = x_windows.reshape(-1, self.window_size * self.window_size, C)

        # 动态生成mask
        attn_mask = None
        if self.shift_size > 0:
            attn_mask = self.calculate_mask(H, W, x.device, x.dtype)

        # W-MSA/SW-MSA
        attn_windows = self.attn(x_windows, mask=attn_mask)

        # Merge windows
        attn_windows = attn_windows.reshape(-1, self.window_size, self.window_size, C)
        shifted_x = window_reverse(attn_windows, self.window_size, H, W)

        # Reverse cyclic shift
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x

        x = x.reshape(B, H * W, C)

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class PatchMerging(nn.Module):
    """Patch merging layer for downsampling"""
    
    def __init__(self, input_resolution, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)
    
    def forward(self, x):
        """Forward pass with patch merging"""
        H, W = self.input_resolution
        B, L, C = x.shape
        assert L == H * W, f"Input feature has wrong size, expected {H*W}, got {L}"
        assert H % 2 == 0 and W % 2 == 0, f"x size ({H}*{W}) are not even."
        
        x = x.reshape(B, H, W, C)
        
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C
        
        x = self.norm(x)
        x = self.reduction(x)
        
        return x


class SwinTransformerStage(nn.Module):
    """A stage of Swin Transformer with multiple blocks"""
    
    def __init__(self, dim, input_resolution, depth, num_heads, window_size, mlp_ratio=4.,
                 qkv_bias=True, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm,
                 downsample=None, use_checkpoint=False):
        super().__init__()
        self.dim = dim
        self.input_resolution = input_resolution
        self.depth = depth
        self.use_checkpoint = use_checkpoint
        
        # Build blocks
        self.blocks = nn.ModuleList([
            SwinTransformerBlock(
                dim=dim,
                num_heads=num_heads, window_size=window_size,
                shift_size=0 if (i % 2 == 0) else window_size // 2,
                mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
                drop=drop, attn_drop=attn_drop,
                drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path,
                norm_layer=norm_layer)
            for i in range(depth)])
        
        # Patch merging layer
        if downsample is not None:
            self.downsample = downsample(input_resolution, dim=dim, norm_layer=norm_layer)
        else:
            self.downsample = None
    
    def forward(self, x):
        """Forward pass through the stage"""
        for blk in self.blocks:
            if self.use_checkpoint:
                x = torch.utils.checkpoint.checkpoint(blk, x, self.input_resolution[0], self.input_resolution[1])
            else:
                x = blk(x, self.input_resolution[0], self.input_resolution[1])
        
        if self.downsample is not None:
            x = self.downsample(x)
        
        return x


class SwinTransformerEncoder(nn.Module):
    """Swin Transformer encoder with multiple stages"""
    
    def __init__(self, img_size=256, patch_size=4, in_chans=2, embed_dim=96, depths=[2, 2, 6, 2],
                 num_heads=[3, 6, 12, 24], window_size=7, mlp_ratio=4., qkv_bias=True,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1, norm_layer=nn.LayerNorm,
                 patch_norm=True, use_checkpoint=False):
        super().__init__()
        
        self.num_layers = len(depths)
        self.embed_dim = embed_dim
        self.patch_norm = patch_norm
        self.num_features = int(embed_dim * 2 ** (self.num_layers - 1))
        self.mlp_ratio = mlp_ratio
        
        # Split image into non-overlapping patches
        self.patch_embed = PatchEmbed(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, embed_dim=embed_dim)
        num_patches = self.patch_embed.num_patches
        patches_resolution = [img_size // patch_size, img_size // patch_size]
        self.patches_resolution = patches_resolution
        
        # Stochastic depth
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        # Build stages
        self.layers = nn.ModuleList()
        for i_layer in range(self.num_layers):
            layer = SwinTransformerStage(
                dim=int(embed_dim * 2 ** i_layer),
                input_resolution=(patches_resolution[0] // (2 ** i_layer),
                                patches_resolution[1] // (2 ** i_layer)),
                depth=depths[i_layer],
                num_heads=num_heads[i_layer],
                window_size=window_size,
                mlp_ratio=self.mlp_ratio,
                qkv_bias=qkv_bias,
                drop=drop_rate, attn_drop=attn_drop_rate,
                drop_path=dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])],
                norm_layer=norm_layer,
                downsample=PatchMerging if (i_layer < self.num_layers - 1) else None,
                use_checkpoint=use_checkpoint)
            self.layers.append(layer)
                
    def forward(self, x):
        """Forward pass through encoder stages"""
        # x: (B, T, N, D) -> process each temporal frame
        B, T, N, D = x.shape
        features = []
        
        # Process each temporal frame through the encoder
        for t in range(T):
            x_t = x[:, t, :, :]  # (B, N, D)
            
            # Pass through all stages
            stage_features = []
            for i, layer in enumerate(self.layers):
                x_t = layer(x_t)
                stage_features.append(x_t)
            
            # Collect features from all stages
            features.append(stage_features)
        
        # Aggregate temporal features
        # Convert to spatial format for decoder
        spatial_features = []
        for stage_idx in range(len(self.layers)):
            stage_feat = torch.stack([features[t][stage_idx] for t in range(T)], dim=1)  # (B, T, L, D)
            
            # Temporal aggregation (mean pooling)
            stage_feat = stage_feat.mean(dim=1)  # (B, L, D)
            
            # Convert to spatial format
            H = W = int(math.sqrt(stage_feat.shape[1]))
            if H * W == stage_feat.shape[1]:
                spatial_feat = stage_feat.view(B, H, W, -1).permute(0, 3, 1, 2)  # (B, D, H, W)
                spatial_features.append(spatial_feat)
            else:
                # Handle non-square features
                spatial_feat = stage_feat.permute(0, 2, 1).unsqueeze(-1)  # (B, D, L, 1)
                spatial_features.append(spatial_feat)
        
        return spatial_features


class GeoTemporalEmbedding(nn.Module):
    """Geographic and temporal embedding module"""
    
    def __init__(self, embed_dim=96, num_freq_bands=6):
        super().__init__()
        self.embed_dim = embed_dim
        
        # Frequency bands for positional encoding
        self.register_buffer('freq_bands', 2.0 ** torch.linspace(0, num_freq_bands-1, num_freq_bands))
        
        # Geographic features (lon, lat)
        geo_fourier_dim = 4 * num_freq_bands
        
        # Temporal features (year, month)
        temporal_fourier_dim = 2 * num_freq_bands
        year_embed_dim = 16
        
        self.year_embed = nn.Embedding(200, year_embed_dim)
        
        # Combine all features
        total_dim = geo_fourier_dim + temporal_fourier_dim + year_embed_dim
        self.projector = nn.Sequential(
            nn.Linear(total_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU()
        )
        
        # FiLM-style modulation
        self.scale_shift = nn.Linear(embed_dim, embed_dim * 2)
    
    def forward(self, lon, lat, year, month):
        """Encode geographic and temporal features"""
        B = lon.shape[0]

        # 确保输入为float32进行计算
        lon = lon.float()
        lat = lat.float()
        year = year.float()
        month = month.float()

        # Geographic features
        lon_scaled = lon / 180.0 * math.pi
        lat_scaled = lat / 90.0 * math.pi

        freq_bands_expanded = self.freq_bands.unsqueeze(0)
        lon_expanded = lon_scaled.unsqueeze(1)
        lat_expanded = lat_scaled.unsqueeze(1)

        lon_freq = lon_expanded * freq_bands_expanded
        lat_freq = lat_expanded * freq_bands_expanded

        lon_sin = torch.sin(lon_freq)
        lon_cos = torch.cos(lon_freq)
        lat_sin = torch.sin(lat_freq)
        lat_cos = torch.cos(lat_freq)

        geo_features = torch.cat([lon_sin, lon_cos, lat_sin, lat_cos], dim=1)

        # Temporal features
        month_angle = (month - 1) / 12.0 * 2 * math.pi
        month_expanded = month_angle.unsqueeze(1)
        month_freq = month_expanded * freq_bands_expanded
        month_sin = torch.sin(month_freq)
        month_cos = torch.cos(month_freq)
        temporal_features = torch.cat([month_sin, month_cos], dim=1)

        # Year embedding
        year_idx = torch.clamp(year - 1900, 0, 199).long()
        year_features = self.year_embed(year_idx)

        # Combine and project
        combined = torch.cat([geo_features, temporal_features, year_features], dim=-1)
        return self.projector(combined)
    
    def modulate(self, x):
        """Apply FiLM-style modulation to features"""
        scale_shift = self.scale_shift(x)
        scale, shift = scale_shift.chunk(2, dim=-1)
        scale = 1 + 0.1 * torch.tanh(scale)
        return scale, shift


class SwinWaterNet(nn.Module):
    """Standard Swin Transformer-based video water body detection model"""
    
    def __init__(self, img_size=256, patch_size=4, in_chans=2, out_chans=2, num_frames=48,
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], window_size=7, mlp_ratio=4.,
                 qkv_bias=True, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, use_checkpoint=False, temporal_patch_size=2):
        super().__init__()
        
        self.num_frames = num_frames
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.out_chans = out_chans
        self.embed_dim = embed_dim
        self.depths = depths
        self.num_heads = num_heads
        self.window_size = window_size
        self.use_checkpoint = use_checkpoint
        self.temporal_patch_size = temporal_patch_size
        
        # Patch embedding
        self.patch_embed = PatchEmbed(
            img_size=self.img_size, patch_size=self.patch_size,
            in_chans=self.in_chans, embed_dim=self.embed_dim
        )
        
        # 3D Swin Transformer encoder
        self.encoder = SwinTransformerEncoder3D(
            img_size=self.img_size, patch_size=self.patch_size, in_chans=self.in_chans,
            embed_dim=self.embed_dim, depths=self.depths, num_heads=self.num_heads,
            window_size=(2, self.window_size, self.window_size),  # 3D window size (T, H, W)
            mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
            drop_rate=drop_rate, attn_drop_rate=attn_drop_rate, drop_path_rate=drop_path_rate,
            norm_layer=norm_layer, use_checkpoint=self.use_checkpoint,
            temporal_patch_size=self.temporal_patch_size, initial_temporal_length=num_frames
        )
        
        # Geographic temporal encoding
        self.geo_temporal_encoder = GeoTemporalEmbedding(embed_dim=self.embed_dim)
        
        # Decoder
        final_dim = self.embed_dim * (2 ** (len(self.depths) - 1))
        self.decoder = SwinUNetDecoder(
            in_channels=final_dim,
            img_size=self.img_size, patch_size=self.patch_size,
            num_classes=self.out_chans, use_checkpoint=self.use_checkpoint
        )
        
        self.apply(self._init_weights)
        
    def _init_weights(self, m):
        """改进的权重初始化方法 - 修复全零输出问题"""
        if isinstance(m, nn.Linear):
            # 使用更保守的初始化，避免梯度消失
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            # LayerNorm的标准初始化
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.GroupNorm):
            # GroupNorm的标准初始化
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            # 卷积层使用更保守的初始化
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.ConvTranspose2d):
            # 转置卷积层使用更保守的初始化
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.Conv3d):
            # 3D卷积层初始化
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.Embedding):
            # 嵌入层使用正态分布初始化
            nn.init.trunc_normal_(m.weight, std=0.02)
            
    def forward(self, batch):
        """Forward pass"""
        x = batch['input_sequence']
        center_frame_idx = batch.get('center_frame_idx', None)
        
        B, T, _, _, _ = x.shape
        device = x.device
        
        # Normalize center_frame_idx
        if center_frame_idx is None:
            center_frame_idx = torch.full((B,), T // 2, device=device, dtype=torch.long)
        elif isinstance(center_frame_idx, (int, float)):
            center_frame_idx = torch.tensor([int(center_frame_idx)] * B, device=device, dtype=torch.long)
        elif center_frame_idx.numel() == 1 and B > 1:
            center_frame_idx = center_frame_idx.expand(B)
        
        # Get water frequency
        water_frequency = batch.get('occurrence', None)

        # Use uniform sampling to maintain temporal coverage
        if T > self.num_frames:
            # Uniform sampling indices
            indices = torch.linspace(0, T-1, self.num_frames, dtype=torch.long, device=device)
            x_sampled = x[:, indices]  # (B, 48, C, H, W)
        else:
            x_sampled = x

        # Direct patch embedding
        x_embed = self.patch_embed(x_sampled)  # (B, T_sampled, N, D)
        x_concat = x_embed
        
        # Geographic temporal encoding
        geo_temporal_encoding = self.geo_temporal_encoder(
            batch['tile_lon'], batch['tile_lat'], batch['year'], batch['month']
        )
        
        # Add geographic encoding to features
        B_f, T_total, N_f, D_f = x_concat.shape
        geo_encoding_expanded = geo_temporal_encoding.unsqueeze(1).unsqueeze(1).expand(
            B_f, T_total, N_f, D_f
        )
        # 确保类型匹配
        geo_encoding_expanded = geo_encoding_expanded.to(x_concat.dtype)
        x_concat = x_concat + 0.1 * geo_encoding_expanded
        
        # Encode through 3D Swin Transformer
        # 3D encoder directly processes spatiotemporal features
        encoder_features = self.encoder(x_concat)
        # Decode using U-Net style decoder
        logits = self.decoder(
            encoder_features[::-1], water_frequency
        )

        return {'inpaint': {'logits': logits}}


def create_swin_water_net(config):
    """Create SwinWaterNet from configuration"""
    if hasattr(config, 'model'):
        model_config = config.model
    else:
        model_config = config.get('model', {})
    
    if hasattr(model_config, 'swin_config'):
        swin_config = model_config.swin_config
    else:
        swin_config = model_config.get('swin_config', {})
    
    if hasattr(model_config, 'get'):
        use_checkpoint = model_config.get('use_gradient_checkpoint', False)
    else:
        use_checkpoint = getattr(model_config, 'use_gradient_checkpoint', False)
        
    if isinstance(swin_config, dict):

        num_frames = swin_config.get('num_frames', 48)
        embed_dim = swin_config.get('embed_dim', 96)
        patch_size = swin_config.get('patch_size', 4)
        window_sizes = swin_config.get('window_sizes', [7, 7, 7, 7])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes
        depths = swin_config.get('depths', [2, 2, 6, 2])
        num_heads = swin_config.get('num_heads', [3, 6, 12, 24])
        img_size = swin_config.get('img_size', 256)
        in_chans = swin_config.get('in_chans', 2)
        out_chans = swin_config.get('out_chans', 2)
        temporal_patch_size = swin_config.get('temporal_patch_size', 2)
    else:

        num_frames = getattr(swin_config, 'num_frames', 48)
        embed_dim = getattr(swin_config, 'embed_dim', 96)
        patch_size = getattr(swin_config, 'patch_size', 4)
        window_sizes = getattr(swin_config, 'window_sizes', [7, 7, 7, 7])
        window_size = window_sizes[0] if isinstance(window_sizes, list) else window_sizes
        depths = getattr(swin_config, 'depths', [2, 2, 6, 2])
        num_heads = getattr(swin_config, 'num_heads', [3, 6, 12, 24])
        img_size = getattr(swin_config, 'img_size', 256)
        in_chans = getattr(swin_config, 'in_chans', 2)
        out_chans = getattr(swin_config, 'out_chans', 2)
        temporal_patch_size = getattr(swin_config, 'temporal_patch_size', 2)
    
    model = SwinWaterNet(
        img_size=img_size, patch_size=patch_size, in_chans=in_chans, out_chans=out_chans,
        embed_dim=embed_dim, depths=depths, num_heads=num_heads, window_size=window_size,
        num_frames=num_frames, use_checkpoint=use_checkpoint,
        temporal_patch_size=temporal_patch_size
    )
    
    return model