"""
Swin Transformer U-Net Decoder for Video Water Body Detection
Designed to work with Swin Transformer encoder features
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import logging

logger = logging.getLogger(__name__)


class Decoder(nn.Module):
    """
    U-Net style decoder for Swin Transformer features
    Processes multi-scale encoder features and outputs segmentation
    """
    
    def __init__(self, img_size=256, num_classes=2, embed_dim=96, depths=[2, 2, 6, 2]):
        super().__init__()
        self.img_size = img_size
        self.up_blocks = nn.ModuleList()
        self.feature_dims = None  # 运行时推断
        self.num_classes = num_classes
        self._final_upsample_inited = False
        # 用占位层，后续forward时替换
        self.final_upsample = nn.Sequential(
            nn.Identity(),  # 占位，forward时替换为ConvTranspose2d
            nn.GroupNorm(8, 64),
            nn.<PERSON>L<PERSON>(),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.GroupNorm(4, 32),
            nn.GELU(),
            nn.ConvTranspose2d(32, 16, 4, 2, 1),
            nn.GroupNorm(2, 16),
            nn.GELU(),
            nn.Conv2d(16, 16, 3, padding=1),
            nn.GroupNorm(2, 16),
            nn.GELU()
        )
        self.final_conv = nn.Sequential(
            nn.Conv2d(16, 8, 3, padding=1),
            nn.GroupNorm(4, 8),
            nn.GELU(),
            nn.Conv2d(8, num_classes, 1)
        )
        
        # 初始化权重
        # self._init_weights()
        # 确保所有层权重为float32
        # self._ensure_float32_weights()

    # def _init_weights(self):
    #     """初始化解码器权重"""
    #     for module in self.modules():
    #         if isinstance(module, (nn.Conv2d, nn.ConvTranspose2d)):
    #             nn.init.trunc_normal_(module.weight, std=0.02)
    #             if module.bias is not None:
    #                 nn.init.constant_(module.bias, 0)
    #         elif isinstance(module, nn.GroupNorm):
    #             nn.init.constant_(module.weight, 1.0)
    #             nn.init.constant_(module.bias, 0)

    # def _ensure_float32_weights(self):
    #     """确保所有模型权重为float32"""
    #     for module in self.modules():
    #         if hasattr(module, 'weight') and module.weight is not None:
    #             module.weight.data = module.weight.data.float()
    #         if hasattr(module, 'bias') and module.bias is not None:
    #             module.bias.data = module.bias.data.float()

    def _build_up_blocks(self, encoder_shapes):
        # encoder_shapes: [(C0, H0, W0), (C1, H1, W1), ...]，顺序为deep到shallow
        self.up_blocks = nn.ModuleList()
        for i in range(len(encoder_shapes) - 1):
            in_ch = encoder_shapes[i][0]
            skip_ch = encoder_shapes[i+1][0]
            up_block = nn.Sequential(
                nn.ConvTranspose2d(in_ch, skip_ch, 4, 2, 1),
                nn.GroupNorm(max(1, skip_ch // 16), skip_ch),
                nn.GELU(),
                nn.Conv2d(skip_ch + skip_ch, skip_ch, 3, padding=1),
                nn.GroupNorm(max(1, skip_ch // 16), skip_ch),
                nn.GELU()
            )
            # 移动到正确设备并确保所有层的权重为float32
            up_block = up_block.to(next(self.parameters()).device)
            for layer in up_block:
                if hasattr(layer, 'weight') and layer.weight is not None:
                    layer.weight.data = layer.weight.data.float()
                if hasattr(layer, 'bias') and layer.bias is not None:
                    layer.bias.data = layer.bias.data.float()
            self.up_blocks.append(up_block)
        # 标记final_upsample未初始化
        self._final_upsample_inited = False
    
    def forward(self, encoder_features, water_frequency=None):
        # encoder_features: [deepest, ..., shallowest]
        # 动态构建up_blocks
        if (self.feature_dims is None) or (len(self.up_blocks) != len(encoder_features) - 1):
            encoder_shapes = [f.shape[1:] for f in encoder_features]
            self._build_up_blocks(encoder_shapes)
            self.feature_dims = [f.shape[1] for f in encoder_features]

        x = encoder_features[0]
        skip_features = encoder_features[1:]
        for i, up_block in enumerate(self.up_blocks):
            layers = list(up_block.children())
            x = layers[0](x)
            x = layers[1](x)
            x = layers[2](x)
            skip_feature = skip_features[i]
            if x.shape[2:] != skip_feature.shape[2:]:
                skip_feature = F.interpolate(skip_feature, size=x.shape[2:], mode='bilinear', align_corners=False)
            x = torch.cat([x, skip_feature], dim=1)
            x = layers[3](x)
            x = layers[4](x)
            x = layers[5](x)
        # 动态替换final_upsample第一层
        if not self._final_upsample_inited:
            in_ch = x.shape[1]
            # 创建新层并确保权重类型正确
            new_layer = nn.ConvTranspose2d(in_ch, 64, 4, 2, 1)
            new_layer = new_layer.to(x.device)
            # 确保权重为float32，让autocast处理输入类型转换
            new_layer.weight.data = new_layer.weight.data.float()
            if new_layer.bias is not None:
                new_layer.bias.data = new_layer.bias.data.float()
            self.final_upsample[0] = new_layer
            self._final_upsample_inited = True
        x = self.final_upsample(x)
        if x.shape[2:] != (self.img_size, self.img_size):
            x = F.interpolate(x, size=(self.img_size, self.img_size), mode='bilinear', align_corners=False)
        # 最终预测层
        logits = self.final_conv(x)

        # 数值稳定性处理
        logits = torch.nan_to_num(logits, nan=0.0, posinf=10.0, neginf=-10.0)
        logits = torch.clamp(logits, min=-10.0, max=10.0)

        return logits