"""
Flow utilities for ProPainter-based model
简化的光流工具函数，用于支持ProPainter架构
"""

import torch
import torch.nn.functional as F


def flow_warp(x, flow, interpolation='bilinear', padding_mode='zeros', align_corners=True):
    """
    使用光流对特征进行变形
    
    Args:
        x: (B, C, H, W) 输入特征
        flow: (B, H, W, 2) 光流，最后一维为 (dx, dy)
        interpolation: 插值方法
        padding_mode: 填充模式
        align_corners: 是否对齐角点
        
    Returns:
        warped_x: (B, C, H, W) 变形后的特征
    """
    B, C, H, W = x.size()
    
    # 创建网格
    grid_y, grid_x = torch.meshgrid(
        torch.arange(0, H, dtype=x.dtype, device=x.device),
        torch.arange(0, W, dtype=x.dtype, device=x.device),
        indexing='ij'
    )
    grid = torch.stack((grid_x, grid_y), 2).float()  # (H, W, 2)
    grid = grid.unsqueeze(0).repeat(B, 1, 1, 1)  # (B, H, W, 2)
    
    # 应用光流
    vgrid = grid + flow
    
    # 归一化到 [-1, 1]
    vgrid_x = 2.0 * vgrid[:, :, :, 0] / max(W - 1, 1) - 1.0
    vgrid_y = 2.0 * vgrid[:, :, :, 1] / max(H - 1, 1) - 1.0
    vgrid_scaled = torch.stack((vgrid_x, vgrid_y), dim=3)
    
    # 采样
    output = F.grid_sample(
        x, vgrid_scaled, 
        mode=interpolation, 
        padding_mode=padding_mode, 
        align_corners=align_corners
    )
    
    return output


def ternary_loss2(img1, img2, mask, missing_mask):
    """
    计算三元损失
    
    Args:
        img1: (B, C, H, W) 第一张图像
        img2: (B, C, H, W) 第二张图像  
        mask: (B, 1, H, W) 掩码
        missing_mask: (B, 1, H, W) 缺失掩码
        
    Returns:
        loss: 标量损失值
    """
    # 简化的三元损失实现
    diff = torch.abs(img1 - img2)
    
    # 应用掩码
    if mask is not None:
        diff = diff * mask
    
    if missing_mask is not None:
        diff = diff * (1 - missing_mask)
    
    # 计算平均损失
    loss = torch.mean(diff)
    
    return loss


def compute_flow_magnitude(flow):
    """
    计算光流幅度
    
    Args:
        flow: (B, 2, H, W) 光流
        
    Returns:
        magnitude: (B, 1, H, W) 光流幅度
    """
    magnitude = torch.sqrt(flow[:, 0:1] ** 2 + flow[:, 1:2] ** 2)
    return magnitude


def flow_consistency_check(flow_fw, flow_bw, alpha1=0.01, alpha2=0.5):
    """
    光流一致性检查
    
    Args:
        flow_fw: (B, 2, H, W) 前向光流
        flow_bw: (B, 2, H, W) 后向光流
        alpha1: 阈值参数1
        alpha2: 阈值参数2
        
    Returns:
        consistency_mask: (B, 1, H, W) 一致性掩码
    """
    # 使用后向光流变形前向光流
    flow_bw_warped = flow_warp(flow_bw, flow_fw.permute(0, 2, 3, 1))
    
    # 计算差异
    flow_diff = flow_fw + flow_bw_warped
    
    # 计算幅度
    mag_sq_fw = torch.sum(flow_fw ** 2, dim=1, keepdim=True)
    mag_sq_bw = torch.sum(flow_bw_warped ** 2, dim=1, keepdim=True)
    mag_sq = mag_sq_fw + mag_sq_bw
    
    # 计算阈值
    occ_thresh = alpha1 * mag_sq + alpha2
    
    # 一致性检查
    diff_sq = torch.sum(flow_diff ** 2, dim=1, keepdim=True)
    consistency_mask = (diff_sq < occ_thresh).float()
    
    return consistency_mask


def create_zero_flow(shape, device):
    """
    创建零光流
    
    Args:
        shape: (B, T, H, W) 或 (B, H, W)
        device: 设备
        
    Returns:
        zero_flow: 零光流张量
    """
    if len(shape) == 4:
        B, T, H, W = shape
        return torch.zeros(B, T, 2, H, W, device=device)
    elif len(shape) == 3:
        B, H, W = shape
        return torch.zeros(B, 2, H, W, device=device)
    else:
        raise ValueError(f"Unsupported shape: {shape}")


def estimate_simple_flow(img1, img2):
    """
    简单的光流估计（基于梯度）
    
    Args:
        img1: (B, C, H, W) 第一帧
        img2: (B, C, H, W) 第二帧
        
    Returns:
        flow: (B, 2, H, W) 估计的光流
    """
    # 转换为灰度图
    if img1.size(1) > 1:
        img1_gray = torch.mean(img1, dim=1, keepdim=True)
        img2_gray = torch.mean(img2, dim=1, keepdim=True)
    else:
        img1_gray = img1
        img2_gray = img2
    
    # 计算时间梯度
    dt = img2_gray - img1_gray
    
    # 计算空间梯度
    dx_kernel = torch.tensor([[-1, 0, 1]], dtype=img1.dtype, device=img1.device).view(1, 1, 1, 3)
    dy_kernel = torch.tensor([[-1], [0], [1]], dtype=img1.dtype, device=img1.device).view(1, 1, 3, 1)
    
    dx = F.conv2d(img1_gray, dx_kernel, padding=(0, 1))
    dy = F.conv2d(img1_gray, dy_kernel, padding=(1, 0))
    
    # 简单的光流估计（Lucas-Kanade方法的简化版本）
    # 这里使用一个非常简化的版本
    flow_x = -dt * dx / (dx ** 2 + dy ** 2 + 1e-6)
    flow_y = -dt * dy / (dx ** 2 + dy ** 2 + 1e-6)
    
    flow = torch.cat([flow_x, flow_y], dim=1)
    
    return flow
