"""
Inpainting-Only Loss Function for SwinWaterNet with Water Frequency Weighting

Combines DICE and BCE losses with frequency-based weighting for better performance
on water body inpainting tasks.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
import logging
import numpy as np
from evaluation.metrics import MetricsCalculator

logger = logging.getLogger(__name__)


class DiceLoss(nn.Module):
    """Dice loss for binary segmentation"""

    def __init__(self, smooth=1e-5):
        super().__init__()
        self.smooth = smooth

    def forward(self, predictions: torch.Tensor, targets: torch.Tensor,
                weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            predictions: (B, H, W) - Predicted probabilities
            targets: (B, H, W) - Binary ground truth
            weight: (B, H, W) - Optional weight for each pixel
        """
        predictions = torch.clamp(predictions, 1e-7, 1.0 - 1e-7)
        predictions = predictions.view(-1)
        targets = targets.view(-1)

        if weight is not None:
            weight = weight.view(-1)
            intersection = (predictions * targets * weight).sum()
            pred_sum = (predictions * weight).sum()
            target_sum = (targets * weight).sum()
        else:
            intersection = (predictions * targets).sum()
            pred_sum = predictions.sum()
            target_sum = targets.sum()

        denominator = pred_sum + target_sum + self.smooth
        numerator = 2.0 * intersection + self.smooth

        dice = numerator / denominator
        dice_loss = 1 - dice

        return dice_loss


class AdaptiveDiceLoss(nn.Module):
    """自适应DICE损失 - 根据水体大小调整权重"""

    def __init__(self, smooth=1e-6):
        super().__init__()
        self.smooth = smooth

    def forward(self, predictions: torch.Tensor, targets: torch.Tensor,
                mask: torch.Tensor, frequency: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            predictions: (B, C, H, W) - Raw logits from model
            targets: (B, H, W) - Ground truth labels
            mask: (B, H, W) - Valid region mask
            frequency: Optional frequency tensor (for compatibility)
        """
        water_probs = torch.softmax(predictions, dim=1)[:, 1, :, :]  # (B, H, W)
        target_binary = (targets == 1).float()  # (B, H, W)

        # 应用mask
        water_probs = water_probs * mask.float()
        target_binary = target_binary * mask.float()

        # 计算每个样本的水体比例，用于自适应权重
        water_ratios = target_binary.sum(dim=[1, 2]) / mask.float().sum(dim=[1, 2]).clamp(min=1)  # (B,)

        # 自适应权重：小水体给更高权重
        adaptive_weights = 1.0 / (water_ratios + 0.1)  # (B,)
        adaptive_weights = adaptive_weights / adaptive_weights.mean()  # 归一化

        # 计算DICE损失
        intersection = (water_probs * target_binary).sum(dim=[1, 2])  # (B,)
        union = water_probs.sum(dim=[1, 2]) + target_binary.sum(dim=[1, 2])  # (B,)

        dice_scores = (2 * intersection + self.smooth) / (union + self.smooth)  # (B,)
        dice_loss = 1 - dice_scores  # (B,)

        # 应用自适应权重
        weighted_dice_loss = (dice_loss * adaptive_weights).mean()

        return weighted_dice_loss


class FocalLoss(nn.Module):
    """
    Independent Focal Loss implementation with mask support
    Computes focal loss directly from probabilities without relying on cross-entropy
    """

    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean', smooth=1e-7):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.smooth = smooth

    def forward(self, predictions: torch.Tensor, targets: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Independent Focal Loss computation with mask support

        Args:
            predictions: (B, C, H, W) - Raw logits from model
            targets: (B, H, W) - Ground truth labels (0 or 1 for binary)
            mask: (B, H, W) - Optional mask to specify valid regions
        """
        # Convert logits to probabilities
        probs = torch.softmax(predictions, dim=1)  # (B, C, H, W)

        # Get water class probabilities (class 1)
        water_probs = probs[:, 1, :, :]  # (B, H, W)

        # Clamp probabilities to avoid numerical issues
        water_probs = torch.clamp(water_probs, self.smooth, 1.0 - self.smooth)

        # Convert targets to binary (ensure 0 or 1)
        binary_targets = (targets > 0.5).float()  # (B, H, W)

        # Compute focal loss components
        # For positive samples (water): -alpha * (1-p)^gamma * log(p)
        # For negative samples (non-water): -alpha * p^gamma * log(1-p)
        pos_loss = -self.alpha * (1 - water_probs) ** self.gamma * torch.log(water_probs)
        neg_loss = -self.alpha * water_probs ** self.gamma * torch.log(1 - water_probs)

        # Select appropriate loss based on target
        focal_loss = binary_targets * pos_loss + (1 - binary_targets) * neg_loss

        # Apply mask if provided
        if mask is not None:
            focal_loss = focal_loss * mask.float()

            # Apply reduction with mask consideration
            if self.reduction == 'mean':
                mask_sum = torch.clamp(mask.float().sum(), min=1e-6)
                return focal_loss.sum() / mask_sum
            elif self.reduction == 'sum':
                return focal_loss.sum()
            else:
                return focal_loss
        else:
            # Apply reduction without mask
            if self.reduction == 'mean':
                return focal_loss.mean()
            elif self.reduction == 'sum':
                return focal_loss.sum()
            else:
                return focal_loss


class BCELoss(nn.Module):
    """
    Binary Cross-Entropy Loss with mask support
    """

    def __init__(self, reduction='mean'):
        super().__init__()
        self.reduction = reduction

    def forward(self, predictions: torch.Tensor, targets: torch.Tensor,
                mask: Optional[torch.Tensor] = None,
                weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        BCE Loss computation with mask and weight support

        Args:
            predictions: (B, C, H, W) - Raw logits from model
            targets: (B, H, W) - Ground truth labels
            mask: (B, H, W) - Optional mask to specify valid regions
            weight: (B, H, W) - Optional pixel weights
        """
        if mask is not None:
            # Extract valid predictions and targets
            target_ce = targets.long()[mask]
            pred_flat = predictions.permute(0, 2, 3, 1).reshape(-1, predictions.size(1))
            pred_valid = pred_flat[mask.reshape(-1)]

            # Compute BCE loss
            bce_loss_all = F.cross_entropy(pred_valid, target_ce, reduction='none')

            # Apply pixel weights if provided
            if weight is not None:
                pixel_weight = weight[mask].float()
                pixel_weight_sum = torch.clamp(pixel_weight.sum(), min=1e-6)

                if self.reduction == 'mean':
                    return (bce_loss_all * pixel_weight).sum() / pixel_weight_sum
                elif self.reduction == 'sum':
                    return (bce_loss_all * pixel_weight).sum()
                else:
                    return bce_loss_all * pixel_weight
            else:
                if self.reduction == 'mean':
                    return bce_loss_all.mean()
                elif self.reduction == 'sum':
                    return bce_loss_all.sum()
                else:
                    return bce_loss_all
        else:
            # No mask, compute on full tensors
            bce_loss = F.cross_entropy(predictions, targets.long(), reduction=self.reduction)
            return bce_loss

    
class Loss(nn.Module):
    """
    Inpainting loss combining BCE, DICE, and Focal losses with frequency-based weighting
    """

    def __init__(self,
                 bce_weight=0.3,
                 dice_weight=0.4,
                ):
        super().__init__()

        # Normalize weights to sum to 1
        total_weight = bce_weight + dice_weight
        self.bce_weight = bce_weight / total_weight
        self.dice_weight = dice_weight / total_weight

        # Loss components
        self.dice = DiceLoss()
        self.bce = BCELoss(reduction='mean')

        # Metrics calculation
        self.metrics_calculator = MetricsCalculator()

    def forward(self, outputs: Dict, batch: Dict, mode = 'train') -> Tuple[torch.Tensor, Dict]:
        """
        Compute inpainting loss
        
        Args:
            outputs: Model outputs containing 'inpaint' predictions
            batch: Batch data containing ground truth, masks, and frequency info
            stage: Training stage (for compatibility)
        """                
        # Extract data
        target = batch['ground_truth']  # (B, H, W)

        mask = batch['missing_mask']
            
        if mask.sum() == 0:
            raise ValueError("Missing mask is all zeros")
        
        # Get water frequency - always needed for metrics calculation
        water_frequency = batch.get('occurrence')
        
        # Get predictions
        pred_logits = outputs['inpaint']['logits']  # (B, 2, H, W)
        
        # Compute main inpainting loss (Cross-Entropy + DICE)
        inpaint_loss = self._calculate_loss(
            pred_logits, target, mask
        )
        
        total_loss = inpaint_loss
        
        if mode == 'train':
            return total_loss
        elif mode == 'eval':
            metrics = self._calculate_metrics(pred_logits, target, mask, water_frequency)
            return total_loss, metrics
        else:
            raise ValueError(f"Unknown mode {self.mode}")
        
        
    def _calculate_loss(self, predictions: torch.Tensor, targets: torch.Tensor,
                        mask: torch.Tensor) -> Tuple[torch.Tensor]:
        """
        Compute weighted BCE + DICE + Focal loss combination
        """
        predictions = predictions.float()
        targets = targets.float()
        mask = mask.bool()

        # 1. Binary Cross-Entropy Loss (only compute if weight > 0)
        bce_loss = self.bce(predictions, targets, mask)

        # 2. DICE Loss
        # Always compute water_probs for metrics calculation
        water_probs = torch.softmax(predictions, dim=1)[:, 1, :, :].float()

        # Use traditional DiceLoss which takes probabilities
        binary_target = (targets == 1).float()
        dice_loss = self.dice(
            water_probs * mask,
            binary_target * mask,
        )

        # Combine all three losses
        combined_loss = (self.bce_weight * bce_loss +
                        self.dice_weight * dice_loss
        )

        return combined_loss
    
    def _calculate_metrics(self, predictions: torch.Tensor, targets: torch.Tensor,
                          mask: torch.Tensor, water_frequency: Optional[torch.Tensor]) -> Dict:
        """
        Calculate metrics for inpainting task
        """
        # Compute metrics
        with torch.no_grad():
            water_probs = torch.softmax(predictions, dim=1)[:, 1, :, :].float()
            pred_binary = (water_probs > 0.5).bool()

            target_bool = (targets == 1).bool()
            valid_mask_bool = mask.bool()

            eval_metrics = self.metrics_calculator.calculate_metrics(
                pred_binary, target_bool, valid_mask_bool, water_frequency
            )

            return eval_metrics



class _Loss(nn.Module):
    """
    Desprected
    Inpainting loss combining BCE, DICE, and Focal losses with frequency-based weighting
    """

    def __init__(self,
                 bce_weight=0.3,
                 dice_weight=0.4,
                 focal_weight=0.3,
                 use_frequency_weight=False,
                 focal_alpha=1.0,
                 focal_gamma=2.0):
        super().__init__()

        # Normalize weights to sum to 1
        total_weight = bce_weight + dice_weight + focal_weight
        self.bce_weight = bce_weight / total_weight
        self.dice_weight = dice_weight / total_weight
        self.focal_weight = focal_weight / total_weight

        self.use_frequency_weight = use_frequency_weight
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma

        # Loss components
        self.dice = DiceLoss()
        self.bce = BCELoss(reduction='mean')
        self.focal = FocalLoss(alpha=focal_alpha, gamma=focal_gamma, reduction='mean')

        # Metrics calculation
        self.metrics_calculator = MetricsCalculator()
    
    def forward(self, outputs: Dict, batch: Dict) -> Tuple[torch.Tensor, Dict]:
        """
        Compute inpainting loss
        
        Args:
            outputs: Model outputs containing 'inpaint' predictions
            batch: Batch data containing ground truth, masks, and frequency info
            stage: Training stage (for compatibility)
        """
        device = batch['ground_truth'].device
        
        metrics = {}
        
        try:
            # Extract data
            target = batch['ground_truth']  # (B, H, W)

            # Get mask
            if 'missing_mask' in batch:
                mask = batch['missing_mask']
            else:
                raise ValueError("Missing mask not found in batch")
                
            if mask.sum() == 0:
                raise ValueError("Missing mask is all zeros")
            
            # Get water frequency - always needed for metrics calculation
            water_frequency = batch.get('occurrence')
            # Only use for weighting if enabled
            water_frequency_for_weighting = water_frequency if self.use_frequency_weight else None
            
            # Get predictions
            if isinstance(outputs.get('inpaint'), dict):
                pred_logits = outputs['inpaint']['logits']  # (B, 2, H, W)
            else:
                pred_logits = outputs.get('inpaint', outputs)
            
            # Compute main inpainting loss (Cross-Entropy + DICE)
            inpaint_loss, metrics = self._compute_weighted_loss(
                pred_logits, target, mask, water_frequency_for_weighting, water_frequency
            )
            
            total_loss = inpaint_loss

            return total_loss, metrics
            
        except Exception as e:
            logger.error(f"Error in loss computation: {e}")
            device = batch['ground_truth'].device if 'ground_truth' in batch else torch.device('cuda:0')
            safe_loss = torch.tensor(1.0, device=device, requires_grad=True)
            return safe_loss, {}
    
    def _compute_weighted_loss(self, predictions: torch.Tensor, targets: torch.Tensor,
                              mask: torch.Tensor, water_frequency_for_weighting: Optional[torch.Tensor],
                              water_frequency_for_metrics: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict]:
        """
        Compute weighted BCE + DICE + Focal loss combination
        """
        device = predictions.device
        predictions = predictions.float()
        targets = targets.float()
        mask = mask.bool()
        if water_frequency_for_weighting is not None:
            water_frequency_for_weighting = water_frequency_for_weighting.float()
        if water_frequency_for_metrics is not None:
            water_frequency_for_metrics = water_frequency_for_metrics.float()

        if mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True), {}

        # Calculate pixel weights
        if self.use_frequency_weight and water_frequency_for_weighting is not None:
            # Ensure water_frequency has the correct shape for compute_dynamic_degree
            if water_frequency_for_weighting.dim() == 4 and water_frequency_for_weighting.shape[1] == 1:
                # Shape is (B, 1, H, W), squeeze to (B, H, W)
                water_freq_squeezed = water_frequency_for_weighting.squeeze(1)
            elif water_frequency_for_weighting.dim() == 3:
                # Shape is already (B, H, W)
                water_freq_squeezed = water_frequency_for_weighting
            else:
                raise ValueError(f"Unexpected water_frequency shape: {water_frequency_for_weighting.shape}")

            frequency_weight = compute_dynamic_degree(water_freq_squeezed).float()
        else:
            frequency_weight = torch.ones_like(mask.float()).float()

        # 1. Binary Cross-Entropy Loss (only compute if weight > 0)
        if self.bce_weight > 0:
            bce_loss = self.bce(predictions, targets, mask, frequency_weight)
        else:
            bce_loss = torch.tensor(0.0, device=device, requires_grad=False)

        # 2. DICE Loss
        # Always compute water_probs for metrics calculation
        water_probs = torch.softmax(predictions, dim=1)[:, 1, :, :].float()

        if self.use_adaptive_dice:
            # Use AdaptiveDiceLoss which takes raw logits
            dice_loss = self.dice(predictions, targets, mask, frequency_weight)
        else:
            # Use traditional DiceLoss which takes probabilities
            binary_target = (targets == 1).float()
            dice_loss = self.dice(
                water_probs * mask,
                binary_target * mask,
                frequency_weight * mask
            )

        # 3. Independent Focal Loss (with mask interface)
        focal_loss = self.focal(predictions, targets, mask)

        # Combine all three losses
        combined_loss = (self.bce_weight * bce_loss +
                        self.dice_weight * dice_loss +
                        self.focal_weight * focal_loss)

        # Compute metrics
        with torch.no_grad():
            # water_probs is already computed above
            binary_target = (targets == 1).float()
            pred_binary = (water_probs > 0.5).bool()
            target_bool = binary_target.bool()
            valid_mask_bool = mask.bool()

            metrics = {
                'bce_loss': bce_loss.item(),
                'dice_loss': dice_loss.item(),
                'focal_loss': focal_loss.item(),
                'combined_loss': combined_loss.item()
            }

            if water_frequency_for_metrics is not None:
                eval_metrics = self.metrics_calculator.calculate_metrics(
                    pred_binary, target_bool, valid_mask_bool, water_frequency_for_metrics
                )
                metrics.update(eval_metrics)
            else:
                # If no water frequency available, set metrics to 0 instead of missing
                metrics.update({
                    'accuracy_0_1': 0.0,
                    'precision_0_1': 0.0,
                    'recall_0_1': 0.0,
                    'f1_score_0_1': 0.0,
                    'iou_0_1': 0.0,
                    'accuracy_0_2_0_8': 0.0,
                    'precision_0_2_0_8': 0.0,
                    'recall_0_2_0_8': 0.0,
                    'f1_score_0_2_0_8': 0.0,
                    'iou_0_2_0_8': 0.0,
                    'accuracy_0_4_0_6': 0.0,
                    'precision_0_4_0_6': 0.0,
                    'recall_0_4_0_6': 0.0,
                    'f1_score_0_4_0_6': 0.0,
                    'iou_0_4_0_6': 0.0,
                })

        return combined_loss, metrics

def compute_dynamic_degree(frequency: torch.Tensor, 
                          alpha: float = 2.0, 
                          sigma: float = 0.25,
                          min_weight: float = 0.01) -> torch.Tensor:
    """
    Compute dynamic degree using non-linear Gaussian weight formula
    
    Args:
        frequency: (B, H, W) or (B, H_p, W_p) - Water frequency map
        alpha: Maximum weight multiplier
        sigma: Controls curve width
        min_weight: Minimum weight
        
    Returns:
        dynamic_degree: (B, H, W) or (B, H_p, W_p) - Dynamic degree weight
    """
    epsilon = 1e-6
    freq_clamped = torch.clamp(frequency, epsilon, 1.0 - epsilon)
    
    # Use "saddle" weight curve, giving higher weight to middle frequencies (0.4-0.6)
    dynamic_degree = alpha * torch.exp(-((freq_clamped - 0.5) / sigma) ** 2)
    dynamic_degree = torch.clamp(dynamic_degree, min_weight, alpha)
    
    return dynamic_degree


 