"""
Optimized Training Script for Swin Transformer v8 Video Water Body Detection
Designed for multi-GPU training with mixed precision and checkpoint recovery

Features:
- Efficient distributed training across multiple GPUs
- Mixed precision training with automatic scaling
- Optimized data loading for Swin Transformer
- Geographic and temporal context encoding
- Memory-efficient implementation without gradient checkpointing
- Comprehensive visualization and metrics tracking
- Checkpoint recovery with history continuation
- Curriculum learning support for progressive difficulty training
"""

import argparse
import logging
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.amp.grad_scaler import GradScaler
from torch.amp.autocast_mode import autocast
from torch.utils.data import DataLoader, DistributedSampler
from torch.utils.tensorboard import SummaryWriter
import yaml
import numpy as np
import multiprocessing as mp

# Configure logging BEFORE importing other modules to ensure consistent format
# Create logs directory if it doesn't exist
logs_dir = Path('logs')
logs_dir.mkdir(exist_ok=True)

# Create log filename with timestamp
log_filename = logs_dir / f"train_v20_2.log"

# Configure logging with both file and console handlers
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler()
    ],
    force=True  # Force reconfiguration of existing loggers
)
logger = logging.getLogger(__name__)

# Set specific loggers to appropriate levels
logging.getLogger('torch.distributed').setLevel(logging.WARNING)
logging.getLogger('torch.nn.parallel').setLevel(logging.WARNING)
logging.getLogger('PIL').setLevel(logging.WARNING)
logging.getLogger('matplotlib').setLevel(logging.WARNING)
logging.getLogger('dataset').setLevel(logging.WARNING)

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data.dataset import WaterBodyDataset
from model.model_v20 import create_swin_water_net
from model.loss import Loss
from model.mixed_precision_utils import convert_batch_for_mixed_precision, handle_loss_dtype, initialize_mixed_precision_model
from configs import get_config
from evaluation.visualization import save_visualization


def count_parameters(model):
    """Count model parameters"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params


def format_param_count(count):
    """Format parameter count in human readable format"""
    if count >= 1e6:
        return f"{count/1e6:.1f}M"
    elif count >= 1e3:
        return f"{count/1e3:.1f}K"
    else:
        return str(count)


class SwinV8Trainer:
    """Optimized trainer for Swin Transformer v8 on multi-GPU"""
    
    def __init__(self, config, local_rank=0, world_size=1, index_file=None, missing_db=None):
        """Initialize trainer with configuration"""
        self.config = config
        self.local_rank = local_rank
        self.rank = local_rank
        self.world_size = world_size
        self.device = torch.device(f'cuda:{local_rank}' if torch.cuda.is_available() else 'cpu')
        
        # Setup logging first
        self._setup_logging()
        
        # Setup data loaders
        self.index_file = Path(index_file) if index_file else None
        self.missing_db = Path(missing_db) if missing_db else None
        self.train_loader, self.val_loader = self._setup_data_loaders()
        
        # Setup model first
        self._setup_model()
        
        # Setup loss function with combined BCE + DICE + Focal Loss
        self.loss_fn = Loss(
            bce_weight=config.training.loss_config.get('bce_weight', 0.3),
            dice_weight=config.training.loss_config.get('dice_weight', 0.4),
        ).to(self.device)
        
        # Setup optimizer and scheduler after model is set
        self.optimizer = self._setup_optimizer()
        self.scheduler = self._setup_scheduler()
        # Initialize GradScaler with default settings
        if config.training.mixed_precision:
            self.scaler = GradScaler()
        else:
            self.scaler = None
                
        # Training state
        self.global_step = 0
        self.epoch = 0

        self.best_metrics_0_4_0_6 = {
            'accuracy_0_4_0_6': 0.0,
            'f1_score_0_4_0_6': 0.0,
            'iou_0_4_0_6': 0.0,
        }
        
        # Validation
        self.validate_every_global_steps = self.config.validation.get('validate_every_global_steps', 1)

        # Early stopping
        self.early_stopping_patience = config.validation.get('early_stopping', {}).get('patience', 15)
        self.patience_counter = 0
        
        # History tracking
        self.train_history = {
            'loss': [],
        }
        
        self.val_history = {
            'loss': [],
            'metrics': []
        }
    
    def _setup_logging(self):
        """Set up logging and checkpointing directories"""
        logging_config = self.config.logging
        
        # Get experiment name
        self.experiment_name = logging_config.get('experiment_name', f"swin_v8_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        # Get result directory
        self.res_dir = Path(logging_config.get('res_dir', 'Results'))
        
        # Create directory paths
        self.log_dir = self.res_dir / 'logs' / self.experiment_name
        self.checkpoint_dir = self.res_dir / 'checkpoints' / self.experiment_name
        self.metrics_dir = self.res_dir / 'metrics' / self.experiment_name
        self.vis_dir = self.res_dir / 'visualizations' / self.experiment_name
        self.val_index_path = self.res_dir / 'val_indices' / self.experiment_name / 'val_indices.json'

        if self.local_rank == 0:
            self.log_dir.mkdir(parents=True, exist_ok=True)
            self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
            self.metrics_dir.mkdir(parents=True, exist_ok=True)
            self.vis_dir.mkdir(parents=True, exist_ok=True)
            self.val_index_path.parent.mkdir(parents=True, exist_ok=True)
            
            # TensorBoard writer
            if logging_config.get('use_tensorboard', True):
                self.writer = SummaryWriter(self.log_dir)
            
            # Save config
            with open(self.checkpoint_dir / 'config.yaml', 'w') as f:
                yaml.dump(self.config, f)
    
    def _setup_model(self):
        """Initialize and prepare model for distributed training"""
        model = create_swin_water_net(self.config)

        # Initialize model for mixed precision training
        model = initialize_mixed_precision_model(model, self.device)
        
        # Distributed training
        if self.world_size > 1:
            model = DDP(
                model,
                device_ids=[self.local_rank],
                output_device=self.local_rank,
                find_unused_parameters=False,
                broadcast_buffers=True,
                bucket_cap_mb=self.config.hardware.get('ddp_bucket_cap_mb', 25),
                static_graph=True
            )
        # Enable cudnn benchmarking for performance
        if self.config.performance.cudnn_benchmark:
            torch.backends.cudnn.benchmark = True
        # Report model parameters (only on rank 0)
        if self.local_rank == 0:
            trainable_params, total_params = count_parameters(model)
            logger.info(f"Model: {format_param_count(trainable_params)} trainable / {format_param_count(total_params)} total parameters")
        
        self.model = model
    
    def _setup_data_loaders(self):
        """Create optimized data loaders for distributed training"""
        # Get missing ratio filter parameters from config
        missing_ratio_config = getattr(self.config.data, 'missing_ratio_filter', {})
        min_missing_ratio = missing_ratio_config.get('min_missing_ratio', 0.0)
        max_missing_ratio = missing_ratio_config.get('max_missing_ratio', 1.0)

        # Log missing ratio filter settings
        if self.local_rank == 0:
            logger.info(f"Missing ratio filter: [{min_missing_ratio:.3f}, {max_missing_ratio:.3f}]")

        # Create datasets
        train_dataset = WaterBodyDataset(
            index_file=str(self.index_file) if self.index_file else "",
            missing_db_file=str(self.missing_db) if self.missing_db else None,
            config=self.config,
            expected_missing_ratio=max_missing_ratio,
            mode='test',  # 修复：使用正确的训练模式
            device='a100',
            use_missing_augmentation=True,
        )

        val_dataset = WaterBodyDataset(
            index_file=str(self.index_file) if self.index_file else "",
            missing_db_file=str(self.missing_db) if self.missing_db else None,
            config=self.config,
            expected_missing_ratio=max_missing_ratio,
            mode='debug',  # 修复：使用正确的验证模式
            device='a100',
            use_missing_augmentation=True,
        )
        
        # ---------------- Curriculum learning sampler logic ----------------
        curriculum_cfg = self.config.training.get('curriculum_learning', {}) if hasattr(self.config, 'training') else {}
        curriculum_enabled = curriculum_cfg.get('enabled', False)

        # Store flag for later use
        self.curriculum_enabled = curriculum_enabled

        if curriculum_enabled:
            from data.dataset import CurriculumSampler, CurriculumDistributedSampler  # Local import to avoid circular

            start_frac = curriculum_cfg.get('start_fraction', 0.3)
            end_frac = curriculum_cfg.get('end_fraction', 1.0)
            total_epochs_cl = curriculum_cfg.get('total_epochs', self.config.training.total_epochs)

            if self.world_size > 1:
                train_sampler = CurriculumDistributedSampler(
                    train_dataset,
                    num_replicas=self.world_size,
                    rank=self.local_rank,
                    start_fraction=start_frac,
                    end_fraction=end_frac,
                    total_epochs=total_epochs_cl,
                    shuffle=True,
                    seed=self.config.seed if hasattr(self.config, 'seed') else 42
                )
            else:
                train_sampler = CurriculumSampler(
                    train_dataset,
                    start_fraction=start_frac,
                    end_fraction=end_frac,
                    total_epochs=total_epochs_cl,
                    shuffle_within=True
                )
        else:
            # Default sampler logic (existing)
            train_sampler = DistributedSampler(
                train_dataset,
                num_replicas=self.world_size,
                rank=self.local_rank,
                shuffle=True,
                drop_last=True
            ) if self.world_size > 1 else None

        val_sampler = DistributedSampler(
            val_dataset,
            num_replicas=self.world_size,
            rank=self.local_rank,
            shuffle=False,
            drop_last=True  # 改为True，确保验证时也丢弃不完整的batch
        ) if self.world_size > 1 else None
        
        # Batch size
        batch_size = self.config.training.batch_size
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=(train_sampler is None),
            sampler=train_sampler,
            num_workers=self.config.training.num_workers,
            pin_memory=self.config.training.pin_memory,
            drop_last=True,
            persistent_workers=self.config.training.get('persistent_workers', True),
            prefetch_factor=self.config.training.get('prefetch_factor', 2)
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            sampler=val_sampler,
            num_workers=self.config.inference.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        if self.local_rank == 0:
            logger.info(f"Train samples: {len(train_dataset)}")
            logger.info(f"Val samples: {len(val_dataset)}")
            logger.info(f"Per-GPU batch sizes - Train: {batch_size}, Val: {batch_size}")

            # Log curriculum learning configuration
            if hasattr(self, 'curriculum_enabled') and self.curriculum_enabled:
                curriculum_cfg = self.config.training.get('curriculum_learning', {})
                start_frac = curriculum_cfg.get('start_fraction', 0.3)
                end_frac = curriculum_cfg.get('end_fraction', 1.0)
                total_epochs_cl = curriculum_cfg.get('total_epochs', self.config.training.total_epochs)
                logger.info(f"CURRICULUM LEARNING ENABLED:")
                logger.info(f"  Start fraction: {start_frac:.1%} ({int(len(train_dataset) * start_frac)} samples)")
                logger.info(f"  End fraction: {end_frac:.1%} ({int(len(train_dataset) * end_frac)} samples)")
                logger.info(f"  Total epochs: {total_epochs_cl}")
                logger.info(f"  Sampler type: {'CurriculumDistributedSampler' if self.world_size > 1 else 'CurriculumSampler'}")
            else:
                logger.info("Using standard data sampling (no curriculum learning)")

            logger.info("Data loaders setup completed successfully")
        
        # Save validation indices
        if self.local_rank == 0:
            try:
                with open(self.val_index_path, 'w') as f:
                    json.dump(val_dataset.samples, f, indent=2, default=lambda o: o if isinstance(o, (int, float, str, bool, list, dict)) else str(o))
                logger.info(f"Saved validation indices to {self.val_index_path}")
            except Exception as e:
                logger.warning(f"Failed to save validation indices: {e}")
        
        return train_loader, val_loader
    
    def _setup_optimizer(self):
        """Create optimizer with parameter groups for Swin Transformer"""
        learning_rate = float(self.config.training.learning_rate)
        weight_decay = float(self.config.training.weight_decay)
        betas = tuple(map(float, self.config.training.betas))
        eps = float(self.config.training.eps)
        
        # Parameter groups for different learning rates
        try:
            if hasattr(self.model, 'module'):
                model = self.model.module
            else:
                model = self.model
            
            # 确保model是nn.Module类型
            if not isinstance(model, nn.Module):
                logger.warning(f"Model is not nn.Module type: {type(model)}. Using all parameters.")
                param_groups = [{'params': self.model.parameters(), 'lr': learning_rate}]
            else:
                param_groups = []
                
                # Encoder parameters
                if hasattr(model, 'encoder') and isinstance(model.encoder, nn.Module):
                    param_groups.append({
                        'params': model.encoder.parameters(),
                        'lr': learning_rate,
                        'name': 'encoder'
                    })
                
                # Patch embedding
                if hasattr(model, 'patch_embed') and isinstance(model.patch_embed, nn.Module):
                    param_groups.append({
                        'params': model.patch_embed.parameters(),
                        'lr': learning_rate * 1.5,
                        'name': 'patch_embed'
                    })
                
                # Decoder parameters
                if hasattr(model, 'decoder') and isinstance(model.decoder, nn.Module):
                    param_groups.append({
                        'params': model.decoder.parameters(),
                        'lr': learning_rate * 2.0,
                        'name': 'decoder'
                    })
                
                # Geographic temporal encoding
                if hasattr(model, 'geo_temporal_encoder') and isinstance(model.geo_temporal_encoder, nn.Module):
                    param_groups.append({
                        'params': model.geo_temporal_encoder.parameters(),
                        'lr': learning_rate * 1.5,
                        'name': 'geo_temporal'
                    })
                
                # Temporal sampling
                if hasattr(model, 'temporal_sampling') and isinstance(model.temporal_sampling, nn.Module):
                    param_groups.append({
                        'params': model.temporal_sampling.parameters(),
                        'lr': learning_rate * 1.2,
                        'name': 'temporal_sampling'
                    })
                
                # If no specific parameter groups were added, use all parameters
                if not param_groups:
                    param_groups = [{'params': model.parameters(), 'lr': learning_rate}]
                    
        except Exception as e:
            logger.warning(f"Error setting up parameter groups: {e}. Using all parameters.")
            param_groups = [{'params': self.model.parameters(), 'lr': learning_rate}]
        
        optimizer = torch.optim.AdamW(
            param_groups,
            lr=learning_rate,
            betas=(betas[0], betas[1]) if len(betas) >= 2 else (0.9, 0.999),
            weight_decay=weight_decay,
            eps=eps
        )
        
        return optimizer
    
    def _setup_scheduler(self):
        """Create learning rate scheduler"""
        scheduler_config = self.config.training.lr_scheduler_params
        
        if self.config.training.scheduler == 'cosine_annealing_warm_restarts':
            scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=int(scheduler_config.T_0),
                T_mult=int(scheduler_config.T_mult),
                eta_min=float(scheduler_config.eta_min)
            )
        else:
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=int(self.config.training.total_epochs),
                eta_min=float(scheduler_config.eta_min)
            )
        
        return scheduler
    
    def train_epoch(self):
        """Train for one epoch with mixed precision"""
        self.model.train()
        
        # Set sampler epoch for distributed training and curriculum learning
        if self.train_loader.sampler is not None:
            sampler = self.train_loader.sampler
            if hasattr(sampler, 'set_epoch'):
                sampler.set_epoch(self.epoch)
        
        epoch_loss = 0.0
        num_batches = len(self.train_loader)
        
        # Logging configuration - reduced frequency for better performance
        log_interval = self.config.logging.get('log_interval', 50)  # Increased from 10 to 50
        empty_cache_freq = self.config.performance.get('empty_cache_freq', 200)  # Increased from 100 to 200
        mixed_precision = self.config.training.mixed_precision
        
        # Save last batch for visualization
        last_batch = None
        last_outputs = None
        
        # Basic synchronization for distributed training
        if self.world_size > 1:
            dist.barrier()
        
        # Note: Do not call scaler.update() at epoch start - it should only be called after scaler.step()
        
        for batch_idx, batch in enumerate(self.train_loader):
            try:
                # Move batch to device with proper type handling for mixed precision
                processed_batch = convert_batch_for_mixed_precision(batch, self.device)

                # Forward pass with mixed precision
                with autocast('cuda', enabled=mixed_precision):
                    outputs = self.model(processed_batch)
                    loss = self.loss_fn(outputs, processed_batch)

                # 确保loss为float32用于反向传播
                loss = handle_loss_dtype(loss, mixed_precision)
                
                # Backward pass with proper mixed precision handling
                if self.scaler is not None:
                    # Mixed precision training with GradScaler
                    # Scale loss and backward pass
                    self.scaler.scale(loss).backward()

                    # Optimizer step (only when accumulation is complete)
                    if (batch_idx + 1) % self.config.training.accumulation_steps == 0:
                        # Standard mixed precision training procedure:
                        # 1. Unscale gradients
                        # 2. Clip gradients (optional)
                        # 3. Step optimizer
                        # 4. Update scaler

                        # Standard mixed precision training procedure
                        self.scaler.unscale_(self.optimizer)

                        # Apply gradient clipping if enabled
                        if self.config.training.gradient_clipping > 0:
                            torch.nn.utils.clip_grad_norm_(
                                self.model.parameters(),
                                self.config.training.gradient_clipping
                            )

                        # Step optimizer
                        self.scaler.step(self.optimizer)
                        self.scaler.update()

                        # Clear gradients
                        self.optimizer.zero_grad()

                        # Note: Learning rate is updated at epoch level, not step level
                else:
                    # No mixed precision - standard training
                    loss.backward()

                    # Optimizer step (only when accumulation is complete)
                    if (batch_idx + 1) % self.config.training.accumulation_steps == 0:
                        # Apply gradient clipping if enabled
                        if self.config.training.gradient_clipping > 0:
                            torch.nn.utils.clip_grad_norm_(
                                self.model.parameters(),
                                self.config.training.gradient_clipping
                            )

                        # Step optimizer
                        self.optimizer.step()
                        self.optimizer.zero_grad()
                
                # Update metrics
                epoch_loss += loss.item()
                accumulated_train_loss += loss.item()
                steps_since_last_save += 1

                # Logging (more frequent than saving)
                if self.local_rank == 0 and (batch_idx + 1) % log_interval == 0:
                    progress = (batch_idx + 1) / num_batches * 100
                    logger.info(f"Epoch {self.epoch} [{progress:5.1f}%] Loss={loss.item():.4f}")

                self.global_step += 1
                
                # Save last batch for visualization
                if batch_idx == len(self.train_loader) - 1:
                    last_batch = {k: v.detach() if torch.is_tensor(v) else v for k, v in processed_batch.items()}
                    last_outputs = {k: {sk: sv.detach() if torch.is_tensor(sv) else sv for sk, sv in v.items()} 
                                   if isinstance(v, dict) else v.detach() if torch.is_tensor(v) else v
                                   for k, v in outputs.items()}
                    
            except Exception as e:
                logger.error(f"Rank {self.local_rank}: Error in training batch {batch_idx}: {str(e)}")
                self.optimizer.zero_grad()
                continue

            if self.global_step % self.validate_every_global_steps == 0:
                self.validate()

            # Clear cache periodically
            if batch_idx % empty_cache_freq == 0:
                torch.cuda.empty_cache()
        
        # Epoch end synchronization
        if self.world_size > 1:
            dist.barrier()
        
        # Average metrics
        avg_loss = epoch_loss / max(1, num_batches)
        
        return avg_loss, last_batch, last_outputs
    
    @torch.no_grad()
    def validate(self):
        """Validate the model"""
        self.model.eval()
        val_loss = 0.0
        total_metrics = {}
        num_valid_batches = {}
        n_batches = 0
        
        mixed_precision = self.config.training.mixed_precision
        
        # Validation start synchronization
        if self.world_size > 1:
            dist.barrier()
            
        for batch_idx, batch in enumerate(self.val_loader):
            try:
                # Move batch to device
                processed_batch = {}
                for k, v in batch.items():
                    if torch.is_tensor(v):
                        # 确保索引张量保持正确的数据类型
                        dtype = torch.long if k in ['center_frame_idx', 'year', 'month'] else None
                        processed_batch[k] = v.to(self.device, dtype=dtype, non_blocking=True)
                    else:
                        processed_batch[k] = v
                
                with autocast('cuda', enabled=mixed_precision):
                    outputs = self.model(processed_batch)
                    loss, metrics = self.loss_fn(outputs, processed_batch, 'eval')
                
                # Update metrics
                val_loss += loss.item()
                n_batches += 1
                
                # Aggregate metrics across batches
                for range_key, value in metrics.items():
                    if not np.isnan(value):
                        total_metrics[range_key] = total_metrics.get(range_key, 0.0) + value
                        num_valid_batches[range_key] = num_valid_batches.get(range_key, 0) + 1
                
            except Exception:
                logger.error(f"Rank {self.local_rank}: Error in validation batch {batch_idx}")
                continue

        # Validation end synchronization
        if self.world_size > 1:
            dist.barrier()

        # Average metrics
        avg_loss = val_loss / max(1, n_batches)
        avg_metrics = {key: total_metrics[key] / max(1, num_valid_batches.get(key, 0)) 
                      for key in total_metrics}

        # Gather metrics from all processes
        if self.world_size > 1:
            avg_loss = self._gather_metric(avg_loss)
            avg_metrics = {k: self._gather_metric(v) for k, v in avg_metrics.items()}

        return avg_loss, avg_metrics
    
    def _gather_metric(self, metric):
        """Gather metric from all processes"""
        if self.world_size == 1:
            return metric
        
        metric_tensor = torch.tensor(metric).to(self.device)
        dist.all_reduce(metric_tensor, op=dist.ReduceOp.SUM)
        return metric_tensor.item() / self.world_size
    
    def save_checkpoint(self, is_best=False):
        """Save model checkpoint with history"""
        if self.local_rank != 0:
            return

        # Get save options with fallbacks (more robust than direct access)
        validation_config = self.config.get('validation', {})
        save_last = True
        save_best_only = True

        if hasattr(validation_config, 'get'):
            save_last = validation_config.get('save_last', True)
            save_best_only = validation_config.get('save_best_only', True)
        else:
            save_last = getattr(validation_config, 'save_last', True)
            save_best_only = getattr(validation_config, 'save_best_only', True)

        # Get save interval with fallback
        logging_config = self.config.get('logging', {})
        save_interval = 5  # Default value

        if hasattr(logging_config, 'get'):
            save_interval = logging_config.get('save_interval', 5)
        else:
            save_interval = getattr(logging_config, 'save_interval', 5)
        
        # Get model state dict safely
        if hasattr(self.model, 'module'):
            model = self.model.module
        else:
            model = self.model
            
        if isinstance(model, nn.Module):
            model_state_dict = model.state_dict()
        else:
            logger.warning(f"Model is not nn.Module type: {type(model)}")
            model_state_dict = {}
        
        checkpoint = {
            'epoch': self.epoch,
            'global_step': self.global_step,
            'model_state_dict': model_state_dict,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_metrics_0_4_0_6': self.best_metrics_0_4_0_6,
            'train_history': self.train_history,
            'val_history': self.val_history,
            'config': self.config,
            # Additional training state information
            'patience_counter': self.patience_counter,
            'curriculum_enabled': getattr(self, 'curriculum_enabled', False),
            'timestamp': datetime.now().isoformat(),
            'pytorch_version': torch.__version__,
            'cuda_version': torch.version.cuda if torch.cuda.is_available() else None
        }
        
        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()
        
        # Save last checkpoint (always save latest state)
        if save_last:
            last_path = self.checkpoint_dir / 'last.pt'
            torch.save(checkpoint, last_path)
            logger.info(f"Saved latest checkpoint: {last_path}")

        # Save best checkpoint
        if is_best:
            if save_best_only:
                best_path = self.checkpoint_dir / 'best.pt'
                torch.save(checkpoint, best_path)
                logger.info(f"Saved best checkpoint: {best_path}")
            else:
                # Save with epoch number even if not save_best_only
                best_path = self.checkpoint_dir / f'best_epoch_{self.epoch}.pt'
                torch.save(checkpoint, best_path)
                logger.info(f"Saved best checkpoint: {best_path}")

        # Save periodic checkpoint
        if self.epoch % save_interval == 0:
            periodic_path = self.checkpoint_dir / f'epoch_{self.epoch}.pt'
            torch.save(checkpoint, periodic_path)
            logger.info(f"Saved periodic checkpoint: {periodic_path}")

        # Clean up old periodic checkpoints to save disk space (keep last 5)
        self._cleanup_old_checkpoints()

    def _cleanup_old_checkpoints(self):
        """Clean up old periodic checkpoints to save disk space"""
        try:
            # Find all epoch checkpoints
            epoch_checkpoints = list(self.checkpoint_dir.glob('epoch_*.pt'))

            # Sort by epoch number
            epoch_checkpoints.sort(key=lambda x: int(x.stem.split('_')[1]))

            # Keep only the last 5 periodic checkpoints
            if len(epoch_checkpoints) > 5:
                for old_checkpoint in epoch_checkpoints[:-5]:
                    try:
                        old_checkpoint.unlink()
                        logger.info(f"Cleaned up old checkpoint: {old_checkpoint}")
                    except Exception as e:
                        logger.warning(f"Failed to remove old checkpoint {old_checkpoint}: {e}")

            # Clean up old backup checkpoints (keep last 3)
            backup_checkpoints = list(self.checkpoint_dir.glob('backup_epoch_*.pt'))
            backup_checkpoints.sort(key=lambda x: int(x.stem.split('_')[2]))

            if len(backup_checkpoints) > 3:
                for old_backup in backup_checkpoints[:-3]:
                    try:
                        old_backup.unlink()
                        logger.info(f"Cleaned up old backup: {old_backup}")
                    except Exception as e:
                        logger.warning(f"Failed to remove old backup {old_backup}: {e}")

        except Exception as e:
            logger.warning(f"Error during checkpoint cleanup: {e}")

    def save_metrics_history(self):
        """Save metrics history to JSON files"""
        if self.local_rank != 0:
            return
            
        with open(self.metrics_dir / f'train_history_epoch_{self.epoch}.json', 'w') as f:
            json.dump({
                'loss': self.train_history['loss'],
            }, f, indent=2)
            
        with open(self.metrics_dir / f'val_history_epoch_{self.epoch}.json', 'w') as f:
            json.dump({
                'loss': self.val_history['loss'],
                'metrics': self.val_history['metrics']
            }, f, indent=2)
            
        # Save latest complete history
        with open(self.metrics_dir / 'train_history.json', 'w') as f:
            json.dump({
                'loss': self.train_history['loss'],
            }, f, indent=2)
            
        with open(self.metrics_dir / 'val_history.json', 'w') as f:
            json.dump({
                'loss': self.val_history['loss'],
                'metrics': self.val_history['metrics']
            }, f, indent=2)

    def train(self):
        """Main training loop"""
        if self.local_rank == 0:
            logger.info(f"SETUP | Total Epochs: {self.config.training.total_epochs} | GPUs: {self.world_size} | Batch Size per GPU: {self.config.training.batch_size}")

        use_tensorboard = self.config.logging.get('use_tensorboard', True)
        vis_interval = self.config.visualization.get('vis_interval', 5)  # Reduced frequency from 1 to 5

        # Visualization configuration
        vis_config = self.config.visualization.copy()
        vis_config['vis_dir'] = self.vis_dir
        
        # Training progress tracking
        start_time = time.time()

        for epoch in range(self.config.training.total_epochs):
            self.epoch = epoch
            epoch_start_time = time.time()

            # Train
            train_loss, last_train_batch, last_train_outputs = self.train_epoch()

            # Calculate epoch timing
            epoch_time = time.time() - epoch_start_time
            total_time = time.time() - start_time

            if self.local_rank == 0:
                # Estimate remaining time
                avg_epoch_time = total_time / (epoch + 1)
                remaining_epochs = self.config.training.total_epochs - epoch - 1
                eta = remaining_epochs * avg_epoch_time

                # Simple timing info
                logger.info(f"TIMING | Epoch: {epoch:3d} | Time: {epoch_time:.1f}s | "
                           f"Total: {total_time/3600:.1f}h | ETA: {eta/3600:.1f}h")
            
            # Log curriculum learning progress
            if self.local_rank == 0 and hasattr(self, 'curriculum_enabled') and self.curriculum_enabled:
                sampler = self.train_loader.sampler
                if hasattr(sampler, '_current_cutoff'):
                    current_samples = sampler._current_cutoff()
                    total_samples = len(sampler.dataset)
                    progress_pct = (current_samples / total_samples) * 100
                    logger.info(f"CURRICULUM | Epoch: {epoch:3d} | Using {current_samples}/{total_samples} samples ({progress_pct:.1f}%)")

            # Add training metrics to history
            if self.local_rank == 0:
                self.train_history['loss'].append(train_loss)
            
            # Validate
            val_loss, val_metrics = self.validate()
            
            # Add validation metrics to history
            if self.local_rank == 0:
                self.val_history['loss'].append(val_loss)
                self.val_history['metrics'].append(val_metrics)
            
            # Check if best model - all three metrics in 0.4-0.6 frequency range must improve
            current_metrics = {
                'accuracy_0_4_0_6': val_metrics.get('accuracy_0_4_0_6', 0),
                'f1_score_0_4_0_6': val_metrics.get('f1_score_0_4_0_6', 0),
                'iou_0_4_0_6': val_metrics.get('iou_0_4_0_6', 0)
            }
            
            # Check if ALL three metrics in 0.4-0.6 range have improved
            all_improved = all(
                current_metrics[metric] > self.best_metrics_0_4_0_6[metric] 
                for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']
            )
            
            is_best = all_improved
            if is_best:
                # Update all best metrics for 0.4-0.6 frequency range
                for metric in ['accuracy_0_4_0_6', 'f1_score_0_4_0_6', 'iou_0_4_0_6']:
                    self.best_metrics_0_4_0_6[metric] = current_metrics[metric]
                self.patience_counter = 0
                
                if self.local_rank == 0:
                    logger.info(f"New best model (0.4-0.6 freq)! Acc={current_metrics['accuracy_0_4_0_6']:.4f}, "
                                f"F1={current_metrics['f1_score_0_4_0_6']:.4f}, "
                                f"IoU={current_metrics['iou_0_4_0_6']:.4f}")
            else:
                self.patience_counter += 1
            
            # Log validation results
            if self.local_rank == 0:
                logger.info(f"Epoch {epoch}: Val Loss={val_loss:.4f}, \n"
                    f"Acc (0-1) = {val_metrics.get('accuracy_0_1', float('nan')):.4f}, F1 (0-1)={val_metrics.get('f1_score_0_1', float('nan')):.4f}, IoU (0-1)={val_metrics.get('iou_0_1', float('nan')):.4f}, \n"
                    f"Acc (0.2-0.8) = {val_metrics.get('accuracy_0_2_0_8', float('nan')):.4f}, F1 (0.2-0.8)={val_metrics.get('f1_score_0_2_0_8', float('nan')):.4f}, IoU (0.2-0.8)={val_metrics.get('iou_0_2_0_8', float('nan')):.4f}, \n"
                    f"Acc (0.4-0.6) = {val_metrics.get('accuracy_0_4_0_6', float('nan')):.4f}, F1 (0.4-0.6)={val_metrics.get('f1_score_0_4_0_6', float('nan')):.4f}, IoU (0.4-0.6)={val_metrics.get('iou_0_4_0_6', float('nan')):.4f}")
                
                if use_tensorboard:
                    self.writer.add_scalar('val/loss', val_loss, epoch)
                    for k, v in val_metrics.items():
                        self.writer.add_scalar(f'val/{k}', v, epoch)
            
            # Save checkpoint (best model if improved)
            self.save_checkpoint(is_best)
            if is_best and self.local_rank == 0:
                logger.info(f"Best checkpoint saved")

            # Save latest checkpoint even without validation
            if self.local_rank == 0:
                self.save_checkpoint(is_best=False)
                self.save_metrics_history()
                current_lr = self.optimizer.param_groups[0]['lr']
                logger.info(f"Learning Rate: {current_lr:.8f}")

            # Save visualization
            if self.local_rank == 0 and last_train_batch is not None and last_train_outputs is not None and epoch % vis_interval == 0:
                save_visualization(last_train_batch, last_train_outputs, epoch, vis_config, logger)
                logger.info(f"VISUAL | Epoch: {epoch:3d} | Visualization saved")
                
            # Update learning rate
            self.scheduler.step()                
                
            if self.local_rank == 0 and use_tensorboard:
                for i, param_group in enumerate(self.optimizer.param_groups):
                    self.writer.add_scalar(
                        f"lr/{param_group.get('name', i)}", 
                        param_group['lr'], 
                        epoch
                    )

            # Check for early stopping
            if self.early_stopping_patience > 0 and self.patience_counter >= self.early_stopping_patience:
                if self.local_rank == 0:
                    logger.info(f"Early stopping triggered after {self.early_stopping_patience} epochs with no improvement.")
                break
        
        # Save final checkpoint when training completes
        if self.local_rank == 0:
            self.save_checkpoint(is_best=False)
            logger.info("Final checkpoint saved")

            logger.info(f"Training completed! Best metrics (0.4-0.6 freq) - "
                       f"Accuracy: {self.best_metrics_0_4_0_6['accuracy_0_4_0_6']:.4f}, "
                       f"F1: {self.best_metrics_0_4_0_6['f1_score_0_4_0_6']:.4f}, "
                       f"IoU: {self.best_metrics_0_4_0_6['iou_0_4_0_6']:.4f}")

            # Save final metrics summary
            final_summary = {
                'training_completed': True,
                'total_epochs': self.epoch + 1,
                'best_metrics': self.best_metrics_0_4_0_6,
                'final_train_loss': self.train_history['loss'][-1] if self.train_history['loss'] else None,
                'final_val_loss': self.val_history['loss'][-1] if self.val_history['loss'] else None,
                'total_training_steps': self.global_step
            }

            with open(self.metrics_dir / 'final_summary.json', 'w') as f:
                json.dump(final_summary, f, indent=2)
            logger.info("Final training summary saved")


def setup_distributed():
    """Initialize distributed training"""
    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
        world_size = int(os.environ['WORLD_SIZE'])

        # Reconfigure logging for distributed training to ensure consistent format
        # Reuse the global log filename defined at the top of the file
        global log_filename

        # Clear existing handlers and reconfigure
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        # Configure root logger directly
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        if local_rank == 0:
            # Main process: log to both file and console with standard format
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ))

            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ))

            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
        else:
            # Other processes: log to file with rank info, console only for warnings
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(
                f'%(asctime)s %(levelname)s [Rank {local_rank}] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ))

            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.WARNING)  # Only warnings/errors to console
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ))

            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)

        # Enhanced NCCL options for numerical stability
        os.environ.setdefault('NCCL_DEBUG', 'WARN')
        os.environ.setdefault('NCCL_IB_DISABLE', '1')
        os.environ.setdefault('NCCL_P2P_DISABLE', '1')
        os.environ.setdefault('TORCH_DISTRIBUTED_DEBUG', 'INFO')

        # Additional NCCL stability options - 使用新的环境变量名称
        os.environ.setdefault('NCCL_TIMEOUT', '1800')  # 30 minutes timeout
        os.environ.setdefault('TORCH_NCCL_BLOCKING_WAIT', '1')  # Enable blocking wait (新名称)
        os.environ.setdefault('TORCH_NCCL_ASYNC_ERROR_HANDLING', '1')  # Better error handling (新名称)
        os.environ.setdefault('NCCL_TREE_THRESHOLD', '0')  # Force ring algorithm
        os.environ.setdefault('NCCL_ALGO', 'Ring')  # Use ring algorithm for stability

        torch.cuda.set_device(local_rank)
        dist.init_process_group(backend='nccl')

        return local_rank, world_size
    else:
        return 0, 1


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Swin Transformer v8 Training')
    parser.add_argument('--config', type=str, required=True,
                        help='Path to configuration file')
    parser.add_argument('--index_file', type=str, required=True,
                        help='Path to data index file')
    parser.add_argument('--missing_db', type=str, required=True,
                        help='Path to missing data database')
    parser.add_argument('--resume', type=str, default=None,
                        help='Path to checkpoint to resume from')
    parser.add_argument('--distributed', action='store_true', default=False,
                        help='Enable distributed training (requires torchrun or mp.spawn)')
    parser.add_argument('--num_gpus', type=int, default=None,
                        help='Number of GPUs to use for distributed training (auto-detect if not specified)')
    
    args = parser.parse_args()
    
    # Setup distributed training
    local_rank, world_size = setup_distributed()
    
    # Load configuration
    config = get_config(args.config)
        
    # Set random seed
    torch.manual_seed(config.seed)
    np.random.seed(config.seed)
    
    # Create trainer
    trainer = SwinV8Trainer(config, local_rank, world_size, args.index_file, args.missing_db)
    
    # Resume from checkpoint if specified
    if args.resume:
        if local_rank == 0:
            logger.info(f"Loading checkpoint from {args.resume}")

        # Load checkpoint on every rank for consistency
        checkpoint = torch.load(args.resume, map_location='cpu', weights_only=False)

        # Load model weights
        target_model = trainer.model.module if hasattr(trainer.model, 'module') else trainer.model
        
        if isinstance(target_model, nn.Module):
            missing_keys, unexpected_keys = target_model.load_state_dict(
                checkpoint['model_state_dict'], strict=False
            )
        else:
            logger.warning(f"Target model is not nn.Module type: {type(target_model)}")
            missing_keys, unexpected_keys = [], []

        if local_rank == 0:
            if missing_keys:
                logger.warning(f"Missing keys when loading checkpoint (showing up to 20): {missing_keys[:20]}")
            if unexpected_keys:
                logger.warning(f"Unexpected keys when loading checkpoint (showing up to 20): {unexpected_keys[:20]}")

        # Load optimizer and scheduler states
        try:
            trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        except KeyError as e:
            if local_rank == 0:
                logger.warning(f"Optimizer/Scheduler state not found in checkpoint: {e}")

        if trainer.scaler and 'scaler_state_dict' in checkpoint:
            trainer.scaler.load_state_dict(checkpoint['scaler_state_dict'])

        # Load training progress and history
        trainer.epoch = checkpoint.get('epoch', 0)
        trainer.global_step = checkpoint.get('global_step', 0)

        # Load best metrics
        if 'best_metrics_0_4_0_6' in checkpoint:
            trainer.best_metrics_0_4_0_6 = checkpoint['best_metrics_0_4_0_6']
        else:
            best_val_acc = checkpoint.get('best_metrics_0_4_0_6', 0.0)
            trainer.best_metrics_0_4_0_6 = {
                'accuracy_0_4_0_6': best_val_acc,
                'f1_score_0_4_0_6': best_val_acc,
                'iou_0_4_0_6': best_val_acc
            }

        # Load history for continuation
        if 'train_history' in checkpoint:
            trainer.train_history = checkpoint['train_history']
        if 'val_history' in checkpoint:
            trainer.val_history = checkpoint['val_history']

        if local_rank == 0:
            logger.info(f"Checkpoint loaded. Resuming from epoch {trainer.epoch}, step {trainer.global_step}.")
            logger.info(f"History loaded - Train epochs: {len(trainer.train_history['loss'])}, Val epochs: {len(trainer.val_history['loss'])}")

    # Register safe globals for deserialization
    try:
        import torch.serialization as _serialization
        _serialization.add_safe_globals([
            get_config
        ])
    except Exception as e:
        logger.warning(f"Unable to register safe globals for deserialization: {e}")

    # Start training
    trainer.train()
    
    # Cleanup
    if world_size > 1:
        dist.destroy_process_group()


def _distributed_worker(local_rank: int, world_size: int):
    """Entry point for each spawned GPU process."""
    os.environ["LOCAL_RANK"] = str(local_rank)
    os.environ["RANK"] = str(local_rank)
    os.environ["WORLD_SIZE"] = str(world_size)
    torch.cuda.set_device(local_rank)
    main()


if __name__ == "__main__":
    # Parse arguments to check if distributed training is requested
    parser = argparse.ArgumentParser(description='Swin Transformer v8 Training')
    parser.add_argument('--distributed', action='store_true', default=False,
                        help='Enable distributed training (requires torchrun or mp.spawn)')
    parser.add_argument('--num_gpus', type=int, default=None,
                        help='Number of GPUs to use for distributed training (auto-detect if not specified)')
    
    # Parse only the distributed-related arguments first
    args, _ = parser.parse_known_args()
    
    if args.distributed:
        # User explicitly requested distributed training
        if 'LOCAL_RANK' in os.environ:
            # Already running under a distributed launcher (e.g. torchrun)
            main()
        else:
            # Auto-launch distributed training
            gpu_count = torch.cuda.device_count()
            if args.num_gpus is not None:
                world_size = min(args.num_gpus, gpu_count)
            else:
                world_size = gpu_count
                
            if world_size > 1:
                # Default rendezvous parameters
                os.environ.setdefault("MASTER_ADDR", "127.0.0.1")
                os.environ.setdefault("MASTER_PORT", "29500")
                
                print(f"Launching distributed training with {world_size} GPUs")
                mp.spawn(_distributed_worker, args=(world_size,), nprocs=world_size, join=True)
            else:
                print("Warning: Distributed training requested but only 1 GPU available. Running single-GPU training.")
                main()
    else:
        # Single-GPU training (default)
        main()