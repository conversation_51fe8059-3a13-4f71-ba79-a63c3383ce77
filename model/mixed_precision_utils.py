"""
Mixed Precision Training Utilities
Provides helper functions and decorators to ensure proper type handling in mixed precision training
"""

import torch
import torch.nn as nn
import functools
from typing import Any, Dict, List, Union, Tuple


def ensure_dtype_compatibility(func):
    """
    Decorator to ensure dtype compatibility in mixed precision training
    Automatically handles type conversions for model components
    """
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        # Convert inputs to appropriate types
        converted_args = []
        for arg in args:
            if torch.is_tensor(arg):
                # Let autocast handle the conversion
                converted_args.append(arg)
            else:
                converted_args.append(arg)
        
        converted_kwargs = {}
        for k, v in kwargs.items():
            if torch.is_tensor(v):
                converted_kwargs[k] = v
            else:
                converted_kwargs[k] = v
        
        return func(self, *converted_args, **converted_kwargs)
    
    return wrapper


def convert_batch_for_mixed_precision(batch: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
    """
    Convert batch data for mixed precision training
    
    Args:
        batch: Input batch dictionary
        device: Target device
        
    Returns:
        Converted batch with proper dtypes
    """
    processed_batch = {}
    
    for k, v in batch.items():
        if torch.is_tensor(v):
            if k in ['center_frame_idx', 'year', 'month']:
                # Integer types should remain as long
                processed_batch[k] = v.to(device, dtype=torch.long, non_blocking=True)
            elif k in ['tile_lon', 'tile_lat']:
                # Geographic coordinates as float32
                processed_batch[k] = v.to(device, dtype=torch.float32, non_blocking=True)
            else:
                # Let autocast handle other tensors
                processed_batch[k] = v.to(device, non_blocking=True)
        else:
            processed_batch[k] = v
    
    return processed_batch


def ensure_parameter_dtype_consistency(model: nn.Module, target_dtype: torch.dtype = torch.float32):
    """
    Ensure all model parameters have consistent dtype
    
    Args:
        model: PyTorch model
        target_dtype: Target dtype for parameters
    """
    for name, param in model.named_parameters():
        if param.dtype != target_dtype:
            param.data = param.data.to(target_dtype)
    
    for name, buffer in model.named_buffers():
        if buffer.dtype != target_dtype and 'position_index' not in name:
            # Skip position indices which should remain as long
            buffer.data = buffer.data.to(target_dtype)


def safe_autocast_forward(model: nn.Module, *args, **kwargs):
    """
    Safe forward pass with autocast handling
    
    Args:
        model: PyTorch model
        *args: Forward arguments
        **kwargs: Forward keyword arguments
        
    Returns:
        Model output
    """
    # Ensure all inputs are on the same device as model
    device = next(model.parameters()).device
    
    converted_args = []
    for arg in args:
        if torch.is_tensor(arg):
            converted_args.append(arg.to(device))
        else:
            converted_args.append(arg)
    
    converted_kwargs = {}
    for k, v in kwargs.items():
        if torch.is_tensor(v):
            converted_kwargs[k] = v.to(device)
        else:
            converted_kwargs[k] = v
    
    return model(*converted_args, **converted_kwargs)


class MixedPrecisionWrapper(nn.Module):
    """
    Wrapper for models to handle mixed precision training properly
    """
    
    def __init__(self, model: nn.Module):
        super().__init__()
        self.model = model
        
    def forward(self, *args, **kwargs):
        # Ensure dtype compatibility
        return safe_autocast_forward(self.model, *args, **kwargs)


def check_tensor_dtypes(tensors: Union[torch.Tensor, List[torch.Tensor], Dict[str, torch.Tensor]], 
                       name: str = "tensor") -> None:
    """
    Debug utility to check tensor dtypes
    
    Args:
        tensors: Tensor(s) to check
        name: Name for logging
    """
    if isinstance(tensors, torch.Tensor):
        print(f"{name}: dtype={tensors.dtype}, device={tensors.device}, shape={tensors.shape}")
    elif isinstance(tensors, (list, tuple)):
        for i, tensor in enumerate(tensors):
            if torch.is_tensor(tensor):
                print(f"{name}[{i}]: dtype={tensor.dtype}, device={tensor.device}, shape={tensor.shape}")
    elif isinstance(tensors, dict):
        for k, tensor in tensors.items():
            if torch.is_tensor(tensor):
                print(f"{name}[{k}]: dtype={tensor.dtype}, device={tensor.device}, shape={tensor.shape}")


def handle_loss_dtype(loss: torch.Tensor, mixed_precision: bool = True) -> torch.Tensor:
    """
    Handle loss tensor dtype for mixed precision training

    Args:
        loss: Loss tensor
        mixed_precision: Whether mixed precision is enabled

    Returns:
        Loss tensor with appropriate dtype
    """
    if mixed_precision:
        # Ensure loss is float32 for proper gradient scaling
        # GradScaler expects float32 loss values
        if loss.dtype == torch.float16:
            return loss.float()
        elif loss.dtype != torch.float32:
            return loss.to(torch.float32)
    return loss


def initialize_mixed_precision_model(model: nn.Module, device: torch.device) -> nn.Module:
    """
    Initialize model for mixed precision training

    Args:
        model: PyTorch model
        device: Target device

    Returns:
        Initialized model
    """
    model = model.to(device)

    # Ensure all parameters are float32 for mixed precision training
    # This is important because GradScaler expects float32 parameters
    ensure_parameter_dtype_consistency(model, torch.float32)

    # Enable mixed precision optimizations
    # Set model to use efficient attention if available
    if hasattr(model, 'set_grad_checkpointing'):
        model.set_grad_checkpointing(False)  # Disable for mixed precision

    return model


def check_gradient_health(model: nn.Module, threshold: float = 100.0) -> Dict[str, Any]:
    """
    Check gradient health for debugging training issues

    Args:
        model: PyTorch model
        threshold: Threshold for large gradient detection

    Returns:
        Dictionary with gradient statistics
    """
    stats = {
        'total_norm': 0.0,
        'param_count': 0,
        'nan_count': 0,
        'inf_count': 0,
        'zero_count': 0,
        'large_count': 0,
        'max_grad': 0.0,
        'min_grad': float('inf')
    }

    for name, param in model.named_parameters():
        if param.grad is not None:
            stats['param_count'] += 1
            grad_norm = param.grad.data.norm(2).item()

            # Update total norm
            stats['total_norm'] += grad_norm ** 2

            # Check for problematic gradients
            if torch.isnan(param.grad).any():
                stats['nan_count'] += 1
            elif torch.isinf(param.grad).any():
                stats['inf_count'] += 1
            elif grad_norm == 0:
                stats['zero_count'] += 1
            elif grad_norm > threshold:
                stats['large_count'] += 1

            # Track min/max
            stats['max_grad'] = max(stats['max_grad'], grad_norm)
            stats['min_grad'] = min(stats['min_grad'], grad_norm)

    stats['total_norm'] = stats['total_norm'] ** 0.5
    if stats['min_grad'] == float('inf'):
        stats['min_grad'] = 0.0

    return stats


def quick_gradient_check(model: nn.Module, max_check_params: int = 10) -> Dict[str, Any]:
    """
    Fast gradient health check for frequent monitoring
    Only checks for NaN/Inf in a subset of parameters for performance

    Args:
        model: PyTorch model
        max_check_params: Maximum number of parameters to check

    Returns:
        Dictionary with quick check results
    """
    result = {
        'has_nan_inf': False,
        'checked_params': 0,
        'total_params': 0
    }

    for name, param in model.named_parameters():
        if param.grad is not None:
            result['total_params'] += 1

            # Only check first N parameters for speed
            if result['checked_params'] < max_check_params:
                if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                    result['has_nan_inf'] = True
                    break
                result['checked_params'] += 1
            elif result['checked_params'] >= max_check_params:
                break

    return result


def clean_nan_inf_gradients(model: nn.Module) -> int:
    """
    Clean NaN/Inf gradients from model parameters

    Args:
        model: PyTorch model

    Returns:
        Number of parameters that had NaN/Inf gradients
    """
    nan_inf_count = 0

    for param in model.parameters():
        if param.grad is not None:
            if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                param.grad.data.zero_()
                nan_inf_count += 1

    return nan_inf_count
