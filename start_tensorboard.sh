#!/bin/bash

# TensorBoard启动脚本
# 使用方法: ./start_tensorboard.sh [日志目录] [端口]

# 默认参数
DEFAULT_LOGDIR="/mnt/storage/xiaozhen/Results/logs"
DEFAULT_PORT="6006"

# 获取参数
LOGDIR=${1:-$DEFAULT_LOGDIR}
PORT=${2:-$DEFAULT_PORT}

echo "启动TensorBoard..."
echo "日志目录: $LOGDIR"
echo "端口: $PORT"

source /mnt/storage/xiaozhen/miniconda3/bin/activate
conda activate geoai

# 检查日志目录是否存在
if [ ! -d "$LOGDIR" ]; then
    echo "警告: 日志目录 $LOGDIR 不存在"
    echo "请确保已经开始训练，或者指定正确的日志目录"
    echo "使用方法: $0 [日志目录] [端口]"
    exit 1
fi

# 启动TensorBoard
echo "TensorBoard将在 http://localhost:$PORT 启动"
echo "按 Ctrl+C 停止TensorBoard"
tensorboard --logdir="$LOGDIR" --port="$PORT" --host=************* --reload_interval=1
