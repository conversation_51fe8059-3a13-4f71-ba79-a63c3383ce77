# Optimized Configuration for Water Body Reconstruction
# Designed for 4x NVIDIA L40 GPUs (40GB each)
# Focus: Water body missing data reconstruction with geographic modulation

# Data configuration
data:
  index_file: null  # Set via command line
  missing_db: null  # Set via command line
  
  sequence_length: 120  # Keep full sequence for temporal context
  img_size: 256
  
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # Geographic and temporal features
  use_geographic_context: true
  geographic_fields:
    - tile_lon    # Tile longitude
    - tile_lat    # Tile latitude  
    - year        # Year information
    - month       # Month information
  
  # No augmentation for stable training
  augmentation:
    random_flip: false
    random_rotate: false
    random_scale: [1.0, 1.0]

# Sampling configuration for dataset indexing
sampling:
  # Bin-level sampling limits (统一使用基于bin的抽样)
  min_samples_per_bin: 10                # 每个bin的最小样本数
  max_samples_per_bin: 100              # 每个多维bin的最大样本数 (no-missing)
  max_missing_samples_per_bin: 1000     # 每个missing ratio bin的最大样本数
  
  max_processing_time: 120
  spatial_sample_factor: 0.5
  
  balance_target_minimum: 10
  missing_balance_minimum: 100

  # Sampling strategy
  sampling_strategy: "reservoir"
  
  # Feature binning configuration
  feature_bins:
    water_proportion_bins: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    missing_proportion_bins: [0.05, 0.2, 0.4, 0.6, 0.8, 1.0]
    mean_water_frequency_bins: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    lon_bins: [-180, -160, -120, -100, -80, -60, -40, -20, 0, 20, 40, 60, 80, 100, 120, 160, 180]
    lat_bins: [-90, -70, -50, -30, -10, 10, 30, 50, 70, 90]
    year_bins: [1984, 1995, 2005, 2015, 2025]
    month_bins: [1, 3, 5, 7, 9, 11]

# Model configuration - optimized for video inpainting
model:
  name: "SwinWaterNet"
  
  # Only inpainting task
  tasks:
    - inpaint
  
  task_weights:
    inpaint: 1.0
  
  # Geographic context encoding
  use_geographic_context: true
  geographic_encoder:
    num_freq_bands: 8      # Increased frequency bands for better encoding
    embed_dim: 128         # Match main model embedding
  
  # Multi-scale temporal processing
  temporal_scales: [1, 4, 12]  # Process every 1, 4, 12 frames
  
  # Video Swin Transformer configuration
  swin_config:
    img_size: 256
    patch_size: 8            # Larger patch for efficiency
    in_chans: 2              # Updated to match V6 input channels
    out_chans: 2             # Added for V6 output channels
    embed_dim: 128           # Base embedding dimension
    depths: [2, 2, 6, 2]     # Layer depths
    num_heads: [4, 8, 16, 32]
    # 2D spatial window sizes, matching the refactored attention mechanism
    window_sizes: [8, 8, 8, 8]
    mlp_ratio: 4.0
    drop_rate: 0.0
    attn_drop_rate: 0.0
    use_gradient_checkpoint: false
    frame_counts: [20, 20, 20]

    # V6特有的参数配置
    base_planes: 16          # ResNet backbone的基础通道数
    feature_layers: ['layer1', 'layer2', 'layer3']  # 多尺度特征层
    temporal_windows: [12, 48, 120]  # 短期、中期、长期时间窗口
    num_frames: 120          # 输入序列长度

# Training configuration for 4x L40 GPUs - FIXED FOR STABILITY
training:
  # Single-stage training for inpainting
  two_stage_training: false
  total_epochs: 100
  
  # Reduced batch size for video processing
  batch_size: 16        # Increased for better GPU utilization
  accumulation_steps: 1 # Adjusted to keep effective batch size the same
  num_workers: 8       # 4 workers per GPU
  pin_memory: true
  prefetch_factor: 2
  persistent_workers: true
  
  # Optimizer - FIXED: Lower learning rate to prevent NaN
  optimizer: "AdamW"
  learning_rate: 1e-4   # REDUCED from 1e-3 for stable training with 87.8M parameters
  weight_decay: 1e-4   # FURTHER REDUCED from 0.005 for stability
  betas: [0.9, 0.999]
  eps: 1e-8
  
  # Scheduler - Cosine with warmup
  scheduler: "cosine_annealing_warm_restarts"
  lr_scheduler_params:
    T_0: 20
    T_mult: 2
    eta_min: 1e-7        # REDUCED from 1e-6 for better final LR
    warmup_epochs: 10    # INCREASED warmup for stability
  
  # Mixed precision for efficiency - CONSERVATIVE SETTINGS
  mixed_precision: true   # Enable for memory efficiency
  amp_opt_level: "O1"     # Conservative mixed precision
  gradient_clipping: 1.0  # INCREASED from 0.1 for better training stability

  # ---------------- Curriculum Learning ----------------
  curriculum_learning:
    enabled: false       # Set true to enable soft curriculum
    start_fraction: 0.3  # Fraction of easiest samples at epoch 0
    end_fraction: 1.0    # Fraction by last curriculum epoch
    total_epochs: 20     # Number of epochs over which to ramp up
  
  # Loss configuration for inpainting - STABILIZED
  loss_config:
    focal_gamma: 1.0      # FURTHER REDUCED from 1.5 to prevent extreme gradients
    label_smoothing: 0.15  # INCREASED from 0.1 for stability
    confidence_weight: 0.1 # FURTHER REDUCED from 0.3 for stability
    use_confidence: true
    dice_weight: 0.5      # REDUCED from 0.6 for stability
    use_focal_loss: false
    use_frequency_weight: true  # DISABLED temporarily to debug

# Hardware configuration for 4x L40
hardware:
  num_cpu_threads: 16  # Number of CPU threads for parallel processing
  num_gpus: 4
  gpu_memory_limit: 40    # GB per GPU (L40 has 40GB)
  
  # Memory optimization for video processing
  gradient_checkpointing: false  # Enable for video sequences
  max_memory_per_gpu: 36        # Conservative limit
  
  # Distributed training settings
  distributed_backend: "nccl"
  find_unused_parameters: false
  sync_batchnorm: true
  
  # DDP settings - 优化后的配置
  ddp_bucket_cap_mb: 50  # 增加到50MB以减少通信次数，提高效率
  ddp_broadcast_buffers: false

# Logging and checkpointing
logging:
  experiment_name: "waternet_v5"
  # res_dir: "/fossfs/xiaozhen/Results"
  res_dir: "/mnt/storage/xiaozhen/Results/"

  log_interval: 10       # REDUCED for more frequent monitoring
  eval_interval: 500    # Evaluate every N iterations
  save_interval: 5      # Save checkpoint every N epochs
  vis_interval: 1
  
  # Metrics to track
  metrics:
    - "loss"
    - "inpaint_accuracy"
    - "confidence_accuracy"
    - "missing_pixel_accuracy"
    - "water_pixel_accuracy"
    - "land_pixel_accuracy"
  
  # Tensorboard settings
  use_tensorboard: true
  tensorboard_update_freq: 100
  profile_gpu: false  # Enable for profiling

# Visualization configuration
visualization:
  enabled: true
  vis_interval: 1
  num_vis_samples: 4
  freq_min: 0.2             # 水体频率最小值
  freq_max: 0.8             # 水体频率最大值

# Inference configuration
inference:
  batch_size: 64        # Larger batch for inference
  num_workers: 8
  
  # Output configuration
  save_predictions: true
  save_confidence_maps: true
  save_visualizations: true
  
  # Inference optimizations
  use_half_precision: true  # Full precision for quality
  compile_model: true       # PyTorch 2.0 compile

# Validation and testing
validation:
  # Validation strategy
  validation_split: "temporal"
  val_years: [2019, 2020]      # Years reserved for validation
  
  # Early stopping
  early_stopping:
    enabled: true
    patience: 15
    monitor: "val_inpaint_acc"
    min_delta: 0.001
    mode: "max"
  
  # Validation frequency
  validate_every_n_epochs: 1
  
  # Best model selection
  save_best_only: true
  save_last: true

# Performance optimizations
performance:
  # Data loading
  dataloader_drop_last: true
  shuffle_buffer_size: 10000
  
  # GPU optimizations
  cudnn_benchmark: true
  cudnn_deterministic: false
  
  # Memory optimizations
  empty_cache_freq: 500  # Clear cache less frequently
  
  # Distributed optimizations
  gradient_as_bucket_view: true
  static_graph: true

# Seed for reproducibility
seed: 42

# Debug options - ENABLED FOR DEBUGGING
debug:
  enabled: false
  detect_anomaly: false
  profiler_wait: 1
  profiler_warmup: 1
  profiler_active: 5
  profiler_repeat: 1 