"""
Configuration Management for Water Body Spatiotemporal Modeling Framework
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class AttrDict(dict):
    """Dictionary that supports attribute-style access (`cfg.key`) recursively."""

    def __getattr__(self, item):
        try:
            value = self[item]
            if isinstance(value, dict) and not isinstance(value, AttrDict):
                # Convert nested dicts on the fly for convenience
                value = AttrDict(value)
                self[item] = value
            return value
        except KeyError as e:
            raise AttributeError(*e.args) from None

    # Support setting attributes like obj.key = val
    def __setattr__(self, key, value):
        # If assigning a plain dict, promote to AttrDict for consistency
        if isinstance(value, dict) and not isinstance(value, AttrDict):
            value = AttrDict(value)
        self[key] = value

    # Ensure copy and deepcopy behave sensibly
    def copy(self):
        return AttrDict({k: (v.copy() if isinstance(v, AttrDict) else v) for k, v in self.items()})

    def get(self, key, default=None):
        value = super().get(key, default)
        if isinstance(value, dict) and not isinstance(value, AttrDict):
            value = AttrDict(value)
            # Do *not* write back into self to keep get() side-effect free
        return value


class Config:
    """Configuration management class"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration
        
        Args:
            config_path: Path to YAML configuration file
        """
        if config_path is None:
            config_path = Path(__file__).parent / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
        
        # Set up logging level
        logging.basicConfig(level=getattr(logging, self._config.get('logging', {}).get('level', 'INFO')))
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f) or {}
            # Wrap with AttrDict for dot-notation access
            config = AttrDict(config_dict)
            # Only log on main process to avoid duplicate messages in distributed training
            if not os.environ.get('LOCAL_RANK', '0') or os.environ.get('LOCAL_RANK', '0') == '0':
                logger.info(f"Configuration loaded from {self.config_path}")
            return config
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {e}")
            raise
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation
        
        Args:
            key: Configuration key (e.g., 'data.batch_size')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value using dot notation
        
        Args:
            key: Configuration key (e.g., 'data.batch_size')
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """
        Update configuration with new values
        
        Args:
            updates: Dictionary of updates
        """
        def _update_nested(d: Dict, u: Dict) -> Dict:
            for k, v in u.items():
                if isinstance(v, dict):
                    d[k] = _update_nested(d.get(k, {}), v)
                else:
                    d[k] = v
            return d
        
        self._config = _update_nested(self._config, updates)
    
    def save(self, path: Optional[str] = None) -> None:
        """
        Save configuration to YAML file
        
        Args:
            path: Output path (default: original config path)
        """
        if path is None:
            path = self.config_path
        
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, indent=2)
        
        logger.info(f"Configuration saved to {path}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Return configuration as dictionary"""
        return self._config.copy()
    
    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access"""
        return self.get(key)
    
    def __setitem__(self, key: str, value: Any) -> None:
        """Allow dictionary-style setting"""
        self.set(key, value)
    
    # Internal helper
    def _section(self, name: str) -> AttrDict:
        val = self._config.get(name, AttrDict())
        if isinstance(val, dict) and not isinstance(val, AttrDict):
            val = AttrDict(val)
            self._config[name] = val  # cache it for future
        return val

    @property
    def data(self) -> AttrDict:
        return self._section('data')

    @property
    def model(self) -> AttrDict:
        return self._section('model')

    @property
    def training(self) -> AttrDict:
        return self._section('training')

    @property
    def sampling(self) -> AttrDict:
        return self._section('sampling')

    @property
    def missing_db(self) -> AttrDict:
        return self._section('missing_db')

    @property
    def distributed(self) -> AttrDict:
        return self._section('distributed')

    @property
    def inference(self) -> AttrDict:
        return self._section('inference')

    @property
    def logging_config(self) -> AttrDict:
        return self._section('logging')

    @property
    def hardware(self) -> AttrDict:
        return self._section('hardware')

    @property
    def experiment(self) -> AttrDict:
        return self._section('experiment')

    @property
    def performance(self) -> AttrDict:
        return self._section('performance')

    def __getattr__(self, item):
        """Delegate attribute access to the internal AttrDict (_config)."""
        try:
            # First check if _config exists to avoid infinite recursion
            if '_config' not in self.__dict__:
                raise AttributeError(f"'Config' object has no attribute '_config'")
                
            # Then check if the item exists in _config
            if item in self._config:
                return self._config[item]
            else:
                raise AttributeError(f"'Config' object has no attribute '{item}'")
        except KeyError as e:
            raise AttributeError(*e.args) from None

    def __reduce__(self):
        """Customized pickling behavior for multiprocessing."""
        # Return a tuple of (callable, args) that will be used to recreate the object
        return (
            get_config,  # Function to recreate the object
            (str(self.config_path),)  # Arguments to pass to get_config
        )


# Global configuration instance
_global_config = None

def get_config(config_path: Optional[str] = None) -> Config:
    """
    Get global configuration instance
    
    Args:
        config_path: Path to configuration file (only used on first call)
        
    Returns:
        Config instance
    """
    global _global_config
    if _global_config is None:
        _global_config = Config(config_path)
    return _global_config

def reload_config(config_path: Optional[str] = None) -> Config:
    """
    Reload configuration
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        New Config instance
    """
    global _global_config
    _global_config = Config(config_path)
    return _global_config


# Convenience function for common use cases
def cfg() -> Config:
    """Get global configuration instance (short alias)"""
    return get_config()


__all__ = ['Config', 'get_config', 'reload_config', 'cfg'] 