# Swin Transformer v8 Configuration for Video Water Body Detection
# Parameters: ~80M, Memory: <40GB
# Based on standard Swin Transformer architecture

# Data configuration
data:
  index_file: null  # Set via command line
  missing_db: null  # Set via command line

  sequence_length: 48  # Full sequence for temporal context
  img_size: 256

  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1

  # Missing pattern filtering configuration
  missing_ratio_filter:
    min_missing_ratio: 0.05  # Minimum missing ratio threshold (0.0-1.0)
    max_missing_ratio: 1.0  # Maximum missing ratio threshold (0.0-1.0)
  
  # Geographic and temporal features
  use_geographic_context: true
  geographic_fields:
    - tile_lon    # Tile longitude
    - tile_lat    # Tile latitude  
    - year        # Year information
    - month       # Month information
  
  # No augmentation for stable training
  augmentation:
    random_flip: false
    random_rotate: false
    random_scale: [1.0, 1.0]

# Sampling configuration
sampling:
  min_samples_per_bin: 10
  max_samples_per_bin: 100
  max_missing_samples_per_bin: 1000
  
  max_processing_time: 120
  spatial_sample_factor: 0.5
  
  balance_target_minimum: 10
  missing_balance_minimum: 100
  
  sampling_strategy: "reservoir"
  
  # Feature binning configuration
  feature_bins:
    water_proportion_bins: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    missing_proportion_bins: [0.05, 0.2, 0.4, 0.6, 0.8, 1.0]
    mean_water_frequency_bins: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    lon_bins: [-180, -160, -120, -100, -80, -60, -40, -20, 0, 20, 40, 60, 80, 100, 120, 160, 180]
    lat_bins: [-90, -70, -50, -30, -10, 10, 30, 50, 70, 90]
    year_bins: [1984, 1995, 2005, 2015, 2025]
    month_bins: [1, 3, 5, 7, 9, 11]

# Model configuration - Standard Swin Transformer v8
model:
  name: "SwinWaterNet_v10"
  
  # Only inpainting task
  tasks:
    - inpaint
  
  task_weights:
    inpaint: 1.0
  
  # Geographic context encoding
  use_geographic_context: true
  geographic_encoder:
    num_freq_bands: 6      # Frequency bands for positional encoding
    embed_dim: 96          # Match main model embedding
  
  # Multi-scale temporal processing
  temporal_scales: [1, 4, 12]  # Process every 1, 4, 12 frames
  
  # Standard Swin Transformer configuration
  swin_config:
    img_size: 256
    patch_size: 4            # Configurable patch size (4, 8, 16, 32)
    in_chans: 2              # Input channels (SAR + optical)
    out_chans: 2             # Output channels (background + water)
    embed_dim: 96            # Base embedding dimension (reduced for 80M params)
    depths: [2, 2, 6, 2]     # Layer depths (standard Swin-T)
    num_heads: [3, 6, 12, 24]  # Number of attention heads
    window_size: 8           # Window size for window attention
    mlp_ratio: 4.0           # MLP expansion ratio
    qkv_bias: true           # Use bias in QKV projection
    drop_rate: 0.0           # Dropout rate
    attn_drop_rate: 0.0      # Attention dropout rate
    drop_path_rate: 0.1      # Stochastic depth rate
    use_gradient_checkpoint: false  # Disable for stability
    frame_counts: [16, 16, 16]  # Frame counts for temporal sampling
    num_frames: 48          # Input sequence length
    num_temporal_scales: 3   # Number of temporal scales

# Training configuration for 4x L40 GPUs
training:
  # Single-stage training for inpainting
  two_stage_training: false
  total_epochs: 100
  
  # Optimized batch size for Swin Transformer
  batch_size: 8            # Reduced for Swin Transformer memory usage
  accumulation_steps: 1      # Effective batch size = 4
  num_workers: 4             # Reduced for single GPU
  pin_memory: true
  prefetch_factor: 2
  persistent_workers: true
  
  # Optimizer - Ultra-conservative settings for numerical stability
  optimizer: "AdamW"
  learning_rate: 5e-5        # Lower LR for Swin Transformer
  weight_decay: 1e-4         # Weight decay
  betas: [0.9, 0.999]
  eps: 1e-8
  
  # Scheduler - Cosine with warmup
  scheduler: "cosine_annealing_warm_restarts"
  lr_scheduler_params:
    T_0: 20
    T_mult: 2
    eta_min: 1e-7
  
  # Mixed precision for efficiency (auto-detected) - more conservative
  mixed_precision: false
  amp_opt_level: "O1"
  gradient_clipping: 1.0
  
  # Curriculum learning
  curriculum_learning:
    enabled: false
    start_fraction: 0.3
    end_fraction: 1.0
    total_epochs: 20
  
  # Loss configuration for inpainting - DICE + Focal Loss (No CE)
  loss_config:
    # Simplified loss weights (automatically normalized to sum to 1)
    bce_weight: 0.5          # No Binary Cross-Entropy (removed for better performance)
    dice_weight: 0.5         # DICE loss weight (segmentation quality)
    focal_weight: 0.0        # Focal loss weight (hard examples & class balance)

    # Focal loss parameters
    focal_alpha: 1.0         # Alpha parameter for focal loss
    focal_gamma: 2.0         # Gamma parameter for focal loss

    # Other parameters
    use_frequency_weight: false  # Enable water frequency weighting
    use_adaptive_dice: false     # Use adaptive DICE loss

# Hardware configuration for 4x L40
hardware:
  num_cpu_threads: 16
  num_gpus: 4
  gpu_memory_limit: 40       # GB per GPU (L40 has 40GB)
  
  # Memory optimization for Swin Transformer
  gradient_checkpointing: false  # Disable for stability
  max_memory_per_gpu: 36        # Conservative limit
  
  # Distributed training settings
  distributed_backend: "nccl"
  find_unused_parameters: false
  sync_batchnorm: true
  
  # DDP settings
  ddp_bucket_cap_mb: 25
  ddp_broadcast_buffers: false

# Logging and checkpointing
logging:
  experiment_name: "swin_waternet_v10"
  # res_dir: "/fossfs/xiaozhen/Results/"
  res_dir: "/mnt/storage/xiaozhen/Results/"
  
  log_interval: 10
  eval_interval: 500
  save_interval: 5
  vis_interval: 1
  
  # Metrics to track
  metrics:
    - "loss"
    - "inpaint_accuracy"
    - "confidence_accuracy"
    - "missing_pixel_accuracy"
    - "water_pixel_accuracy"
    - "land_pixel_accuracy"
  
  # Tensorboard settings
  use_tensorboard: true
  tensorboard_update_freq: 100
  profile_gpu: false

# Visualization configuration
visualization:
  enabled: true
  vis_interval: 1
  num_vis_samples: 4
  freq_min: 0.2
  freq_max: 0.8

# Inference configuration
inference:
  batch_size: 32             # Reduced for Swin Transformer
  num_workers: 4
  
  # Output configuration
  save_predictions: true
  save_confidence_maps: true
  save_visualizations: true
  
  # Inference optimizations
  use_half_precision: true
  compile_model: true

# Validation and testing
validation:
  # Validation strategy
  validation_split: "temporal"
  val_years: [2019, 2020]
  
  # Early stopping
  early_stopping:
    enabled: false
    patience: 15
    monitor: "val_inpaint_acc"
    min_delta: 0.001
    mode: "max"
  
  # Validation frequency
  validate_every_n_epochs: 1
  
  # Best model selection
  save_best_only: true
  save_last: true

# Performance optimizations
performance:
  # Data loading
  dataloader_drop_last: true
  shuffle_buffer_size: 10000
  
  # GPU optimizations
  cudnn_benchmark: true
  cudnn_deterministic: false
  
  # Memory optimizations
  empty_cache_freq: 500
  
  # Distributed optimizations
  gradient_as_bucket_view: true
  static_graph: true

# Seed for reproducibility
seed: 42

# Debug options
debug:
  enabled: false
  detect_anomaly: false
  profiler_wait: 1
  profiler_warmup: 1
  profiler_active: 5
  profiler_repeat: 1 