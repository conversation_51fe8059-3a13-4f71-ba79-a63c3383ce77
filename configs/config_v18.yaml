# Configuration file for SwinWaterNetV18
# Enhanced architecture with attention mask and multi-scale patch attention

# Training configuration
training:
  batch_size: 8
  learning_rate: 1e-4
  weight_decay: 1e-4
  epochs: 100
  gradient_clip: 1.0
  warmup_steps: 1000
  
  # Mixed precision training
  use_amp: true
  
  # Scheduler settings
  scheduler:
    type: "cosine"
    warmup_epochs: 5
    min_lr: 1e-6

# Model configuration
model:
  name: "SwinWaterNetV18"
  swin_config:
    # Architecture parameters
    img_size: 256
    patch_sizes: [4, 8, 16, 32]  # Multi-scale patch sizes for first stage
    in_chans: 2                  # Input channels (water + missing mask)
    out_chans: 2                 # Output channels 
    embed_dim: 96                # Base embedding dimension
    depths: [2, 2, 6, 2]         # Number of blocks per stage
    num_heads: [3, 6, 12, 24]    # Number of attention heads per stage
    window_size: 8               # Window size for attention
    mlp_ratio: 4.0               # MLP expansion ratio
    
    # Dropout rates
    drop_rate: 0.0               # General dropout rate
    attn_drop_rate: 0.0          # Attention dropout rate
    drop_path_rate: 0.1          # Stochastic depth rate
    
    # Multi-scale attention parameters
    use_multiscale: true         # Enable multi-scale attention in first stage
    multiscale_patch_sizes: [4, 8, 16, 32]  # Patch sizes for multi-scale attention
    
    # Attention mechanism parameters
    qkv_bias: true               # Use bias in QKV projections
    
# Data configuration
data:
  # Sequence parameters
  num_frames: 48               # Total number of frames in sequence
  sequence_length: 120         # Extended sequence length for dataset compatibility
  tile_size: 256               # Spatial resolution
  
  # Missing pattern parameters
  expected_missing_ratio: 0.3  # Expected missing ratio for pattern filtering
  use_missing_augmentation: true
  
  # Data paths (update these for specific training)
  index_file: "/path/to/balanced_dataset_index.json"
  missing_db_file: "/path/to/missing_patterns_database.npz"
  
  # Data processing
  normalize: true
  augment: false               # Spatial augmentation (if implemented)

# Loss configuration
loss:
  # Primary losses
  inpaint_weight: 1.0          # Weight for inpainting loss
  
  # Loss types
  inpaint_loss: "l1"           # Options: l1, l2, smooth_l1
  
  # Advanced loss components (if implemented)
  perceptual_weight: 0.0       # Perceptual loss weight
  adversarial_weight: 0.0      # Adversarial loss weight
  
  # Temporal consistency (if implemented)
  temporal_weight: 0.0         # Temporal consistency loss weight

# Attention mask configuration
attention_mask:
  # Rules for attention computation
  missing_to_valid_only: true     # Missing queries only attend to valid keys
  valid_to_valid_normal: true     # Valid queries normally attend to valid keys  
  no_attend_to_missing_keys: true # No queries attend to missing keys
  
  # Temporal mask processing
  use_temporal_mask: true         # Use temporal dimension in missing mask
  patch_missing_threshold: 0.5    # Threshold for patch-level missing detection

# Multi-scale attention configuration  
multiscale_attention:
  enabled: true                   # Enable multi-scale attention
  scales: [4, 8, 16, 32]         # Patch sizes for different scales
  fusion_method: "concat"         # Options: concat, sum, weighted_sum
  
  # STTN-style parameters
  use_sttn_style: true           # Use STTN-style implementation
  num_heads: 8                   # Number of attention heads for multi-scale
  attn_drop: 0.0                 # Attention dropout
  proj_drop: 0.0                 # Projection dropout

# Optimization configuration
optimizer:
  type: "adamw"                  # Optimizer type
  betas: [0.9, 0.999]           # Adam betas
  eps: 1e-8                     # Adam epsilon
  
# Logging and checkpointing
logging:
  log_interval: 100             # Log every N steps
  eval_interval: 1000           # Evaluate every N steps
  save_interval: 5000           # Save checkpoint every N steps
  
  # Wandb logging (if used)
  use_wandb: false
  project_name: "swin_water_net_v18"
  
# Evaluation configuration
evaluation:
  metrics: ["mae", "mse", "psnr", "ssim"]  # Evaluation metrics
  save_images: true                         # Save sample images during evaluation
  num_samples: 10                          # Number of samples to save

# Hardware configuration
hardware:
  device: "cuda"                # Device to use
  num_workers: 8                # Number of data loader workers
  pin_memory: true              # Pin memory for faster GPU transfer
  
  # Multi-GPU settings
  use_ddp: false                # Use DistributedDataParallel
  local_rank: 0                 # Local rank for DDP
  
# Compatibility settings
compatibility:
  train_v8_compatible: true     # Ensure compatibility with train_v8.py
  use_legacy_format: false      # Use legacy data format if needed