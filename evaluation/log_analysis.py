#!/usr/bin/env python3
"""
示例：Step级别训练历史记录的使用

展示如何读取和分析step级别的训练历史记录
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def load_training_history(metrics_dir):
    """加载训练历史记录"""
    training_file = Path(metrics_dir) / 'training_history.json'
    validation_file = Path(metrics_dir) / 'validation_history.json'
    
    training_history = []
    validation_history = []
    
    # 加载训练历史
    if training_file.exists():
        with open(training_file, 'r') as f:
            training_history = json.load(f)
    
    # 加载验证历史
    if validation_file.exists():
        with open(validation_file, 'r') as f:
            validation_history = json.load(f)
    
    return training_history, validation_history

def analyze_training_progress(training_history, validation_history):
    """分析训练进度"""
    print("=== 训练历史分析 ===")
    
    if training_history:
        train_steps = [record['global_step'] for record in training_history]
        train_losses = [record['train_loss_avg'] for record in training_history]
        
        print(f"训练记录数量: {len(training_history)}")
        print(f"训练步数范围: {min(train_steps)} - {max(train_steps)}")
        print(f"训练损失范围: {min(train_losses):.4f} - {max(train_losses):.4f}")
        print(f"最终训练损失: {train_losses[-1]:.4f}")
    
    if validation_history:
        val_steps = [record['global_step'] for record in validation_history]
        val_losses = [record['val_loss'] for record in validation_history]
        
        # 获取最佳验证指标
        best_acc = max(record['val_metrics']['accuracy_0_4_0_6'] for record in validation_history)
        best_f1 = max(record['val_metrics']['f1_score_0_4_0_6'] for record in validation_history)
        best_iou = max(record['val_metrics']['iou_0_4_0_6'] for record in validation_history)
        
        print(f"\n验证记录数量: {len(validation_history)}")
        print(f"验证步数范围: {min(val_steps)} - {max(val_steps)}")
        print(f"验证损失范围: {min(val_losses):.4f} - {max(val_losses):.4f}")
        print(f"最佳验证指标 (0.4-0.6):")
        print(f"  Accuracy: {best_acc:.4f}")
        print(f"  F1 Score: {best_f1:.4f}")
        print(f"  IoU: {best_iou:.4f}")

def plot_training_curves(training_history, validation_history, save_path=None):
    """绘制训练曲线"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 训练损失曲线
    if training_history:
        train_steps = [record['global_step'] for record in training_history]
        train_losses = [record['train_loss_avg'] for record in training_history]
        
        axes[0, 0].plot(train_steps, train_losses, 'b-', alpha=0.7, label='Training Loss')
        axes[0, 0].set_title('Training Loss vs Steps')
        axes[0, 0].set_xlabel('Global Step')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
    
    # 验证损失曲线
    if validation_history:
        val_steps = [record['global_step'] for record in validation_history]
        val_losses = [record['val_loss'] for record in validation_history]
        
        axes[0, 1].plot(val_steps, val_losses, 'r-', alpha=0.7, label='Validation Loss')
        axes[0, 1].set_title('Validation Loss vs Steps')
        axes[0, 1].set_xlabel('Global Step')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 验证指标曲线
        accuracies = [record['val_metrics']['accuracy_0_4_0_6'] for record in validation_history]
        f1_scores = [record['val_metrics']['f1_score_0_4_0_6'] for record in validation_history]
        ious = [record['val_metrics']['iou_0_4_0_6'] for record in validation_history]
        
        axes[1, 0].plot(val_steps, accuracies, 'g-', alpha=0.7, label='Accuracy')
        axes[1, 0].plot(val_steps, f1_scores, 'b-', alpha=0.7, label='F1 Score')
        axes[1, 0].plot(val_steps, ious, 'r-', alpha=0.7, label='IoU')
        axes[1, 0].set_title('Validation Metrics (0.4-0.6) vs Steps')
        axes[1, 0].set_xlabel('Global Step')
        axes[1, 0].set_ylabel('Metric Value')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 损失对比
        if training_history:
            # 插值验证损失到训练步数
            val_loss_interp = np.interp(train_steps, val_steps, val_losses)
            
            axes[1, 1].plot(train_steps, train_losses, 'b-', alpha=0.7, label='Training Loss')
            axes[1, 1].plot(train_steps, val_loss_interp, 'r-', alpha=0.7, label='Validation Loss (interpolated)')
            axes[1, 1].set_title('Training vs Validation Loss')
            axes[1, 1].set_xlabel('Global Step')
            axes[1, 1].set_ylabel('Loss')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练曲线已保存到: {save_path}")
    
    plt.show()

def find_best_validation_step(validation_history):
    """找到最佳验证步数"""
    if not validation_history:
        return None
    
    best_record = max(validation_history, 
                     key=lambda x: x['val_metrics']['accuracy_0_4_0_6'])
    
    print(f"\n=== 最佳验证结果 ===")
    print(f"Global Step: {best_record['global_step']}")
    print(f"Epoch: {best_record['epoch']}")
    print(f"Validation Loss: {best_record['val_loss']:.4f}")
    print(f"Validation Metrics (0.4-0.6):")
    print(f"  Accuracy: {best_record['val_metrics']['accuracy_0_4_0_6']:.4f}")
    print(f"  F1 Score: {best_record['val_metrics']['f1_score_0_4_0_6']:.4f}")
    print(f"  IoU: {best_record['val_metrics']['iou_0_4_0_6']:.4f}")
    print(f"Timestamp: {best_record['timestamp']}")
    
    return best_record

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='分析step级别训练历史')
    parser.add_argument('--metrics_dir', type=str, required=True,
                       help='指标文件目录路径')
    parser.add_argument('--plot', action='store_true',
                       help='绘制训练曲线')
    parser.add_argument('--save_plot', type=str, default=None,
                       help='保存图片的路径')
    
    args = parser.parse_args()
    
    # 检查目录是否存在
    metrics_dir = Path(args.metrics_dir)
    if not metrics_dir.exists():
        print(f"错误: 指标目录不存在: {metrics_dir}")
        return
    
    # 加载历史记录
    training_history, validation_history = load_training_history(metrics_dir)
    
    if not training_history and not validation_history:
        print("未找到训练历史记录文件")
        return
    
    # 分析训练进度
    analyze_training_progress(training_history, validation_history)
    
    # 找到最佳验证结果
    find_best_validation_step(validation_history)
    
    # 绘制训练曲线
    if args.plot:
        plot_training_curves(training_history, validation_history, args.save_plot)

if __name__ == "__main__":
    main()
