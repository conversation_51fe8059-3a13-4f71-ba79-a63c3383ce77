"""
Optimized Configuration for Validation Sample Generation

This module provides optimized configuration parameters for efficient
validation sample generation with improved parallel processing.

Author: <PERSON> Assistant  
Date: 2025-08-02
"""

import os
import multiprocessing
from typing import Dict, Any


class OptimizedValidationConfig:
    """Optimized configuration for validation sample generation"""
    
    def __init__(self):
        """Initialize optimized configuration based on system resources"""
        self.cpu_count = multiprocessing.cpu_count()
        self.memory_gb = self._get_available_memory_gb()
        
    def _get_available_memory_gb(self) -> float:
        """Get available system memory in GB"""
        try:
            import psutil
            return psutil.virtual_memory().available / (1024**3)
        except ImportError:
            # Fallback estimate
            return 16.0
    
    def get_optimized_params(self) -> Dict[str, Any]:
        """Get optimized parameters based on system resources"""
        
        # Base configuration
        config = {
            # Parallel processing
            'max_workers': min(16, self.cpu_count * 2),

            # I/O optimization
            'spatial_sample_factor': 0.01,  # Increased from 0.01
            'chunk_size': 100,  # Larger chunks for better I/O
            'batch_size': 2000,  # Larger batches for spatial reading
            'enable_vectorization': True,

            # Caching
            'enable_spatial_cache': True,
            'cache_size_limit': 100,  # Max cached spatial locations
            
            # Data processing
            'enable_early_filtering': True,
            'min_valid_samples_per_file': 10,
            'skip_empty_files': True,
            
            # Distribution optimization
            'stratification_bins': 5,
            'enable_adaptive_binning': True,
            'reservoir_sampling': True,
            'balanced_sampling': True,
        }
        
        return config
    
    def get_netcdf_config(self) -> Dict[str, Any]:
        """Get optimized NetCDF reading configuration"""
        return {
            'engine_priority': ['netcdf4', 'h5netcdf'],
            'chunk_config': {
                'time': 200,
                'idx_x': 100,
                'idx_y': 100
            },
            'decode_times': True,
            'mask_and_scale': True,
            'use_cftime': False,
        }
    
    def get_sampling_strategy(self) -> Dict[str, Any]:
        """Get optimized sampling strategy configuration"""
        return {
            'spatial_strategy': 'stratified_random',  # Better than pure random
            'temporal_strategy': 'uniform',
            'enable_geographic_balance': True,
            'min_spatial_distance': 0.01,  # Minimum distance between samples
            'temporal_coverage_target': 0.5,  # Target 80% temporal coverage
        }
    
    def get_performance_monitoring(self) -> Dict[str, Any]:
        """Get performance monitoring configuration"""
        return {
            'enable_profiling': False,
            'log_processing_time': True,
            'progress_update_frequency': 100,  # Update progress every 100 files
            'enable_detailed_logging': False,
        }


def get_default_optimized_config() -> Dict[str, Any]:
    """Get default optimized configuration"""
    config_manager = OptimizedValidationConfig()
    
    base_config = config_manager.get_optimized_params()
    base_config.update({
        'netcdf': config_manager.get_netcdf_config(),
        'sampling': config_manager.get_sampling_strategy(),
        'monitoring': config_manager.get_performance_monitoring(),
    })
    
    return base_config


def print_system_info():
    """Print system information for optimization"""
    config_manager = OptimizedValidationConfig()
    
    print("System Information for Validation Optimization:")
    print(f"  CPU cores: {config_manager.cpu_count}")
    print(f"  Available memory: {config_manager.memory_gb:.1f} GB")
    
    config = config_manager.get_optimized_params()
    print(f"  Recommended workers: {config['max_workers']}")
    print(f"  Recommended chunk size: {config['chunk_size']}")
    print(f"  Recommended batch size: {config['batch_size']}")
    print(f"  Spatial sample factor: {config['spatial_sample_factor']}")


if __name__ == "__main__":
    print_system_info()
    
    config = get_default_optimized_config()
    print("\nOptimized Configuration:")
    for key, value in config.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for subkey, subvalue in value.items():
                print(f"    {subkey}: {subvalue}")
        else:
            print(f"  {key}: {value}")
