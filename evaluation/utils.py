"""
Utility Functions for Water Body Model Validation

This module implements utility functions for:
1. Histogram analysis of key variables
2. Geographical distribution visualization
3. Academic-standard visualizations with proper formatting

Author: <PERSON> Assistant
Date: 2025-08-02
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging
import json
import yaml

logger = logging.getLogger(__name__)

# Set matplotlib style for academic papers
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 11,
    'figure.titlesize': 16,
    'font.family': 'serif',
    'mathtext.fontset': 'stix'
})


def analyze_sample_distributions(samples: List[Dict],
                               output_dir: Union[str, Path],
                               title_prefix: str = "Validation",
                               config_path: str = "configs/config_v20.yaml") -> str:
    """
    Generate histogram analysis for key variables from validation dataset samples
    using intervals specified in config_v20.yaml

    Args:
        samples: List of sample dictionaries from validation_dataset.samples
        output_dir: Directory to save plots
        title_prefix: Prefix for plot titles
        config_path: Path to config file containing bin specifications

    Returns:
        Path to saved histogram plot
    """

    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Analyzing distributions for {len(samples)} samples")

    # Load config to get bin specifications
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        feature_bins = config['sampling']['feature_bins']
        logger.info(f"Loaded bin specifications from {config_path}")
    except Exception as e:
        logger.warning(f"Could not load config from {config_path}: {e}")
        # Fallback to default bins
        feature_bins = {
            'water_proportion_bins': [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
            'missing_proportion_bins': [0.05, 0.2, 0.4, 0.6, 0.8, 1.0],
            'mean_water_frequency_bins': [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
            'year_bins': [1984, 1995, 2005, 2015, 2025],
            'month_bins': [1, 3, 5, 7, 9, 11]
        }

    # Extract variables from validation dataset samples
    water_frequencies = []
    water_ratios = []
    years = []
    months = []
    missing_ratios = []

    for s in samples:
        # Handle both direct sample format and metadata format
        if 'mean_water_frequency' in s:
            water_frequencies.append(s['mean_water_frequency'])
        else:
            logger.warning(f"Sample missing 'mean_water_frequency': {s}")
            water_frequencies.append(0.5)  # Default value

        if 'water_proportion' in s:
            water_ratios.append(s['water_proportion'])
        else:
            logger.warning(f"Sample missing 'water_proportion': {s}")
            water_ratios.append(0.0)  # Default value

        if 'year' in s:
            years.append(s['year'])
        else:
            logger.warning(f"Sample missing 'year': {s}")
            years.append(2020)  # Default value

        if 'month' in s:
            months.append(s['month'])
        else:
            logger.warning(f"Sample missing 'month': {s}")
            months.append(6)  # Default value

        # Handle missing proportion with multiple possible field names
        if 'simulated_missing_ratio' in s:
            missing_ratios.append(s['simulated_missing_ratio'])
        else:
            logger.warning(f"Sample missing 'simulated_missing_ratio': {s}")
            missing_ratios.append(0.0)  # Default to no missing data

    # Create figure with 5 subplots (5 rows, 1 column)
    fig, axes = plt.subplots(5, 1, figsize=(10, 20))
    fig.suptitle(f'{title_prefix} Sample Distribution Analysis (Config v20 Bins)', fontsize=16, y=0.995)

    # 1. Water Frequency Distribution using config bins
    water_freq_bins = feature_bins['mean_water_frequency_bins']
    axes[0].hist(water_frequencies, bins=water_freq_bins, alpha=0.7, color='blue', edgecolor='black')
    axes[0].set_xlabel('Water Frequency')
    axes[0].set_ylabel('Count')
    axes[0].set_title(f'Water Frequency Distribution (Bins: {water_freq_bins})')
    axes[0].grid(True, alpha=0.3)
    axes[0].set_xlim(water_freq_bins[0], water_freq_bins[-1])

    # Add statistics
    mean_freq = np.mean(water_frequencies)
    std_freq = np.std(water_frequencies)
    axes[0].axvline(mean_freq, color='red', linestyle='--',
                   label=f'Mean: {mean_freq:.3f} ± {std_freq:.3f}')
    axes[0].legend()

    # Add bin boundaries as vertical lines
    for bin_edge in water_freq_bins[1:-1]:  # Skip first and last
        axes[0].axvline(bin_edge, color='gray', linestyle=':', alpha=0.5)

    # 2. Water Proportion Distribution using config bins
    water_prop_bins = feature_bins['water_proportion_bins']
    axes[1].hist(water_ratios, bins=water_prop_bins, alpha=0.7, color='cyan', edgecolor='black')
    axes[1].set_xlabel('Water Proportion')
    axes[1].set_ylabel('Count')
    axes[1].set_title(f'Water Proportion Distribution (Bins: {water_prop_bins})')
    axes[1].grid(True, alpha=0.3)
    axes[1].set_xlim(water_prop_bins[0], water_prop_bins[-1])

    mean_ratio = np.mean(water_ratios)
    std_ratio = np.std(water_ratios)
    axes[1].axvline(mean_ratio, color='red', linestyle='--',
                   label=f'Mean: {mean_ratio:.3f} ± {std_ratio:.3f}')
    axes[1].legend()

    # Add bin boundaries as vertical lines
    for bin_edge in water_prop_bins[1:-1]:  # Skip first and last
        axes[1].axvline(bin_edge, color='gray', linestyle=':', alpha=0.5)

    # 3. Missing Proportion Distribution using config bins
    missing_prop_bins = feature_bins['missing_proportion_bins']
    axes[2].hist(missing_ratios, bins=missing_prop_bins, alpha=0.7, color='orange', edgecolor='black')
    axes[2].set_xlabel('Missing Proportion')
    axes[2].set_ylabel('Count')
    axes[2].set_title(f'Missing Proportion Distribution (Bins: {missing_prop_bins})')
    axes[2].grid(True, alpha=0.3)
    axes[2].set_xlim(missing_prop_bins[0], missing_prop_bins[-1])

    mean_missing = np.mean(missing_ratios)
    std_missing = np.std(missing_ratios)
    axes[2].axvline(mean_missing, color='red', linestyle='--',
                   label=f'Mean: {mean_missing:.3f} ± {std_missing:.3f}')
    axes[2].legend()

    # Add bin boundaries as vertical lines
    for bin_edge in missing_prop_bins[1:-1]:  # Skip first and last
        axes[2].axvline(bin_edge, color='gray', linestyle=':', alpha=0.5)

    # 4. Year Distribution using config bins
    year_bins = feature_bins['year_bins']
    axes[3].hist(years, bins=year_bins, alpha=0.7, color='green', edgecolor='black')
    axes[3].set_xlabel('Year')
    axes[3].set_ylabel('Count')
    axes[3].set_title(f'Temporal Distribution by Year (Bins: {year_bins})')
    axes[3].grid(True, alpha=0.3)
    axes[3].set_xlim(year_bins[0], year_bins[-1])
    axes[3].tick_params(axis='x', rotation=45)

    # Add bin boundaries as vertical lines
    for bin_edge in year_bins[1:-1]:  # Skip first and last
        axes[3].axvline(bin_edge, color='gray', linestyle=':', alpha=0.5)

    # 5. Month Distribution using config bins
    month_bins = feature_bins['month_bins']
    # For months, we need to handle the binning differently since it's categorical
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    # Create histogram using month bins from config
    axes[4].hist(months, bins=month_bins + [12], alpha=0.7, color='purple', edgecolor='black')
    axes[4].set_xlabel('Month')
    axes[4].set_ylabel('Count')
    axes[4].set_title(f'Temporal Distribution by Month (Bins: {month_bins})')
    axes[4].set_xticks(range(1, 13))
    axes[4].set_xticklabels(month_names)
    axes[4].grid(True, alpha=0.3)
    axes[4].tick_params(axis='x', rotation=45)

    # Add bin boundaries as vertical lines
    for bin_edge in month_bins[1:]:  # Skip first
        axes[4].axvline(bin_edge, color='gray', linestyle=':', alpha=0.5)

    # Adjust layout and save
    plt.tight_layout(rect=[0, 0.03, 1, 0.97])

    output_path = output_dir / f'{title_prefix.lower()}_histogram_analysis.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Histogram analysis saved to {output_path}")
    return str(output_path)


def visualize_geographical_distribution(samples: List[Dict],
                                      output_dir: Union[str, Path],
                                      metric_name: Optional[str] = None,
                                      metric_values: Optional[List[float]] = None,
                                      title_prefix: str = "Validation") -> str:
    """
    Visualize global sample distribution using world map projection

    Args:
        samples: List of sample dictionaries from validation_dataset.samples
        output_dir: Directory to save plots
        metric_name: Name of metric to overlay (e.g., 'Accuracy')
        metric_values: Values of metric to overlay
        title_prefix: Prefix for plot titles

    Returns:
        Path to saved geographical plot
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Creating geographical distribution plot for {len(samples)} samples")

    # Extract coordinates with error handling
    lons = []
    lats = []

    for s in samples:
        if 'tile_lon' in s and 'tile_lat' in s:
            lons.append(s['tile_lon'])
            lats.append(s['tile_lat'])
        else:
            logger.warning(f"Sample missing coordinates: {s}")
            lons.append(0.0)  # Default longitude
            lats.append(0.0)  # Default latitude

    # Use Cartopy for world map visualization (required)
    import cartopy.crs as ccrs
    import cartopy.feature as cfeature
    logger.info("Using Cartopy for world map visualization (no matplotlib fallback)")

    # Determine whether we will plot a metric overlay or density
    # New behavior: if metric_values is provided, strictly pair by the same indices
    # (i) Only indices where a metric exists, is finite (not NaN), and i < len(samples) are used
    # (ii) No reordering or shuffling; we keep original order to avoid any mixing
    use_metric = metric_values is not None
    valid_lons = []
    valid_lats = []
    valid_values = []

    if use_metric:
        indices = []
        for i, v in enumerate(metric_values):
            if i < len(samples):
                try:
                    vf = float(v)
                except Exception:
                    continue
                if not np.isnan(vf):
                    indices.append(i)
        if len(metric_values) != len(samples):
            logger.warning(
                f"metric_values length ({len(metric_values)}) != samples ({len(samples)}); "
                f"subsetting to {len(indices)} aligned positions by index.")
        valid_lons = [lons[i] for i in indices]
        valid_lats = [lats[i] for i in indices]
        valid_values = [metric_values[i] for i in indices]

    if use_metric and len(valid_values) > 0:
        # Enhanced figure with side panels: map + longitude/latitude statistics
        from matplotlib.gridspec import GridSpec

        # Prepare metrics in percentage for display
        values_np = np.asarray(valid_values, dtype=float)
        values_pct = np.clip(values_np * 100.0, 0.0, 100.0)

        # Aggregate by unique longitudes/latitudes for side plots
        try:
            import pandas as _pd
            df = _pd.DataFrame({'lon': valid_lons, 'lat': valid_lats, 'val': values_pct})
            lon_stats = df.groupby('lon', as_index=False)['val'].mean()
            lat_stats = df.groupby('lat', as_index=False)['val'].mean()
        except Exception:
            # Fallback without pandas (should not happen as pandas is required)
            lon_stats = None
            lat_stats = None

        # Build layout
        fig = plt.figure(figsize=(18, 12))
        gs = GridSpec(nrows=2, ncols=2, width_ratios=[4.0, 1.2], height_ratios=[3.0, 1.2], figure=fig)
        ax_map = fig.add_subplot(gs[0, 0], projection=ccrs.PlateCarree())
        ax_lon = fig.add_subplot(gs[1, 0])  # Longitude vs metric
        ax_lat = fig.add_subplot(gs[0, 1])  # Metric vs Latitude
        ax_cbar = fig.add_subplot(gs[1, 1])

        # Map base features
        ax_map.set_global()
        ax_map.add_feature(cfeature.COASTLINE, linewidth=0.5, edgecolor='black')
        ax_map.add_feature(cfeature.BORDERS, linewidth=0.3)
        ax_map.add_feature(cfeature.LAND, alpha=0.3, color='lightgray')
        ax_map.add_feature(cfeature.OCEAN, alpha=0.3, color='lightblue')
        ax_map.gridlines(draw_labels=True, alpha=0.3)

        # Scatter on map
        scatter = ax_map.scatter(valid_lons, valid_lats, c=values_pct, s=15, alpha=0.9,
                                 cmap='viridis', transform=ccrs.PlateCarree(),
                                 edgecolors='black', linewidth=0.1)
        title_metric = metric_name if metric_name is not None else "Metric (%)"
        ax_map.set_title(f'{title_prefix} {title_metric}', fontsize=14, pad=12)
        ax_map.set_extent([-180, 180, -90, 90])

        # Colorbar (horizontal) — draw directly into the reserved cbar axes
        cbar = fig.colorbar(scatter, cax=ax_cbar, orientation='horizontal')
        cbar.set_label(title_metric if title_metric else 'Metric (%)', fontsize=12)

        # Longitude statistics (mean metric by lon)
        ax_lon.set_xlim([-180, 180])
        ax_lon.set_ylim([-5, 105])
        ax_lon.set_xticks(list(range(-180, 181, 60)))
        ax_lon.set_yticks(list(range(0, 101, 20)))
        ax_lon.tick_params(axis='x', direction='in', length=3)
        ax_lon.grid(True, linestyle='--', alpha=0.3)
        ax_lon.set_title('Longitude', y=1.02)
        if lon_stats is not None:
            ax_lon.scatter(lon_stats['lon'], lon_stats['val'], c=lon_stats['val'], cmap='viridis', s=10)

        # Latitude statistics (mean metric by lat) - horizontal orientation
        ax_lat.set_ylim([-90, 90])
        ax_lat.set_xlim([-5, 105])
        ax_lat.set_yticks(list(range(-90, 91, 30)))
        ax_lat.set_xticks(list(range(0, 101, 20)))
        ax_lat.tick_params(axis='y', direction='in', length=3)
        ax_lat.grid(True, linestyle='--', alpha=0.3)
        ax_lat.set_ylabel('Latitude', fontsize=12)
        if lat_stats is not None:
            ax_lat.scatter(lat_stats['val'], lat_stats['lat'], c=lat_stats['val'], cmap='viridis', s=10)
    else:
        # Create a single figure with Cartopy projection (density-only visualization)
        fig = plt.figure(figsize=(20, 12))
        ax = fig.add_subplot(1, 1, 1, projection=ccrs.PlateCarree())
        ax.set_global()
        ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax.add_feature(cfeature.BORDERS, linewidth=0.3)
        ax.add_feature(cfeature.LAND, alpha=0.3, color='lightgray')
        ax.add_feature(cfeature.OCEAN, alpha=0.3, color='lightblue')
        ax.gridlines(draw_labels=True, alpha=0.3)
        # Add water frequency raster basemap (0=white, 1=deep blue)
        try:
            import rasterio as rio
            from matplotlib.colors import LinearSegmentedColormap
            tif_path = "/mnt/storage/xiaozhen/Water/JRC_max_extent_land.tif"
            with rio.open(tif_path) as src:
                data = src.read(1).astype(np.float32)
                nodata = src.nodata
                if nodata is not None:
                    data = np.where(data == nodata, np.nan)
                data = np.clip(data, 0.0, 1.0)
                bounds = src.bounds
                extent = [bounds.left, bounds.right, bounds.bottom, bounds.top]
                # Determine appropriate CRS for the raster
                crs_obj = src.crs
                data_crs = ccrs.PlateCarree()
                try:
                    if crs_obj is not None:
                        # If the raster CRS is geographic (e.g., EPSG:4326), use PlateCarree
                        if hasattr(crs_obj, 'is_geographic') and crs_obj.is_geographic:
                            data_crs = ccrs.PlateCarree()
                        else:
                            epsg = None
                            try:
                                epsg = crs_obj.to_epsg()
                            except Exception:
                                epsg = None
                            if epsg is not None:
                                try:
                                    data_crs = ccrs.epsg(epsg)
                                except Exception:
                                    # Fallback if EPSG not a projection
                                    data_crs = ccrs.PlateCarree()
                except Exception:
                    data_crs = ccrs.PlateCarree()

                cmap = LinearSegmentedColormap.from_list('waterfreq', ['#FFFFFF', '#00008B'])
                cmap.set_bad((0, 0, 0, 0))  # Transparent for NaN

                # Try imshow first; some Matplotlib backends may raise errors with transform+extent
                try:
                    base_img = ax.imshow(
                        data,
                        extent=extent,
                        origin='upper',
                        transform=data_crs,
                        cmap=cmap,
                        vmin=0.0,
                        vmax=1.0,
                        zorder=1.0,
                        interpolation='nearest'
                    )
                except Exception:
                    # Fallback to pcolormesh with explicit coordinates
                    # Build 2D corner grids to avoid backend-specific issues
                    xx = np.linspace(bounds.left, bounds.right, data.shape[1] + 1)
                    yy = np.linspace(bounds.bottom, bounds.top, data.shape[0] + 1)
                    XX, YY = np.meshgrid(xx, yy)
                    data_ma = np.ma.masked_invalid(data)
                    if np.all(np.ma.getmaskarray(np.ma.masked_invalid(data_ma))):
                        # All values are NaN/masked - skip basemap to avoid render errors
                        base_img = None
                    else:
                        base_img = ax.pcolormesh(
                            XX, YY, data_ma,
                            cmap=cmap, vmin=0.0, vmax=1.0,
                            transform=data_crs, zorder=1.0, shading='auto'
                        )
                # Add aesthetic horizontal colorbar for water frequency basemap
                if base_img is not None:
                    try:
                        from mpl_toolkits.axes_grid1.inset_locator import inset_axes
                        cax = inset_axes(ax, width="35%", height="3%", loc='lower left',
                                         bbox_to_anchor=(0.05, 0.02, 0.9, 0.9),
                                         bbox_transform=ax.transAxes, borderpad=0)
                        cbar_base = plt.colorbar(base_img, cax=cax, orientation='horizontal')
                    except Exception:
                        cbar_base = plt.colorbar(base_img, ax=ax, orientation='horizontal', fraction=0.046, pad=0.08)
                    cbar_base.set_label('Water Frequency (0–1)', rotation=0, labelpad=6, fontsize=11)
                    cbar_base.ax.tick_params(labelsize=9)
                else:
                    logger.info("Water frequency basemap is empty after masking; skipping colorbar.")
        except Exception as e:
            logger.warning(f"Failed to add water frequency basemap: {e}")

        # Plot sample density (color encodes density)
        if len(lons) > 1:
            try:
                from scipy.stats import gaussian_kde
                xy = np.vstack([lons, lats])
                kde = gaussian_kde(xy)
                density = kde(xy)
                scatter = ax.scatter(lons, lats, c=density, s=80, alpha=0.8,
                                     cmap='plasma', transform=ccrs.PlateCarree(),
                                     edgecolors='black', linewidth=0.5)
                ax.set_title(f'{title_prefix} Sample Density Distribution', fontsize=14, pad=20)
                cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, pad=0.05, aspect=30)
                cbar.set_label('Sample Density', rotation=270, labelpad=20, fontsize=12)
            except Exception as e:
                logger.warning(f"KDE density computation failed ({e}); falling back to simple scatter.")
                ax.scatter(lons, lats, c='orange', s=80, alpha=0.8,
                           transform=ccrs.PlateCarree(),
                           edgecolors='black', linewidth=0.5)
                ax.set_title(f'{title_prefix} Sample Locations', fontsize=14, pad=20)
        else:
            ax.scatter(lons, lats, c='orange', s=80, alpha=0.8,
                       transform=ccrs.PlateCarree(),
                       edgecolors='black', linewidth=0.5)
            ax.set_title(f'{title_prefix} Sample Locations', fontsize=14, pad=20)

    # Add sample statistics text box
    stats_text = f'Total Samples: {len(samples)}\n'
    stats_text += f'Lat Range: [{np.min(lats):.1f}°, {np.max(lats):.1f}°]\n'
    stats_text += f'Lon Range: [{np.min(lons):.1f}°, {np.max(lons):.1f}°]\n'
    stats_text += f'Geographic Coverage: {len(set(zip(lons, lats)))} unique locations'
    # Choose the correct axes for annotation depending on the branch
    target_ax = None
    try:
        target_ax = ax_map if use_metric else ax
    except NameError:
        # Fallback if variable not defined for some reason
        target_ax = ax if 'ax' in locals() else None
    if target_ax is not None:
        target_ax.text(0.02, 0.98, stats_text, transform=target_ax.transAxes, fontsize=11,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

    # Ensure filename suffix only when a metric was plotted
    if not use_metric:
        metric_name = None

    plt.tight_layout()

    # Save plot
    suffix = f"_with_{metric_name.lower()}" if metric_name else ""
    output_path = output_dir / f'{title_prefix.lower()}_geographical_distribution{suffix}.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Geographical distribution plot saved to {output_path}")
    return str(output_path)


def create_correlation_matrix(samples: List[Dict],
                            output_dir: Union[str, Path],
                            title_prefix: str = "Validation") -> str:
    """
    Create correlation matrix for key variables from validation dataset samples

    Args:
        samples: List of sample dictionaries from validation_dataset.samples
        output_dir: Directory to save plots
        title_prefix: Prefix for plot titles

    Returns:
        Path to saved correlation matrix plot
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Creating correlation matrix for {len(samples)} samples")

    # Extract variables for correlation analysis with error handling
    variables = {}

    # Water Frequency
    water_frequencies = []
    for s in samples:
        if 'mean_water_frequency' in s:
            water_frequencies.append(s['mean_water_frequency'])
        else:
            water_frequencies.append(0.5)  # Default value
    variables['Water Frequency'] = water_frequencies

    # Water Ratio
    water_ratios = []
    for s in samples:
        if 'water_proportion' in s:
            water_ratios.append(s['water_proportion'])
        else:
            water_ratios.append(0.0)  # Default value
    variables['Water Ratio'] = water_ratios

    # Latitude
    lats = []
    for s in samples:
        if 'tile_lat' in s:
            lats.append(s['tile_lat'])
        else:
            lats.append(0.0)  # Default value
    variables['Latitude'] = lats

    # Longitude
    lons = []
    for s in samples:
        if 'tile_lon' in s:
            lons.append(s['tile_lon'])
        else:
            lons.append(0.0)  # Default value
    variables['Longitude'] = lons

    # Year
    years = []
    for s in samples:
        if 'year' in s:
            years.append(s['year'])
        else:
            years.append(2020)  # Default value
    variables['Year'] = years

    # Month
    months = []
    for s in samples:
        if 'month' in s:
            months.append(s['month'])
        else:
            months.append(6)  # Default value
    variables['Month'] = months

    # Add missing ratio if available
    missing_ratios = []
    for s in samples:
        if 'simulated_missing_ratio' in s:
            missing_ratios.append(s['simulated_missing_ratio'])
        else:
            missing_ratios.append(0.0)

    if any(mr > 0 for mr in missing_ratios):
        variables['Missing Ratio'] = missing_ratios

    # Create DataFrame
    df = pd.DataFrame(variables)

    # Calculate correlation matrix
    corr_matrix = df.corr()

    # Create plot
    fig, ax = plt.subplots(figsize=(10, 8))

    # Create heatmap
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))  # Mask upper triangle
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": 0.8}, ax=ax)

    ax.set_title(f'{title_prefix} Sample Variable Correlations')

    plt.tight_layout()

    # Save plot
    output_path = output_dir / f'{title_prefix.lower()}_correlation_matrix.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Correlation matrix saved to {output_path}")
    return str(output_path)


def analyze_validation_dataset_distributions(validation_dataset,
                                          output_dir: Union[str, Path],
                                          title_prefix: str = "Validation") -> Dict[str, str]:
    """
    Analyze sample distributions directly from a validation dataset object

    Args:
        validation_dataset: WaterBodyDataset object with samples attribute
        output_dir: Directory to save plots
        title_prefix: Prefix for plot titles

    Returns:
        Dictionary with paths to all generated plots
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Analyzing validation dataset with {len(validation_dataset.samples)} samples")

    # Extract samples from the validation dataset
    samples = validation_dataset.samples

    # Generate comprehensive report using the extracted samples
    return generate_comprehensive_report(
        samples=samples,
        output_dir=output_dir,
        title_prefix=title_prefix
    )


def generate_comprehensive_report(samples: List[Dict],
                                output_dir: Union[str, Path],
                                title_prefix: str = "Validation",
                                metric_name: Optional[str] = None,
                                metric_values: Optional[List[float]] = None) -> Dict[str, str]:
    """
    Generate comprehensive visualization report from validation dataset samples

    Args:
        samples: List of sample dictionaries from validation_dataset.samples
        output_dir: Directory to save all plots
        title_prefix: Prefix for all plot titles
        metric_name: Name of metric to overlay in geographical plot
        metric_values: Values of metric to overlay

    Returns:
        Dictionary with paths to all generated plots
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Generating comprehensive visualization report for {len(samples)} samples")

    plot_paths = {}

    # Generate all visualizations
    try:
        plot_paths['histogram'] = analyze_sample_distributions(samples, output_dir, title_prefix)
    except Exception as e:
        logger.error(f"Failed to generate histogram analysis: {e}")

    # Generate summary statistics with error handling
    stats_path = output_dir / f'{title_prefix.lower()}_summary_statistics.json'

    # Extract values with error handling
    water_frequencies = []
    water_ratios = []
    lats = []
    lons = []
    years = []
    months = []

    for s in samples:
        water_frequencies.append(s.get('mean_water_frequency', 0.5))
        water_ratios.append(s.get('water_proportion', 0.0))
        lats.append(s.get('tile_lat', 0.0))
        lons.append(s.get('tile_lon', 0.0))
        years.append(s.get('year', 2020))
        months.append(s.get('month', 6))

    summary_stats = {
        'total_samples': len(samples),
        'water_frequency_stats': {
            'mean': float(np.mean(water_frequencies)),
            'std': float(np.std(water_frequencies)),
            'min': float(np.min(water_frequencies)),
            'max': float(np.max(water_frequencies))
        },
        'water_ratio_stats': {
            'mean': float(np.mean(water_ratios)),
            'std': float(np.std(water_ratios)),
            'min': float(np.min(water_ratios)),
            'max': float(np.max(water_ratios))
        },
        'spatial_coverage': {
            'lat_range': [float(np.min(lats)), float(np.max(lats))],
            'lon_range': [float(np.min(lons)), float(np.max(lons))],
            'lat_mean': float(np.mean(lats)),
            'lon_mean': float(np.mean(lons))
        },
        'temporal_coverage': {
            'year_range': [int(np.min(years)), int(np.max(years))],
            'unique_years': len(set(years)),
            'unique_months': len(set(months)),
            'year_distribution': {str(year): years.count(year) for year in set(years)},
            'month_distribution': {str(month): months.count(month) for month in set(months)}
        }
    }

    if metric_values:
        summary_stats['metric_stats'] = {
            'metric_name': metric_name,
            'mean': float(np.mean(metric_values)),
            'std': float(np.std(metric_values)),
            'min': float(np.min(metric_values)),
            'max': float(np.max(metric_values))
        }

    with open(stats_path, 'w') as f:
        json.dump(summary_stats, f, indent=2)

    plot_paths['summary_stats'] = str(stats_path)

    logger.info(f"Comprehensive report generated in {output_dir}")
    logger.info(f"Generated plots: {list(plot_paths.keys())}")

    return plot_paths


def main():
    """Main function for testing utility functions"""
    import argparse

    parser = argparse.ArgumentParser(description="Test visualization utilities")
    parser.add_argument("--samples_file", type=str, required=True,
                       help="Path to samples JSON file")
    parser.add_argument("--output_dir", type=str, default="./validation_plots",
                       help="Output directory for plots")
    parser.add_argument("--title_prefix", type=str, default="Test",
                       help="Prefix for plot titles")

    args = parser.parse_args()

    # Load samples
    with open(args.samples_file, 'r') as f:
        data = json.load(f)

    if 'validation_samples' in data:
        samples = data['validation_samples']
    else:
        samples = data

    # Generate comprehensive report
    plot_paths = generate_comprehensive_report(
        samples=samples,
        output_dir=args.output_dir,
        title_prefix=args.title_prefix
    )

    print(f"Generated plots: {plot_paths}")


if __name__ == "__main__":
    main()