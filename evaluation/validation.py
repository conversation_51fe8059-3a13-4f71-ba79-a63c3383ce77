"""
Model Validation and Evaluation Workflow

This module implements comprehensive model validation including:
1. Loading validation samples and feeding into model
2. Computing metrics across different water frequency intervals
3. Exporting results as CSV files
4. Academic-standard evaluation and reporting

Author: Claude Code Assistant
Date: 2025-08-02
"""

import os
import sys
import json
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging
from tqdm import tqdm
import xarray as xr
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data.dataset import WaterBodyDataset
from model.model_v20 import create_swin_water_net
from evaluation.utils import generate_comprehensive_report
from evaluation.metrics import MetricsCalculator
from configs import get_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 移除了独立的计算函数，现在使用 MetricsCalculator 类


class ModelValidator:
    """Comprehensive model validation and evaluation"""

    def __init__(self,
                 model_path: str,
                 validation_samples_path: str,
                 missing_db_path: Optional[str] = None,
                 config_path: Optional[str] = None,
                 device: str = 'cuda',
                 platform: str = 'a100',
                 batch_size: int = 8,
                 optimize_gpu_memory: bool = True,
                 average_mode: str = 'pixel',
                 log_mode: str = 'minimal'):
        """
        Initialize model validator

        Args:
            model_path: Path to trained model checkpoint
            validation_samples_path: Path to validation samples JSON
            missing_db_path: Path to missing pattern database
            config_path: Path to model configuration
            device: Device to run inference on
            platform: Platform to run inference on
            batch_size: Batch size for inference
            optimize_gpu_memory: Whether to use optimized GPU memory management
                               (accumulate results on GPU before CPU transfer)
            average_mode: 'pixel' (micro) or 'sample' (macro)
            log_mode: 'minimal' to suppress non-essential logs; 'detailed' to show all info
        """
        self.model_path = Path(model_path)
        self.validation_samples_path = Path(validation_samples_path)
        self.missing_db_path = Path(missing_db_path) if missing_db_path else None

        # GPU device setup with validation
        self._setup_device(device)
        self.platform = platform
        self.batch_size = batch_size
        self.optimize_gpu_memory = optimize_gpu_memory
        # Averaging mode for metrics aggregation: 'pixel' (micro) or 'sample' (macro)
        if average_mode not in ('pixel', 'sample'):
            if log_mode == 'detailed':
                logger.warning(f"Invalid average_mode '{average_mode}', defaulting to 'pixel'")
            average_mode = 'pixel'
        self.average_mode = average_mode
        self.log_mode = log_mode

        # Load configuration
        if config_path:
            self.config = get_config(config_path)
        else:
            # Use default config
            self.config = get_config()

        # Initialize model
        self.model = None
        self.validation_dataset = None
        self.validation_loader = None

        logger.info(f"ModelValidator initialized")
        logger.info(f"Model path: {self.model_path}")
        logger.info(f"Validation samples: {self.validation_samples_path}")
        logger.info(f"Device: {self.device}")
        logger.info(f"Platform: {self.platform}")

    def _setup_device(self, device: str):
        """Setup and validate GPU device"""
        if device == 'cuda' or device.startswith('cuda:'):
            if not torch.cuda.is_available():
                logger.warning("CUDA requested but not available, falling back to CPU")
                self.device = torch.device('cpu')
            else:
                # If specific GPU index provided, use it; otherwise use default
                if device.startswith('cuda:'):
                    gpu_id = int(device.split(':')[1])
                    if gpu_id >= torch.cuda.device_count():
                        logger.warning(f"GPU {gpu_id} not available, using GPU 0")
                        gpu_id = 0
                    self.device = torch.device(f'cuda:{gpu_id}')
                else:
                    self.device = torch.device('cuda:0')

                # Set current device and log GPU info
                torch.cuda.set_device(self.device)
                gpu_name = torch.cuda.get_device_name(self.device)
                gpu_memory = torch.cuda.get_device_properties(self.device).total_memory / 1024**3
                logger.info(f"Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                logger.info(f"GPU device: {self.device}")

                # Clear GPU cache
                torch.cuda.empty_cache()
        else:
            self.device = torch.device(device)
            logger.info(f"Using device: {self.device}")

    def _log_gpu_memory(self, stage: str = ""):
        """Log current GPU memory usage"""
        if self.device.type == 'cuda':
            memory_allocated = torch.cuda.memory_allocated(self.device) / 1024**3
            memory_reserved = torch.cuda.memory_reserved(self.device) / 1024**3
            memory_free = (torch.cuda.get_device_properties(self.device).total_memory -
                          torch.cuda.memory_reserved(self.device)) / 1024**3
            logger.info(f"GPU memory {stage}: {memory_allocated:.2f}GB allocated, "
                       f"{memory_reserved:.2f}GB reserved, {memory_free:.2f}GB free")

    def load_model(self):
        """Load trained model from checkpoint with GPU optimization"""
        logger.info("Loading model...")

        # Create model architecture
        self.model = create_swin_water_net(self.config)

        # Move model to device with memory optimization
        if self.device.type == 'cuda':
            # Enable optimizations for inference
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False

        self.model = self.model.to(self.device)

        # Load checkpoint with device mapping
        logger.info(f"Loading checkpoint from {self.model_path}")
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)

        # Load model weights
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint

        # Handle DDP wrapper
        if any(key.startswith('module.') for key in state_dict.keys()):
            # Remove 'module.' prefix from DDP
            new_state_dict = {}
            for key, value in state_dict.items():
                new_key = key.replace('module.', '') if key.startswith('module.') else key
                new_state_dict[new_key] = value
            state_dict = new_state_dict

        # Load weights
        missing_keys, unexpected_keys = self.model.load_state_dict(state_dict, strict=False)

        if missing_keys:
            logger.warning(f"Missing keys in model: {missing_keys}")
        if unexpected_keys:
            logger.warning(f"Unexpected keys in model: {unexpected_keys}")

        # Set model to evaluation mode and enable inference optimizations
        self.model.eval()

        # Enable inference optimizations for GPU
        if self.device.type == 'cuda':
            # Compile model for faster inference (PyTorch 2.0+)
            try:
                if hasattr(torch, 'compile'):
                    self.model = torch.compile(self.model, mode='reduce-overhead')
                    logger.info("Model compiled for optimized inference")
            except Exception as e:
                logger.warning(f"Model compilation failed: {e}")

        logger.info("Model loaded successfully")

        # Get model info from checkpoint if available
        if isinstance(checkpoint, dict):
            if 'epoch' in checkpoint:
                logger.info(f"Model from epoch: {checkpoint['epoch']}")
            if 'best_metrics' in checkpoint:
                logger.info(f"Model best metrics: {checkpoint['best_metrics']}")

    def create_validation_dataset(self):
        """Create validation dataset from samples"""
        logger.info("Creating validation dataset...")

        # Create a temporary index file for the validation dataset
        with open(self.validation_samples_path, 'r') as f:
            validation_data = json.load(f)

        # Handle both list format and dict format for validation samples
        if isinstance(validation_data, list):
            validation_samples = validation_data
        elif isinstance(validation_data, dict):
            validation_samples = validation_data.get('validation_samples', validation_data)
        else:
            raise ValueError(f"Unexpected validation data format: {type(validation_data)}")

        # Optional: verify missing simulation consistency between get_random_pattern and add_missing_ratio
        # We reproduce the seeding logic using file_path (after device-specific path normalization)
        # to ensure the selected pattern ratio matches any precomputed 'simulated_missing_ratio'.
        # try:
            # if self.missing_db_path and self.missing_db_path.exists():
                # import numpy as np  # already imported at top, but safe in local scope
                # import hashlib, random
                # missing_data = np.load(self.missing_db_path, allow_pickle=True)
                # patterns = missing_data['patterns']

                # def normalize_path_for_seed(p: str) -> str:
                #     # Mirror dataset device-specific replacement used before seeding
                #     if self.platform == 'a100':
                #         return p.replace('/fossfs/xiaozhen/Clip/JRC4/', '/mnt/storage/xiaozhen/Water/Clip/JRC4/')
                #     elif self.platform == 'L40':
                #         return p.replace('/fossfs/xiaozhen/Clip/JRC4/', '/lustre1/g/geog_geors/xiaozhen/Water/JRC4/')
                #     return p

                # mismatch_count = 0
                # checked_count = 0
                # for sample in validation_samples:
                #     file_path = sample.get('file_path') or sample.get('source_file') or ''
                #     seed_key = normalize_path_for_seed(file_path)
                #     state = random.getstate()
                #     hash_value = int(hashlib.md5(seed_key.encode()).hexdigest(), 16)
                #     random.seed(42 + hash_value)
                #     chosen = random.choice(patterns)
                #     random.setstate(state)
                #     expected_ratio = float(chosen['missing_ratio'])

                #     if 'simulated_missing_ratio' in sample:
                #         checked_count += 1
                #         if abs(float(sample['simulated_missing_ratio']) - expected_ratio) > 1e-8:
                #             mismatch_count += 1
                # if checked_count > 0 and mismatch_count > 0:
                #     logger.warning(f"Detected {mismatch_count}/{checked_count} samples where simulated_missing_ratio mismatches the expected ratio based on get_random_pattern seeding. Evaluation will follow dataset seeding (get_random_pattern).")
        # except Exception as e:
            # logger.warning(f"Skipped missing-ratio consistency check: {e}")

        # Group samples by file for dataset format
        samples_by_file = {}
        for sample in validation_samples:
            file_path = sample['file_path']
            if file_path not in samples_by_file:
                samples_by_file[file_path] = []
            samples_by_file[file_path].append(sample)

        # Create temporary index file
        temp_index = {
            'no_missing_samples': samples_by_file,
            'missing_samples_by_ratio': {},
            'metadata': {
                'total_samples': len(validation_samples),
                'source': 'validation_samples'
            }
        }

        temp_index_path = self.validation_samples_path.parent / 'temp_validation_index.json'
        with open(temp_index_path, 'w') as f:
            json.dump(temp_index, f, indent=2, default=str)

        # Create dataset
        self.validation_dataset = WaterBodyDataset(
            index_file=str(temp_index_path),
            missing_db_file=str(self.missing_db_path) if self.missing_db_path else None,
            expected_missing_ratio=0.99,
            config=self.config,
            mode='evaluation',  # Use validation mode
            device=self.platform,
            use_missing_augmentation=True
        )

        # Create data loader with GPU optimizations
        num_workers = 4 if self.device.type == 'cuda' else 2
        pin_memory = self.device.type == 'cuda'

        self.validation_loader = DataLoader(
            self.validation_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory,
            drop_last=False,
            persistent_workers=True if num_workers > 0 else False,
            prefetch_factor=2 if num_workers > 0 else None
        )

        logger.info(f"Created validation dataset with {len(self.validation_dataset)} samples")

        # Clean up temp file
        temp_index_path.unlink()

    def extract_sample_metadata(self) -> List[Dict]:
        """Extract metadata for each validation sample"""
        metadata = []

        for i in range(len(self.validation_dataset)):
            sample_info = self.validation_dataset.samples[i]

            meta = {
                'sample_idx': i,
                'file_path': sample_info['file_path'],
                'idx_x': sample_info['idx_x'],
                'idx_y': sample_info['idx_y'],
                'time_idx': sample_info['time_idx'],
                'mean_water_frequency': sample_info['mean_water_frequency'],
                'tile_lon': sample_info['tile_lon'],
                'tile_lat': sample_info['tile_lat'],
                'year': sample_info['year'],
                'month': sample_info['month']
            }

            # Add optional fields if present and valid to avoid analysis warnings
            # - water_proportion, missing_proportion come from index/build pipeline
            # - simulated_missing_ratio may be added by evaluation/sampler
            for key in ('water_proportion', 'missing_proportion', 'simulated_missing_ratio'):
                if key in sample_info and sample_info[key] is not None:
                    meta[key] = sample_info[key]

            metadata.append(meta)

        return metadata

    @torch.no_grad()
    def run_inference(self) -> Tuple[List[np.ndarray], List[np.ndarray], List[np.ndarray], List[np.ndarray]]:
        """
        Run model inference on validation samples with GPU optimization

        Returns:
            Tuple of (predictions, ground_truths, missing_masks, water_frequencies)
        """
        if self.optimize_gpu_memory:
            return self._run_inference_optimized()
        else:
            return self._run_inference_traditional()

    @torch.no_grad()
    def _run_inference_optimized(self) -> Tuple[List[np.ndarray], List[np.ndarray], List[np.ndarray], List[np.ndarray]]:
        """
        Optimized inference that minimizes CPU-GPU communication by accumulating results on GPU

        Returns:
            Tuple of (predictions, ground_truths, missing_masks, water_frequencies)
        """
        logger.info("Running model inference with optimized GPU-CPU communication...")

        self.model.eval()

        # Accumulate all results on GPU first to minimize CPU-GPU transfers
        gpu_predictions = []
        gpu_ground_truths = []
        gpu_missing_masks = []
        gpu_water_frequencies = []

        # Enable inference mode for better performance
        with torch.inference_mode():
            for batch_idx, batch in enumerate(tqdm(self.validation_loader, desc="Inference")):
                # Move batch to device with optimized transfers
                processed_batch = {}
                for k, v in batch.items():
                    if torch.is_tensor(v):
                        if k in ['center_frame_idx', 'year', 'month']:
                            processed_batch[k] = v.to(self.device, dtype=torch.long, non_blocking=True)
                        else:
                            processed_batch[k] = v.to(self.device, non_blocking=True)
                    else:
                        processed_batch[k] = v

                # Forward pass with mixed precision if available
                if self.device.type == 'cuda':
                    with torch.autocast(device_type='cuda', dtype=torch.float16):
                        outputs = self.model(processed_batch)
                else:
                    outputs = self.model(processed_batch)

                # Model returns dictionary with inpaint key containing logits
                pred = outputs['inpaint']['logits']

                # Apply sigmoid if needed and convert to probabilities
                if pred.dim() > 3:  # (B, C, H, W)
                    if pred.size(1) > 1:  # Multi-class
                        pred = F.softmax(pred, dim=1)[:, 1]  # Take water class
                    else:  # Single class
                        pred = torch.sigmoid(pred).squeeze(1)
                else:  # (B, H, W)
                    pred = torch.sigmoid(pred)

                # Get ground truth, missing mask, and water frequency
                gt = processed_batch['ground_truth']  # (B, H, W)
                mask = processed_batch['missing_mask']  # (B, H, W)
                occurrence = processed_batch['occurrence']  # (B, H, W) - water frequency image

                # Store tensors on GPU (avoid immediate CPU transfer)
                # Split batch into individual samples and keep on GPU
                for i in range(pred.shape[0]):
                    gpu_predictions.append(pred[i].clone())  # Keep on GPU
                    gpu_ground_truths.append(gt[i].clone())
                    gpu_missing_masks.append(mask[i].clone())
                    gpu_water_frequencies.append(occurrence[i].clone())

                # Clear GPU cache periodically to prevent OOM
                if self.device.type == 'cuda' and batch_idx % 100 == 0:
                    torch.cuda.empty_cache()
                    # Log memory usage
                    if batch_idx % 500 == 0:
                        self._log_gpu_memory(f"after batch {batch_idx}")

        logger.info(f"Inference completed on {len(gpu_predictions)} samples")
        logger.info("Converting GPU tensors to CPU numpy arrays (one-time transfer)...")

        # One-time transfer from GPU to CPU and convert to numpy
        predictions = []
        ground_truths = []
        missing_masks = []
        water_frequencies = []

        # Process in chunks to manage memory efficiently
        chunk_size = 100  # Process 100 samples at a time
        total_samples = len(gpu_predictions)

        for start_idx in tqdm(range(0, total_samples, chunk_size), desc="GPU->CPU transfer"):
            end_idx = min(start_idx + chunk_size, total_samples)

            # Stack chunk of tensors and transfer to CPU in one operation
            chunk_preds = torch.stack(gpu_predictions[start_idx:end_idx])
            chunk_gts = torch.stack(gpu_ground_truths[start_idx:end_idx])
            chunk_masks = torch.stack(gpu_missing_masks[start_idx:end_idx])
            chunk_freqs = torch.stack(gpu_water_frequencies[start_idx:end_idx])

            # Single CPU transfer for the entire chunk
            chunk_preds_np = chunk_preds.cpu().numpy()
            chunk_gts_np = chunk_gts.cpu().numpy()
            chunk_masks_np = chunk_masks.cpu().numpy()
            chunk_freqs_np = chunk_freqs.cpu().numpy()

            # Add individual samples to lists
            for i in range(chunk_preds_np.shape[0]):
                predictions.append(chunk_preds_np[i])
                ground_truths.append(chunk_gts_np[i])
                missing_masks.append(chunk_masks_np[i])
                water_frequencies.append(chunk_freqs_np[i])

            # Clear processed GPU tensors to free memory
            del chunk_preds, chunk_gts, chunk_masks, chunk_freqs
            del chunk_preds_np, chunk_gts_np, chunk_masks_np, chunk_freqs_np

            # Clear GPU cache
            if self.device.type == 'cuda':
                torch.cuda.empty_cache()

        # Clear all GPU tensors
        del gpu_predictions, gpu_ground_truths, gpu_missing_masks, gpu_water_frequencies
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            self._log_gpu_memory("after inference cleanup")

        logger.info(f"Successfully transferred {len(predictions)} samples to CPU")
        return predictions, ground_truths, missing_masks, water_frequencies

    @torch.no_grad()
    def _run_inference_traditional(self) -> Tuple[List[np.ndarray], List[np.ndarray], List[np.ndarray], List[np.ndarray]]:
        """
        Traditional inference with immediate CPU-GPU transfers (for comparison/fallback)

        Returns:
            Tuple of (predictions, ground_truths, missing_masks, water_frequencies)
        """
        logger.info("Running model inference with traditional CPU-GPU communication...")

        self.model.eval()
        predictions = []
        ground_truths = []
        missing_masks = []
        water_frequencies = []

        # Enable inference mode for better performance
        with torch.inference_mode():
            for batch_idx, batch in enumerate(tqdm(self.validation_loader, desc="Inference")):
                # Move batch to device with optimized transfers
                processed_batch = {}
                for k, v in batch.items():
                    if torch.is_tensor(v):
                        if k in ['center_frame_idx', 'year', 'month']:
                            processed_batch[k] = v.to(self.device, dtype=torch.long, non_blocking=True)
                        else:
                            processed_batch[k] = v.to(self.device, non_blocking=True)
                    else:
                        processed_batch[k] = v

                # Forward pass with mixed precision if available
                if self.device.type == 'cuda':
                    with torch.autocast(device_type='cuda', dtype=torch.float16):
                        outputs = self.model(processed_batch)
                else:
                    outputs = self.model(processed_batch)

                # Model returns dictionary with inpaint key containing logits
                pred = outputs['inpaint']['logits']

                # Apply sigmoid if needed and convert to probabilities
                if pred.dim() > 3:  # (B, C, H, W)
                    if pred.size(1) > 1:  # Multi-class
                        pred = F.softmax(pred, dim=1)[:, 1]  # Take water class
                    else:  # Single class
                        pred = torch.sigmoid(pred).squeeze(1)
                else:  # (B, H, W)
                    pred = torch.sigmoid(pred)

                # Get ground truth, missing mask, and water frequency
                gt = processed_batch['ground_truth']  # (B, H, W)
                mask = processed_batch['missing_mask']  # (B, H, W)
                occurrence = processed_batch['occurrence']  # (B, H, W) - water frequency image

                # Convert to numpy (immediate CPU transfer)
                pred_np = pred.cpu().numpy()
                gt_np = gt.cpu().numpy()
                mask_np = mask.cpu().numpy()
                occurrence_np = occurrence.cpu().numpy()

                # Store results
                for i in range(pred_np.shape[0]):
                    predictions.append(pred_np[i])
                    ground_truths.append(gt_np[i])
                    missing_masks.append(mask_np[i])
                    water_frequencies.append(occurrence_np[i])

                # Clear GPU cache periodically to prevent OOM
                if self.device.type == 'cuda' and batch_idx % 200 == 0:
                    torch.cuda.empty_cache()

        logger.info(f"Inference completed on {len(predictions)} samples")
        return predictions, ground_truths, missing_masks, water_frequencies

    def compute_metrics_by_frequency_intervals(self,
                                             predictions: List[np.ndarray],
                                             ground_truths: List[np.ndarray],
                                             missing_masks: List[np.ndarray],
                                             water_frequencies: List[np.ndarray]) -> Dict[str, Dict]:
        """
        Compute metrics across different water frequency intervals using MetricsCalculator

        Args:
            predictions: List of prediction arrays
            ground_truths: List of ground truth arrays
            missing_masks: List of missing mask arrays
            water_frequencies: List of water frequency images (not average values)

        Returns:
            Dictionary with metrics for each frequency interval
        """
        logger.info("Computing metrics by frequency intervals using MetricsCalculator...")

        # Define frequency intervals - 对应 MetricsCalculator 的三个区间
        intervals = {
            'full_range_0_1': (0.0, 1.0),
            'dynamic_0_2_0_8': (0.2, 0.8),
            'highly_dynamic_0_4_0_6': (0.4, 0.6)
        }

        results = {}

        # Collect per-sample tensors and masks first
        all_preds = []
        all_gts = []
        all_valid_masks = []
        all_water_freqs = []

        for idx in range(len(predictions)):
            pred = predictions[idx]
            gt = ground_truths[idx]
            mask = missing_masks[idx]
            water_freq = water_frequencies[idx]

            valid_pixels = mask  # only evaluate on missing pixels
            if np.any(valid_pixels):
                pred_tensor = torch.from_numpy((pred >= 0.5).astype(bool))
                gt_tensor = torch.from_numpy(gt.astype(bool))
                valid_tensor = torch.from_numpy(valid_pixels)
                freq_tensor = torch.from_numpy(water_freq.astype(np.float32))

                all_preds.append(pred_tensor)
                all_gts.append(gt_tensor)
                all_valid_masks.append(valid_tensor)
                all_water_freqs.append(freq_tensor)

        if not all_preds:
            logger.warning("No valid pixels found in any sample")
            for interval_name in intervals.keys():
                results[interval_name] = {
                    'sample_count': 0,
                    'total_pixels': 0,
                    'accuracy': np.nan,
                    'precision': np.nan,
                    'recall': np.nan,
                    'f1_score': np.nan,
                    'iou': np.nan
                }
            return results

        if getattr(self, 'average_mode', 'pixel') == 'sample':
            logger.info("Computing sample-level (macro) averaged metrics...")
            # Compute metrics per sample then average across samples for each interval
            per_interval_values = {
                'full_range_0_1': {'accuracy': [], 'precision': [], 'recall': [], 'f1_score': [], 'iou': []},
                'dynamic_0_2_0_8': {'accuracy': [], 'precision': [], 'recall': [], 'f1_score': [], 'iou': []},
                'highly_dynamic_0_4_0_6': {'accuracy': [], 'precision': [], 'recall': [], 'f1_score': [], 'iou': []}
            }
            interval_sample_counts = {k: 0 for k in intervals.keys()}
            interval_total_pixels = {k: 0 for k in intervals.keys()}

            for pred_t, gt_t, valid_t, freq_t in zip(all_preds, all_gts, all_valid_masks, all_water_freqs):
                # Add batch/channel dims as expected by MetricsCalculator
                metrics = MetricsCalculator.calculate_metrics(
                    pred=pred_t.unsqueeze(0),
                    target=gt_t.unsqueeze(0),
                    valid_mask=valid_t.unsqueeze(0),
                    water_frequency=freq_t.unsqueeze(0).unsqueeze(0)
                )
                # For each interval, check if this sample has any valid pixels in that interval
                for interval_name, (low, high) in intervals.items():
                    if interval_name == 'full_range_0_1':
                        suffix = '_0_1'
                    elif interval_name == 'dynamic_0_2_0_8':
                        suffix = '_0_2_0_8'
                    else:
                        suffix = '_0_4_0_6'

                    interval_mask = (freq_t >= low) & (freq_t <= high)
                    valid_in_interval = (valid_t & interval_mask).sum().item()
                    if valid_in_interval > 0:
                        interval_sample_counts[interval_name] += 1
                        interval_total_pixels[interval_name] += int(valid_in_interval)
                        # Append metric if it's not NaN
                        for m in ['accuracy', 'precision', 'recall', 'f1_score', 'iou']:
                            val = float(metrics[f'{m}{suffix}'])
                            if not np.isnan(val):
                                per_interval_values[interval_name][m].append(val)

            # Aggregate by unweighted mean across samples
            for interval_name in intervals.keys():
                agg = {}
                for m in ['accuracy', 'precision', 'recall', 'f1_score', 'iou']:
                    vals = per_interval_values[interval_name][m]
                    agg[m] = float(np.mean(vals)) if len(vals) > 0 else np.nan
                results[interval_name] = {
                    'sample_count': int(interval_sample_counts[interval_name]),
                    'total_pixels': int(interval_total_pixels[interval_name]),
                    'accuracy': agg['accuracy'],
                    'precision': agg['precision'],
                    'recall': agg['recall'],
                    'f1_score': agg['f1_score'],
                    'iou': agg['iou']
                }
                logger.info(
                    f"Interval {interval_name} (macro): Acc={results[interval_name]['accuracy']:.4f}, "
                    f"F1={results[interval_name]['f1_score']:.4f}, IoU={results[interval_name]['iou']:.4f}"
                )
            return results
        else:
            # Pixel-level (micro) averaging: combine all valid pixels then compute metrics once
            logger.info("Computing pixel-level (micro) averaged metrics...")
            combined_preds = torch.cat(all_preds, dim=0).unsqueeze(0)
            combined_gts = torch.cat(all_gts, dim=0).unsqueeze(0)
            combined_valid = torch.cat(all_valid_masks, dim=0).unsqueeze(0)
            combined_freqs = torch.cat(all_water_freqs, dim=0).unsqueeze(0).unsqueeze(0)

            logger.info("Calculating metrics using MetricsCalculator (global tensors)...")
            metrics = MetricsCalculator.calculate_metrics(
                pred=combined_preds,
                target=combined_gts,
                valid_mask=combined_valid,
                water_frequency=combined_freqs
            )

            # Prepare interval-wise counts (number of samples with valid pixels in interval and total valid pixels)
            for interval_name, (low, high) in intervals.items():
                if interval_name == 'full_range_0_1':
                    suffix = '_0_1'
                elif interval_name == 'dynamic_0_2_0_8':
                    suffix = '_0_2_0_8'
                else:
                    suffix = '_0_4_0_6'

                interval_sample_count = 0
                interval_total_valid_pixels = 0
                for vmask, wfreq in zip(all_valid_masks, all_water_freqs):
                    interval_mask = (wfreq >= low) & (wfreq <= high)
                    valid_in_interval = (vmask & interval_mask).sum().item()
                    if valid_in_interval > 0:
                        interval_sample_count += 1
                        interval_total_valid_pixels += valid_in_interval

                results[interval_name] = {
                    'sample_count': int(interval_sample_count),
                    'total_pixels': int(interval_total_valid_pixels),
                    'accuracy': float(metrics[f'accuracy{suffix}']),
                    'precision': float(metrics[f'precision{suffix}']),
                    'recall': float(metrics[f'recall{suffix}']),
                    'f1_score': float(metrics[f'f1_score{suffix}']),
                    'iou': float(metrics[f'iou{suffix}'])
                }

                logger.info(
                    f"Interval {interval_name}: Acc={results[interval_name]['accuracy']:.4f}, "
                    f"F1={results[interval_name]['f1_score']:.4f}, IoU={results[interval_name]['iou']:.4f}"
                )

            return results

    def compute_sample_metrics(self,
                             predictions: List[np.ndarray],
                             ground_truths: List[np.ndarray],
                             missing_masks: List[np.ndarray],
                             water_frequencies: List[np.ndarray],
                             sample_metadata: List[Dict]) -> List[Dict]:
        """
        Compute metrics for each individual sample using MetricsCalculator

        Args:
            predictions: List of prediction arrays
            ground_truths: List of ground truth arrays
            missing_masks: List of missing mask arrays
            water_frequencies: List of water frequency images
            sample_metadata: List of sample metadata

        Returns:
            List of dictionaries containing metrics for each sample
        """
        import torch
        logger.info("Computing metrics for individual samples using MetricsCalculator...")

        sample_metrics = []

        for idx in range(len(predictions)):
            pred = predictions[idx]
            gt = ground_truths[idx]
            mask = missing_masks[idx]
            water_freq = water_frequencies[idx]
            meta = sample_metadata[idx]

            # Apply missing mask - only evaluate on missing pixels
            valid_pixels = mask

            sample_result = {
                'sample_idx': idx,
                'file_path': meta['file_path'],
                'idx_x': meta['idx_x'],
                'idx_y': meta['idx_y'],
                'time_idx': meta['time_idx'],
                'mean_water_frequency': meta['mean_water_frequency'],
                'tile_lon': meta['tile_lon'],
                'tile_lat': meta['tile_lat'],
                'year': meta['year'],
                'month': meta['month'],
                'valid_pixels_count': int(np.sum(valid_pixels))
            }

            if np.any(valid_pixels):
                # 转换为 torch tensor 格式
                pred_tensor = torch.from_numpy((pred >= 0.5).astype(bool)).unsqueeze(0)
                gt_tensor = torch.from_numpy(gt.astype(bool)).unsqueeze(0)
                valid_tensor = torch.from_numpy(valid_pixels).unsqueeze(0)
                freq_tensor = torch.from_numpy(water_freq.astype(np.float32)).unsqueeze(0).unsqueeze(0)

                # 使用 MetricsCalculator 计算指标
                metrics = MetricsCalculator.calculate_metrics(
                    pred=pred_tensor,
                    target=gt_tensor,
                    valid_mask=valid_tensor,
                    water_frequency=freq_tensor
                )

                # 添加所有频率区间的指标
                sample_result.update({
                    # Full range (0-1)
                    'accuracy_0_1': float(metrics['accuracy_0_1']) if not np.isnan(metrics['accuracy_0_1']) else None,
                    'precision_0_1': float(metrics['precision_0_1']) if not np.isnan(metrics['precision_0_1']) else None,
                    'recall_0_1': float(metrics['recall_0_1']) if not np.isnan(metrics['recall_0_1']) else None,
                    'f1_score_0_1': float(metrics['f1_score_0_1']) if not np.isnan(metrics['f1_score_0_1']) else None,
                    'iou_0_1': float(metrics['iou_0_1']) if not np.isnan(metrics['iou_0_1']) else None,

                    # Dynamic range (0.2-0.8)
                    'accuracy_0_2_0_8': float(metrics['accuracy_0_2_0_8']) if not np.isnan(metrics['accuracy_0_2_0_8']) else None,
                    'precision_0_2_0_8': float(metrics['precision_0_2_0_8']) if not np.isnan(metrics['precision_0_2_0_8']) else None,
                    'recall_0_2_0_8': float(metrics['recall_0_2_0_8']) if not np.isnan(metrics['recall_0_2_0_8']) else None,
                    'f1_score_0_2_0_8': float(metrics['f1_score_0_2_0_8']) if not np.isnan(metrics['f1_score_0_2_0_8']) else None,
                    'iou_0_2_0_8': float(metrics['iou_0_2_0_8']) if not np.isnan(metrics['iou_0_2_0_8']) else None,

                    # Highly dynamic range (0.4-0.6)
                    'accuracy_0_4_0_6': float(metrics['accuracy_0_4_0_6']) if not np.isnan(metrics['accuracy_0_4_0_6']) else None,
                    'precision_0_4_0_6': float(metrics['precision_0_4_0_6']) if not np.isnan(metrics['precision_0_4_0_6']) else None,
                    'recall_0_4_0_6': float(metrics['recall_0_4_0_6']) if not np.isnan(metrics['recall_0_4_0_6']) else None,
                    'f1_score_0_4_0_6': float(metrics['f1_score_0_4_0_6']) if not np.isnan(metrics['f1_score_0_4_0_6']) else None,
                    'iou_0_4_0_6': float(metrics['iou_0_4_0_6']) if not np.isnan(metrics['iou_0_4_0_6']) else None,
                })
            else:
                # No valid pixels - set all metrics to None
                for suffix in ['_0_1', '_0_2_0_8', '_0_4_0_6']:
                    for metric in ['accuracy', 'precision', 'recall', 'f1_score', 'iou']:
                        sample_result[f'{metric}{suffix}'] = None

            sample_metrics.append(sample_result)

        logger.info(f"Completed metrics calculation for {len(sample_metrics)} samples")
        return sample_metrics

    def save_sample_metrics_to_csv(self,
                                 sample_metrics: List[Dict],
                                 output_path: Union[str, Path]):
        """
        Save individual sample metrics to CSV file

        Args:
            sample_metrics: List of sample metrics dictionaries
            output_path: Path to save the CSV file
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        logger.info(f"Saving sample metrics to {output_path}")

        # Convert to DataFrame
        df = pd.DataFrame(sample_metrics)

        # Reorder columns for better readability
        base_columns = [
            'sample_idx', 'file_path', 'idx_x', 'idx_y', 'time_idx',
            'mean_water_frequency', 'tile_lon', 'tile_lat', 'year', 'month',
            'valid_pixels_count'
        ]

        metric_columns = []
        for suffix in ['_0_1', '_0_2_0_8', '_0_4_0_6']:
            for metric in ['accuracy', 'precision', 'recall', 'f1_score', 'iou']:
                metric_columns.append(f'{metric}{suffix}')

        # Reorder columns
        ordered_columns = base_columns + metric_columns
        df = df[ordered_columns]

        # Save to CSV
        df.to_csv(output_path, index=False, float_format='%.6f')

        logger.info(f"Sample metrics saved to {output_path}")
        logger.info(f"Total samples: {len(df)}")

        # Log summary statistics
        for suffix in ['_0_1', '_0_2_0_8', '_0_4_0_6']:
            acc_col = f'accuracy{suffix}'
            if acc_col in df.columns:
                valid_acc = df[acc_col].dropna()
                if len(valid_acc) > 0:
                    logger.info(f"Accuracy{suffix}: mean={valid_acc.mean():.4f}, "
                               f"std={valid_acc.std():.4f}, "
                               f"min={valid_acc.min():.4f}, "
                               f"max={valid_acc.max():.4f}, "
                               f"valid_samples={len(valid_acc)}")

        return output_path

    def save_metric_geographical_distribution(self,
                                            sample_metrics: List[Dict],
                                            sample_metadata: List[Dict],
                                            output_dir: Union[str, Path],
                                            metric_name: str = 'accuracy_0_1',
                                            title_prefix: str = "Validation") -> str:
        """
        Save geographical distribution plot for a specific metric using utils.visualize_geographical_distribution

        Args:
            sample_metrics: List of sample metrics dictionaries from compute_sample_metrics
            sample_metadata: List of sample metadata dictionaries
            output_dir: Directory to save the plot
            metric_name: Name of the metric to visualize (e.g., 'accuracy_0_1', 'f1_score_0_2_0_8')
            title_prefix: Prefix for plot title

        Returns:
            Path to saved geographical distribution plot
        """
        from .utils import visualize_geographical_distribution

        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Creating geographical distribution plot for metric: {metric_name}")

        # Validate metric name
        valid_metrics = []
        for suffix in ['_0_1', '_0_2_0_8', '_0_4_0_6']:
            for metric in ['accuracy', 'precision', 'recall', 'f1_score', 'iou']:
                valid_metrics.append(f'{metric}{suffix}')

        if metric_name not in valid_metrics:
            raise ValueError(f"Invalid metric name: {metric_name}. Valid options: {valid_metrics}")

        # Extract metric values from sample_metrics
        metric_values = []
        valid_samples = []

        for i, sample_metric in enumerate(sample_metrics):
            metric_value = sample_metric.get(metric_name)
            if metric_value is not None and not np.isnan(metric_value):
                metric_values.append(metric_value)
                valid_samples.append(sample_metadata[i])
            else:
                # For samples with no valid metric, we can either skip them or use a default value
                # Here we skip them to avoid misleading visualization
                logger.debug(f"Skipping sample {i} due to invalid {metric_name} value: {metric_value}")

        if not metric_values:
            logger.warning(f"No valid {metric_name} values found for geographical visualization")
            return ""

        logger.info(f"Visualizing {len(metric_values)} samples with valid {metric_name} values")

        # Create human-readable metric name for display
        metric_display_name = metric_name.replace('_', ' ').title()
        if '_0_1' in metric_name:
            metric_display_name += ' (0.0-1.0)'
        elif '_0_2_0_8' in metric_name:
            metric_display_name += ' (0.2-0.8)'
        elif '_0_4_0_6' in metric_name:
            metric_display_name += ' (0.4-0.6)'

        # Use the utils function to create geographical distribution plot
        plot_path = visualize_geographical_distribution(
            samples=valid_samples,
            output_dir=output_dir,
            metric_name=metric_display_name,
            metric_values=metric_values,
            title_prefix=title_prefix
        )

        logger.info(f"Geographical distribution plot for {metric_name} saved to: {plot_path}")

        # Log metric statistics
        if metric_values:
            logger.info(f"{metric_name} statistics: "
                       f"mean={np.mean(metric_values):.4f}, "
                       f"std={np.std(metric_values):.4f}, "
                       f"min={np.min(metric_values):.4f}, "
                       f"max={np.max(metric_values):.4f}")

        return plot_path

    def save_multiple_metric_geographical_distributions(self,
                                                      sample_metrics: List[Dict],
                                                      sample_metadata: List[Dict],
                                                      output_dir: Union[str, Path],
                                                      metrics_to_plot: Optional[List[str]] = None,
                                                      title_prefix: str = "Validation") -> Dict[str, str]:
        """
        Save geographical distribution plots for multiple metrics

        Args:
            sample_metrics: List of sample metrics dictionaries from compute_sample_metrics
            sample_metadata: List of sample metadata dictionaries
            output_dir: Directory to save the plots
            metrics_to_plot: List of metric names to plot. If None, plots key metrics for each frequency range
            title_prefix: Prefix for plot titles

        Returns:
            Dictionary mapping metric names to plot file paths
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Default metrics to plot if not specified
        if metrics_to_plot is None:
            metrics_to_plot = [
                # Overall (0.0-1.0)
                'accuracy_0_1',
                'f1_score_0_1',
                'iou_0_1',
                # Dynamic range (0.2-0.8)
                'accuracy_0_2_0_8',
                'f1_score_0_2_0_8',
                'iou_0_2_0_8',
                # Highly dynamic range (0.4-0.6)
                'accuracy_0_4_0_6',
                'f1_score_0_4_0_6',
                'iou_0_4_0_6',
            ]

        logger.info(f"Creating geographical distribution plots for {len(metrics_to_plot)} metrics")

        plot_paths = {}

        for metric_name in metrics_to_plot:
            try:
                plot_path = self.save_metric_geographical_distribution(
                    sample_metrics=sample_metrics,
                    sample_metadata=sample_metadata,
                    output_dir=output_dir,
                    metric_name=metric_name,
                    title_prefix=title_prefix
                )
                if plot_path:
                    plot_paths[metric_name] = plot_path
                    logger.info(f"✓ Created geographical plot for {metric_name}")
                else:
                    logger.warning(f"✗ Failed to create geographical plot for {metric_name}")
            except Exception as e:
                logger.error(f"✗ Error creating geographical plot for {metric_name}: {e}")

        logger.info(f"Created {len(plot_paths)} geographical distribution plots")
        return plot_paths

    def save_sample_visualizations(self,
                                 predictions: List[np.ndarray],
                                 ground_truths: List[np.ndarray],
                                 missing_masks: List[np.ndarray],
                                 water_frequencies: List[np.ndarray],
                                 sample_metadata: List[Dict],
                                 output_dir: Path,
                                 num_samples: int = 8,
                                 freq_min: float = 0.2,
                                 freq_max: float = 0.8):
        """
        Save sample visualizations similar to training visualization

        Args:
            predictions: List of prediction arrays
            ground_truths: List of ground truth arrays
            missing_masks: List of missing mask arrays
            water_frequencies: List of water frequency images
            sample_metadata: List of sample metadata
            output_dir: Output directory for visualizations
            num_samples: Number of samples to visualize
            freq_min: Minimum water frequency for sample selection
            freq_max: Maximum water frequency for sample selection
        """
        import matplotlib.pyplot as plt
        import matplotlib.colors as mcolors
        import matplotlib.patches as mpatches
        from matplotlib.colors import LinearSegmentedColormap
        import random

        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Saving sample visualizations to {output_dir}")

        # Select samples based on water frequency
        valid_indices = []
        for i, water_freq in enumerate(water_frequencies):
            mean_freq = np.mean(water_freq[water_freq > 0]) if np.any(water_freq > 0) else 0.0
            if freq_min <= mean_freq <= freq_max:
                valid_indices.append(i)

        if not valid_indices:
            # If no samples in frequency range, use all samples
            valid_indices = list(range(len(predictions)))
            logger.warning(f"No samples found in frequency range [{freq_min}, {freq_max}], using all samples")

        # Randomly select samples for visualization
        num_vis_samples = min(num_samples, len(valid_indices))
        selected_indices = random.sample(valid_indices, num_vis_samples)

        logger.info(f"Visualizing {num_vis_samples} samples from {len(valid_indices)} valid samples")

        # Create visualization
        fig, axes = plt.subplots(num_vis_samples, 6, figsize=(24, 4*num_vis_samples))
        if num_vis_samples == 1:
            axes = axes.reshape(1, -1)

        fig.suptitle('Validation Sample Visualizations', fontsize=16)

        # Color maps
        colors_yellow_blue = [(1, 1, 0), (0, 0, 1)]
        colors_white_red = [(1, 1, 1), (1, 0, 0)]
        n_bins = 100
        yellow_blue_cmap = LinearSegmentedColormap.from_list("custom_yb", colors_yellow_blue, N=n_bins)
        white_red_cmap = LinearSegmentedColormap.from_list("custom_wr", colors_white_red, N=n_bins)

        # Water frequency layer colors
        freq_colors = {
            'background': '#FFFFFF',  # 白色背景（非水体）
            'low': '#A6CEE3',         # 浅蓝色 (0-0.2)
            'mid': '#1F78B4',         # 中蓝色 (0.2-0.4, 0.6-0.8)
            'high': '#08306B'         # 深蓝色 (0.4-0.6)
        }

        for idx, sample_idx in enumerate(selected_indices):
            pred = predictions[sample_idx]
            gt = ground_truths[sample_idx]
            mask = missing_masks[sample_idx]
            water_freq = water_frequencies[sample_idx]
            meta = sample_metadata[sample_idx]

            # Get geographic and temporal info
            geo_temp_info = ""
            if 'mean_water_frequency' in meta:
                mean_freq = meta['mean_water_frequency']
                geo_temp_info = f"Mean Freq: {mean_freq:.3f}"

            # 1. Input visualization (missing areas)
            input_rgb = np.ones((gt.shape[0], gt.shape[1], 3))
            water_mask = gt > 0.5
            missing_mask_vis = mask
            input_rgb[water_mask] = [0, 0, 1]  # Blue for water
            input_rgb[missing_mask_vis] = [0.7, 0.7, 0.7]  # Gray for missing
            axes[idx, 0].imshow(input_rgb)
            axes[idx, 0].set_title(f'Sample {sample_idx} - Input\n{geo_temp_info}', fontsize=10)
            axes[idx, 0].axis('off')

            # 2. Ground truth
            im1 = axes[idx, 1].imshow(gt, cmap=yellow_blue_cmap, vmin=0, vmax=1)
            axes[idx, 1].set_title('Ground Truth')
            axes[idx, 1].axis('off')
            plt.colorbar(im1, ax=axes[idx, 1], fraction=0.046)

            # 3. Prediction
            im2 = axes[idx, 2].imshow(pred, cmap=yellow_blue_cmap, vmin=0, vmax=1)
            axes[idx, 2].set_title('Prediction')
            axes[idx, 2].axis('off')
            plt.colorbar(im2, ax=axes[idx, 2], fraction=0.046)

            # 4. Error map
            error = np.abs(pred - gt)
            im3 = axes[idx, 3].imshow(error, cmap=white_red_cmap, vmin=0, vmax=1)
            axes[idx, 3].set_title('Error')
            axes[idx, 3].axis('off')
            plt.colorbar(im3, ax=axes[idx, 3], fraction=0.046)

            # 5. Missing mask
            axes[idx, 4].imshow(mask, cmap='gray', vmin=0, vmax=1)
            axes[idx, 4].set_title('Missing Mask')
            axes[idx, 4].axis('off')

            # 6. Water frequency layers
            water_freq_layered = np.zeros((water_freq.shape[0], water_freq.shape[1], 3))

            # Create frequency masks
            mask_0_1 = water_freq > 0  # 0-1范围
            mask_0_2_0_8 = (water_freq >= 0.2) & (water_freq <= 0.8)  # 0.2-0.8范围
            mask_0_4_0_6 = (water_freq >= 0.4) & (water_freq <= 0.6)  # 0.4-0.6范围

            # Convert hex colors to RGB
            rgb_bg = mcolors.hex2color(freq_colors['background'])
            rgb_low = mcolors.hex2color(freq_colors['low'])
            rgb_mid = mcolors.hex2color(freq_colors['mid'])
            rgb_high = mcolors.hex2color(freq_colors['high'])

            # Set background
            water_freq_layered[:] = rgb_bg

            # Fill colors by frequency range
            water_freq_layered[mask_0_1] = rgb_low
            water_freq_layered[mask_0_2_0_8] = rgb_mid
            water_freq_layered[mask_0_4_0_6] = rgb_high

            axes[idx, 5].imshow(water_freq_layered)
            axes[idx, 5].set_title('Frequency Layers')
            axes[idx, 5].axis('off')

            # Add legend for frequency layers
            legend_elements = [
                mpatches.Patch(color=freq_colors['low'], label='0.0-0.2, 0.8-1.0'),
                mpatches.Patch(color=freq_colors['mid'], label='0.2-0.8'),
                mpatches.Patch(color=freq_colors['high'], label='0.4-0.6')
            ]
            axes[idx, 5].legend(handles=legend_elements, loc='lower right', fontsize='small')

        # Save visualization
        filename = 'validation_samples.png'
        plt.tight_layout()
        plt.savefig(output_dir / filename, dpi=300, bbox_inches='tight')
        logger.info(f"Sample visualization saved: {output_dir / filename}")
        plt.close()

    def export_results_to_csv(self,
                             results: Dict[str, Dict],
                             output_path: Union[str, Path]):
        """Export evaluation results to CSV format"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        logger.info(f"Exporting results to {output_path}")

        # Convert results to DataFrame
        rows = []
        for interval, metrics in results.items():
            rows.append({
                'interval': interval,
                'sample_count': metrics['sample_count'],
                'total_pixels': metrics.get('total_pixels', 0),
                'accuracy': metrics['accuracy'],
                'f1_score': metrics['f1_score'],
                'iou': metrics['iou']
            })

        df = pd.DataFrame(rows)
        df.to_csv(output_path, index=False)

        logger.info(f"Results exported to {output_path}")
    def _plot_scatter_with_density(self, x: np.ndarray, y: np.ndarray, out_path: Path, title: str, x_label: str, y_label: str):
        import matplotlib.pyplot as plt

        # Compute point densities via 2D histogram binning
        bins = 100
        H, xedges, yedges = np.histogram2d(x, y, bins=bins)
        # Map each point to its bin count
        ix = np.clip(np.searchsorted(xedges, x, side='right') - 1, 0, bins - 1)
        iy = np.clip(np.searchsorted(yedges, y, side='right') - 1, 0, bins - 1)
        density = H[ix, iy].astype(float)
        if density.max() > 0:
            density_norm = (density - density.min()) / (density.max() - density.min() + 1e-12)
        else:
            density_norm = density

        # Linear fit y = a*x + b
        if len(x) >= 2:
            a, b = np.polyfit(x, y, 1)
            y_pred = a * x + b
            ss_res = float(np.sum((y - y_pred) ** 2))
            ss_tot = float(np.sum((y - np.mean(y)) ** 2))
            r2 = 1.0 - ss_res / ss_tot if ss_tot > 0 else np.nan
            mae = float(np.mean(np.abs(y - x)))
            rmse = float(np.sqrt(np.mean((y - x) ** 2)))
            # Relative metrics (per-sample relative error averaged; avoid div by zero)
            valid_rel = x > 0
            if np.any(valid_rel):
                mae_rel = float(np.mean(np.abs((y[valid_rel] - x[valid_rel]) / x[valid_rel])))
                rmse_rel = float(np.sqrt(np.mean(((y[valid_rel] - x[valid_rel]) / x[valid_rel]) ** 2)))
            else:
                # Fallback: relative to mean x
                denom = float(np.mean(x) + 1e-8)
                mae_rel = mae / denom
                rmse_rel = rmse / denom
        else:
            a = b = r2 = mae = rmse = mae_rel = rmse_rel = np.nan

        # Plot
        plt.figure(figsize=(7, 6))
        sc = plt.scatter(x, y, c=density_norm, cmap='jet', s=10, alpha=0.7, edgecolors='none')
        plt.xlabel(x_label)
        plt.ylabel(y_label)
        plt.title(title)
        cbar = plt.colorbar(sc)
        cbar.set_label('Point density (blue→red)')

        # Plot fitted line if available
        if not np.isnan(a):
            x_line = np.linspace(np.min(x), np.max(x), 100)
            y_line = a * x_line + b
            plt.plot(x_line, y_line, color='black', linewidth=2, label=f'y={a:.3f}x+{b:.3f}')
            plt.legend(loc='best')

            # Annotation box
            text = (
                f'R$^2$ = {r2:.3f}\n'
                f'Slope = {a:.3f}\n'
                f'Intercept = {b:.3f}\n'
                f'MAE = {mae:.2f} ({mae_rel*100:.1f}%)\n'
                f'RMSE = {rmse:.2f} ({rmse_rel*100:.1f}%)'
            )
            plt.gca().text(0.05, 0.95, text, transform=plt.gca().transAxes,
                           verticalalignment='top', bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray'))

        plt.tight_layout()
        out_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(out_path, dpi=300)
        plt.close()

    def compute_and_save_area_analysis(self,
                                       predictions: List[np.ndarray],
                                       ground_truths: List[np.ndarray],
                                       missing_masks: List[np.ndarray],
                                       sample_metadata: List[Dict],
                                       output_dir: Union[str, Path]):
        """
        Per-sample water area analysis using full image areas:
          - gt_area_px: ground truth water area over the full image (px)
          - simulated_area_px: water area in the simulated-gap image (i.e., GT with missing pixels removed)
          - pred_area_px: predicted water area over the full image (px)
        Export CSV and draw two scatter plots:
          1) GT area vs Simulated-gap area (sanity check of missing simulation)
          2) GT area vs Predicted (gap-filled) area
        """
        output_dir = Path(output_dir)
        rows = []
        gt_list = []
        pred_list = []
        sim_list = []

        for i in range(len(predictions)):
            pred = predictions[i]
            gt = ground_truths[i]
            mask = missing_masks[i]
            meta = sample_metadata[i] if i < len(sample_metadata) else {}

            # Binarize prediction and GT
            pred_bin = (pred >= 0.5)
            gt_bin = (gt >= 0.5)
            vmask = mask.astype(bool)  # True = missing

            # Areas in pixels
            gt_area = int(np.sum(gt_bin))
            pred_area = int(np.sum(pred_bin))
            # Simulated-gap image water area: remove missing pixels from GT
            sim_area = int(np.sum(gt_bin & (~vmask)))

            rows.append({
                'sample_idx': i,
                'file_path': meta.get('file_path', ''),
                'gt_area_px': gt_area,
                'simulated_area_px': sim_area,
                'pred_area_px': pred_area,
                'missing_ratio': float(np.mean(vmask))
            })
            gt_list.append(gt_area)
            sim_list.append(sim_area)
            pred_list.append(pred_area)

        # Save CSV
        df = pd.DataFrame(rows)
        out_csv = output_dir / 'area_per_sample.csv'
        output_dir.mkdir(parents=True, exist_ok=True)
        df.to_csv(out_csv, index=False)
        logger.info(f"Saved per-sample area CSV: {out_csv}")

        # Scatter 1: GT vs Simulated-gap areas
        x1 = np.asarray(gt_list, dtype=float)
        y1 = np.asarray(sim_list, dtype=float)
        self._plot_scatter_with_density(x1, y1, output_dir / 'gt_vs_simulated_gap.png',
                                        'GT vs Simulated-gap Water Area',
                                        'Ground truth water area (px)',
                                        'Simulated-gap water area (px)')

        # Scatter 2: GT vs Pred (gap-filled) areas
        x2 = np.asarray(gt_list, dtype=float)
        y2 = np.asarray(pred_list, dtype=float)
        self._plot_scatter_with_density(x2, y2, output_dir / 'gt_vs_pred.png',
                                        'GT vs Gap-filled Water Area',
                                        'Ground truth water area (px)',
                                        'Gap-filled water area (px)')


    def run_complete_validation(self, output_dir: Union[str, Path]) -> Dict:
        """
        Run complete validation workflow

        Args:
            output_dir: Directory to save all outputs

        Returns:
            Dictionary with all results and paths
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        if self.log_mode == 'detailed':
            logger.info(f"Starting complete validation workflow")
            logger.info(f"Output directory: {output_dir}")

        # Step 1: Load model
        self.load_model()

        # Step 2: Create validation dataset
        self.create_validation_dataset()

        # Step 3: Extract sample metadata
        sample_metadata = self.extract_sample_metadata()

        # Step 3.1: Generate basic geographical distribution plot for samples
        if self.log_mode == 'detailed':
            logger.info("Generating basic geographical distribution plot for validation samples...")
        basic_geo_plot_path = ""
        try:
            from .utils import visualize_geographical_distribution
            basic_geo_plot_path = visualize_geographical_distribution(
                samples=sample_metadata,
                output_dir=output_dir / 'sample_distributions',
                metric_name=None,  # No metric overlay, just sample locations
                metric_values=None,
                title_prefix="Validation"
            )
            if self.log_mode == 'detailed':
                logger.info(f"Basic geographical distribution plot saved to: {basic_geo_plot_path}")
        except Exception as e:
            logger.error(f"Failed to generate basic geographical distribution plot: {e}")

        # Step 4: Run inference
        predictions, ground_truths, missing_masks, water_frequencies = self.run_inference()

        # Step 5: Compute metrics by frequency intervals
        results = self.compute_metrics_by_frequency_intervals(
            predictions, ground_truths, missing_masks, water_frequencies
        )

        # Step 6: Compute individual sample metrics
        if self.log_mode == 'detailed':
            logger.info("Computing individual sample metrics...")
        sample_metrics = self.compute_sample_metrics(
            predictions, ground_truths, missing_masks, water_frequencies, sample_metadata
        )

        # Step 6.1: Compute area analysis and plots
        try:
            self.compute_and_save_area_analysis(
                predictions=predictions,
                ground_truths=ground_truths,
                missing_masks=missing_masks,
                sample_metadata=sample_metadata,
                output_dir=output_dir / 'area_analysis'
            )
        except Exception as e:
            logger.error(f"Area analysis failed: {e}")

        # Step 7: Export results to CSV
        csv_path = output_dir / 'validation_metrics.csv'
        self.export_results_to_csv(results, csv_path)

        # Step 8: Export sample metrics to CSV
        sample_csv_path = output_dir / 'sample_metrics.csv'
        self.save_sample_metrics_to_csv(sample_metrics, sample_csv_path)

        # Step 9: Generate geographical distribution plots for metrics
        if self.log_mode == 'detailed':
            logger.info("Generating geographical distribution plots for metrics...")
        geo_plot_paths = {}
        try:
            geo_plot_paths = self.save_multiple_metric_geographical_distributions(
                sample_metrics=sample_metrics,
                sample_metadata=sample_metadata,
                output_dir=output_dir / 'geographical_distributions',
                title_prefix="Validation"
            )
        except Exception as e:
            logger.error(f"Failed to generate geographical distribution plots: {e}")

        # Step 10: Generate sample visualizations
        if self.log_mode == 'detailed':
            logger.info("Generating sample visualizations...")
        try:
            self.save_sample_visualizations(
                predictions, ground_truths, missing_masks, water_frequencies,
                sample_metadata, output_dir / 'sample_visualizations'
            )
        except Exception as e:
            logger.error(f"Failed to generate sample visualizations: {e}")

        # Step 11: Generate visualization report
        if self.log_mode == 'detailed':
            logger.info("Generating visualization report...")
        try:
            # Use pre-computed accuracy values from sample_metrics
            accuracy_values = []
            for sample_metric in sample_metrics:
                acc_0_1 = sample_metric.get('accuracy_0_1')
                if acc_0_1 is not None:
                    accuracy_values.append(acc_0_1)
                else:
                    accuracy_values.append(0.0)

            # Generate comprehensive visualization report
            plot_paths = generate_comprehensive_report(
                samples=sample_metadata,
                output_dir=output_dir / 'visualizations',
                title_prefix="Validation",
                metric_name="Accuracy",
                metric_values=accuracy_values
            )

        except Exception as e:
            logger.error(f"Failed to generate visualization report: {e}")
            plot_paths = {}

        # Step 12: Save complete results summary
        summary = {
            'validation_info': {
                'model_path': str(self.model_path),
                'validation_samples_path': str(self.validation_samples_path),
                'total_samples': len(sample_metadata),
                'validation_timestamp': datetime.now().isoformat(),
                'device': str(self.device),
                'platform': self.platform,
                'batch_size': self.batch_size
            },
            'results_by_interval': results,
            'sample_metrics_summary': {
                'total_samples': len(sample_metrics),
                'samples_with_valid_pixels': len([s for s in sample_metrics if s['valid_pixels_count'] > 0])
            },
            'output_files': {
                'csv_results': str(csv_path),
                'sample_metrics_csv': str(sample_csv_path),
                'basic_geographical_distribution': basic_geo_plot_path,
                'visualization_plots': plot_paths,
                'geographical_distribution_plots': geo_plot_paths
            }
        }

        summary_path = output_dir / 'validation_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        logger.info(f"Complete validation summary saved to {summary_path}")

        # Minimal final summary log
        summary_lines = [
            "VALIDATION SUMMARY:",
            f"Model: {self.model_path.name}",
            f"Samples: {summary['validation_info']['total_samples']}",
        ]
        for interval, metrics in results.items():
            summary_lines.append(
                f"{interval}: Acc={metrics['accuracy']:.4f}, F1={metrics['f1_score']:.4f}, IoU={metrics['iou']:.4f}, "
                f"Samples={metrics['sample_count']}"
            )
        logger.info(" | ".join(summary_lines))

        return summary


def main():
    """Main function for running validation"""
    import argparse

    parser = argparse.ArgumentParser(description="Model Validation and Evaluation")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to trained model checkpoint")
    parser.add_argument("--validation_samples", type=str, required=True,
                       help="Path to validation samples JSON")
    parser.add_argument("--output_dir", type=str, required=True,
                       help="Output directory for results")
    parser.add_argument("--missing_db", type=str,
                       help="Path to missing pattern database")
    parser.add_argument("--config", type=str,
                       help="Path to model configuration")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device for inference (cuda, cuda:0, cuda:1, cpu)")
    parser.add_argument("--platform", type=str, default="a100",
                       help="Platform for inference")
    parser.add_argument("--batch_size", type=int, default=8,
                       help="Batch size for inference (adjust based on GPU memory)")
    parser.add_argument("--gpu_id", type=int, default=None,
                       help="Specific GPU ID to use (overrides device setting)")
    parser.add_argument("--optimize_gpu_memory", action="store_true", default=True,
                       help="Use optimized GPU memory management (accumulate on GPU before CPU transfer)")
    parser.add_argument("--traditional_inference", action="store_true", default=False,
                       help="Use traditional inference with immediate CPU transfers (disables GPU optimization)")
    parser.add_argument("--log_mode", type=str, default='minimal', choices=['minimal','detailed'],
                       help="Logging mode: 'minimal' (default) or 'detailed'")

    args = parser.parse_args()

    # Handle GPU ID override
    device = args.device
    if args.gpu_id is not None:
        device = f"cuda:{args.gpu_id}"
    log_mode = args.log_mode

    # Log system info
    # Determine GPU optimization setting
    optimize_gpu_memory = args.optimize_gpu_memory and not args.traditional_inference

    # Create validator
    validator = ModelValidator(
        model_path=args.model_path,
        validation_samples_path=args.validation_samples,
        missing_db_path=args.missing_db,
        config_path=args.config,
        device=device,
        platform=args.platform,
        batch_size=args.batch_size,
        optimize_gpu_memory=optimize_gpu_memory,
        log_mode=log_mode
    )

    # Run validation
    results = validator.run_complete_validation(args.output_dir)

    logger.info("Validation completed successfully!")
    return results


if __name__ == "__main__":
    main()