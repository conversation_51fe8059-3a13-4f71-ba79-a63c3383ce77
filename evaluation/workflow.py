"""
Complete Validation Workflow
============================

This module implements a complete validation workflow that integrates:
1. Sample loading and missing data simulation using sampler.py
2. Sample distribution analysis using utils.analyze_sample_distributions
3. Model validation and metrics calculation using validation.py
4. Results export to specified directory structure

The workflow consists of three main steps:
- Step 1: Load samples and apply missing simulation, export to /samples
- Step 2: Analyze sample variable distributions, export to /distribution_analysis
- Step 3: Run model validation with MetricsCalculator, export to /validation_metrics

Author: Claude Code Assistant
Date: 2025-08-03
"""

import sys
import json
import logging
from pathlib import Path
from typing import Dict, Optional
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from evaluation.sampler import ValidationSampler
from evaluation.validation import ModelValidator
from evaluation.utils import analyze_sample_distributions

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CompleteValidationWorkflow:
    """Complete validation workflow integrating sampling and validation"""
    
    def __init__(self,
                 input_validation_index: str,
                 model_path: str,
                 missing_db_path: str,
                 output_base_dir: str,
                 config_path: Optional[str] = None,
                 device: str = 'cuda',
                 batch_size: int = 64,
                 random_seed: int = 42,
                 average_mode: str = 'pixel'):
        """
        Initialize complete validation workflow

        Args:
            input_validation_index: Path to input validation index file
            model_path: Path to trained model checkpoint
            missing_db_path: Path to missing pattern database
            output_base_dir: Base output directory for all results
            config_path: Path to model configuration
            device: Device for inference
            batch_size: Batch size for inference
            random_seed: Random seed for reproducibility
            average_mode: Averaging mode for metrics calculation
        """
        self.input_validation_index = Path(input_validation_index)
        self.model_path = Path(model_path)
        self.missing_db_path = Path(missing_db_path)
        self.output_base_dir = Path(output_base_dir)
        self.config_path = config_path
        self.device = device
        self.batch_size = batch_size
        self.random_seed = random_seed
        self.average_mode = average_mode
        
        # Create output directories
        self.samples_dir = self.output_base_dir / "samples"
        self.distribution_analysis_dir = self.output_base_dir / "distribution_analysis"
        self.validation_metrics_dir = self.output_base_dir / "validation_metrics"
        
        # Initialize components
        self.sampler = None
        self.validator = None
        self.processed_samples_path = None
        
        logger.info(f"CompleteValidationWorkflow initialized")
        logger.info(f"Input validation index: {self.input_validation_index}")
        logger.info(f"Model path: {self.model_path}")
        logger.info(f"Missing DB path: {self.missing_db_path}")
        logger.info(f"Output base directory: {self.output_base_dir}")
        logger.info(f"Samples output: {self.samples_dir}")
        logger.info(f"Distribution analysis output: {self.distribution_analysis_dir}")
        logger.info(f"Validation metrics output: {self.validation_metrics_dir}")
    
    def validate_inputs(self):
        """Validate all input files and paths"""
        logger.info("Validating input files...")
        
        # Check input validation index
        if not self.input_validation_index.exists():
            raise FileNotFoundError(f"Input validation index not found: {self.input_validation_index}")
        
        # Check model path
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model checkpoint not found: {self.model_path}")
        
        # Check missing database
        if not self.missing_db_path.exists():
            raise FileNotFoundError(f"Missing pattern database not found: {self.missing_db_path}")
        
        # Check config if provided
        if self.config_path and not Path(self.config_path).exists():
            raise FileNotFoundError(f"Config file not found: {self.config_path}")
        
        logger.info("All input files validated successfully")
    
    def step1_sample_processing(self) -> str:
        """
        Step 1: Load samples and apply missing data simulation
        
        Returns:
            Path to processed samples file
        """
        logger.info("=" * 80)
        logger.info("STEP 1: SAMPLE PROCESSING WITH MISSING SIMULATION")
        logger.info("=" * 80)
        
        # Create samples output directory
        self.samples_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize sampler
        self.sampler = ValidationSampler(random_seed=self.random_seed)
        
        # Define output path for processed samples
        self.processed_samples_path = self.samples_dir / "processed_validation_samples.json"
        
        # Process validation index with missing simulation
        output_path = self.sampler.process(
            input_path=str(self.input_validation_index),
            output_path=str(self.processed_samples_path),
            missing_db_path=str(self.missing_db_path)
        )
        
        logger.info(f"Step 1 completed: Processed samples saved to {output_path}")
        return output_path

    def step2_analyze_distributions(self) -> str:
        """
        Step 2: Analyze sample variable distributions using utils.analyze_sample_distributions

        Returns:
            Path to distribution analysis plot
        """
        logger.info("=" * 80)
        logger.info("STEP 2: SAMPLE DISTRIBUTION ANALYSIS")
        logger.info("=" * 80)

        if not self.processed_samples_path or not self.processed_samples_path.exists():
            raise ValueError("Processed samples not found. Run step1_sample_processing first.")

        # Create distribution analysis output directory
        self.distribution_analysis_dir.mkdir(parents=True, exist_ok=True)

        # Load processed samples
        with open(self.processed_samples_path, 'r') as f:
            processed_data = json.load(f)

        # Extract samples list from the processed data
        if 'samples' in processed_data:
            samples = processed_data['samples']
        else:
            # If the file contains a list directly
            samples = processed_data

        logger.info(f"Analyzing distributions for {len(samples)} samples")

        # Run distribution analysis
        distribution_plot_path = analyze_sample_distributions(
            samples=samples,
            output_dir=self.distribution_analysis_dir,
            title_prefix="Validation",
            config_path="configs/config_v20.yaml"
        )

        logger.info(f"Step 2 completed: Distribution analysis saved to {distribution_plot_path}")
        return distribution_plot_path

    def step3_model_validation(self) -> Dict:
        """
        Step 3: Run model validation with MetricsCalculator

        Returns:
            Validation results dictionary
        """
        logger.info("=" * 80)
        logger.info("STEP 3: MODEL VALIDATION WITH METRICS CALCULATION")
        logger.info("=" * 80)
        
        if not self.processed_samples_path or not self.processed_samples_path.exists():
            raise ValueError("Processed samples not found. Run step1_sample_processing first.")
        
        # Create validation metrics output directory
        self.validation_metrics_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize model validator
        self.validator = ModelValidator(
            model_path=str(self.model_path),
            validation_samples_path=str(self.processed_samples_path),
            missing_db_path=str(self.missing_db_path),
            config_path=self.config_path,
            device=self.device,
            platform=self.platform,
            batch_size=self.batch_size,
            optimize_gpu_memory=False,
            average_mode=self.average_mode
        )
        
        # Run complete validation workflow
        validation_results = self.validator.run_complete_validation(
            output_dir=self.validation_metrics_dir
        )
        
        logger.info(f"Step 3 completed: Validation results saved to {self.validation_metrics_dir}")
        return validation_results
    
    def run_complete_workflow(self) -> Dict:
        """
        Run the complete validation workflow
        
        Returns:
            Complete workflow results
        """
        workflow_start_time = datetime.now()
        
        logger.info("=" * 80)
        logger.info("STARTING COMPLETE VALIDATION WORKFLOW")
        logger.info("=" * 80)
        logger.info(f"Workflow start time: {workflow_start_time}")
        
        try:
            # Validate inputs
            self.validate_inputs()
            
            # Step 1: Sample processing with missing simulation
            processed_samples_path = self.step1_sample_processing()

            # Step 2: Analyze sample variable distributions
            distribution_plot_path = self.step2_analyze_distributions()

            # Step 3: Model validation with metrics calculation
            validation_results = self.step3_model_validation()
            
            # Calculate workflow duration
            workflow_end_time = datetime.now()
            workflow_duration = workflow_end_time - workflow_start_time
            
            # Compile complete results
            complete_results = {
                'workflow_info': {
                    'start_time': workflow_start_time.isoformat(),
                    'end_time': workflow_end_time.isoformat(),
                    'duration_seconds': workflow_duration.total_seconds(),
                    'input_validation_index': str(self.input_validation_index),
                    'model_path': str(self.model_path),
                    'missing_db_path': str(self.missing_db_path),
                    'output_base_dir': str(self.output_base_dir),
                    'random_seed': self.random_seed
                },
                'step1_results': {
                    'processed_samples_path': processed_samples_path,
                    'samples_output_dir': str(self.samples_dir)
                },
                'step2_results': {
                    'distribution_plot_path': distribution_plot_path,
                    'distribution_analysis_dir': str(self.distribution_analysis_dir)
                },
                'step3_results': validation_results,
                'output_structure': {
                    'samples_dir': str(self.samples_dir),
                    'distribution_analysis_dir': str(self.distribution_analysis_dir),
                    'validation_metrics_dir': str(self.validation_metrics_dir)
                }
            }
            
            # Save complete workflow results
            workflow_summary_path = self.output_base_dir / "complete_workflow_summary.json"
            with open(workflow_summary_path, 'w') as f:
                json.dump(complete_results, f, indent=2, default=str)
            
            logger.info("=" * 80)
            logger.info("COMPLETE VALIDATION WORKFLOW FINISHED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"Total duration: {workflow_duration}")
            logger.info(f"Complete workflow summary: {workflow_summary_path}")
            logger.info(f"Samples directory: {self.samples_dir}")
            logger.info(f"Distribution analysis directory: {self.distribution_analysis_dir}")
            logger.info(f"Validation metrics directory: {self.validation_metrics_dir}")
            
            return complete_results
            
        except Exception as e:
            logger.error(f"Workflow failed with error: {e}")
            raise


def main():
    """Main function for running complete validation workflow"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Complete Validation Workflow")
    parser.add_argument("--input_index", type=str, required=True,
                       help="Path to input validation index file")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to trained model checkpoint")
    parser.add_argument("--missing_db", type=str, required=True,
                       help="Path to missing pattern database")
    parser.add_argument("--output_dir", type=str, required=True,
                       help="Base output directory for all results")
    parser.add_argument("--config", type=str, default="configs/config_v20.yaml",
                       help="Path to model configuration")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device for inference")
    parser.add_argument("--batch_size", type=int, default=64,
                       help="Batch size for inference")
    parser.add_argument("--random_seed", type=int, default=42,
                       help="Random seed for reproducibility")
    parser.add_argument("--average_mode", type=str, default='pixel', choices=['pixel','sample'],
                       help="Averaging mode for metrics: 'pixel' (micro) or 'sample' (macro)")

    args = parser.parse_args()

    # Create workflow
    workflow = CompleteValidationWorkflow(
        input_validation_index=args.input_index,
        model_path=args.model_path,
        missing_db_path=args.missing_db,
        output_base_dir=args.output_dir,
        config_path=args.config,
        device=args.device,
        batch_size=args.batch_size,
        random_seed=args.random_seed,
        average_mode=args.average_mode
    )
    
    # Run complete workflow
    results = workflow.run_complete_workflow()
    
    logger.info("Complete validation workflow finished successfully!")
    return results


if __name__ == "__main__":
    main()
