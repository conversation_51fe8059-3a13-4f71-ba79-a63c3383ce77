"""
Validation Sample Index Processing Module

This module adds simulated_missing_ratio field to validation samples.

Author: <PERSON> Assistant
Date: 2025-08-03
"""

import json
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional
import logging
import hashlib
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ValidationSampler:
    """Add simulated_missing_ratio field to validation samples"""

    def __init__(self, random_seed: int = 42):
        """Initialize validation sampler
        Args:
            random_seed: base random seed
        """
        self.random_seed = random_seed
        random.seed(self.random_seed)
        np.random.seed(self.random_seed)

    def load_validation_index(self, index_file_path: str) -> List[Dict]:
        """Load validation sample index file"""
        with open(index_file_path, 'r') as f:
            data = json.load(f)

        # Handle both list format and dict format
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return data.get('validation_samples', data.get('samples', []))
        else:
            raise ValueError("Invalid index file format")
    
    def add_missing_ratio(self, samples: List[Dict], missing_db_path: Optional[str] = None) -> List[Dict]:
        """Add simulated_missing_ratio field to samples"""
        if not missing_db_path or not Path(missing_db_path).exists():
            logger.warning("Missing pattern database not found, skipping simulation")
            return samples

        try:
            # Load missing pattern database
            missing_data = np.load(missing_db_path, allow_pickle=True)
            patterns = missing_data['patterns']

            # Add missing ratio to each sample
            for sample in samples:
                file_path = sample.get('file_path') or sample.get('source_file') or str(sample)

                seed_key = os.path.basename(file_path)
                # Use same random seed logic as dataset.py MissingPatternDatabase.get_random_pattern
                state = random.getstate()
                hash_object = hashlib.md5(seed_key.encode())
                hash_value = int(hash_object.hexdigest(), 16)
                random.seed(42 + hash_value)

                pattern_info = random.choice(patterns)
                random.setstate(state)

                sample['simulated_missing_ratio'] = float(pattern_info['missing_ratio'])

            return samples

        except Exception as e:
            logger.error(f"Failed to add missing ratio: {e}")
            return samples
    
    def save_samples(self, samples: List[Dict], output_path: str):
        """Save samples with simulated_missing_ratio field"""
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w') as f:
            json.dump(samples, f, indent=2)

        logger.info(f"Saved {len(samples)} samples to {output_path}")
    
    def process(self, input_path: str, output_path: str, missing_db_path: Optional[str] = None) -> str:
        """Process samples: load, add missing ratio, save"""
        samples = self.load_validation_index(input_path)
        samples_with_ratio = self.add_missing_ratio(samples, missing_db_path)
        self.save_samples(samples_with_ratio, output_path)
        return output_path


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description="Add simulated_missing_ratio to validation samples")
    parser.add_argument("--input", type=str, required=True, help="Input sample file",
                        default="/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/val_indices.json")
    parser.add_argument("--output", type=str, required=True, help="Output sample file", 
                        default="/mnt/storage/xiaozhen/Water/Results/validation/swin_waternet_v20_2/samples/processed_samples.json")
    parser.add_argument("--missing_db", type=str,
                       default="/mnt/storage/xiaozhen/Water/Sample/Sample4/missing_db.npz",
                       help="Missing pattern database")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")

    args = parser.parse_args()

    sampler = ValidationSampler(random_seed=args.seed)
    sampler.process(args.input, args.output, args.missing_db)


if __name__ == "__main__":
    main()
