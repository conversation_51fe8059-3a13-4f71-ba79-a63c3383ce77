import numpy as np
import torch
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.colors import LinearSegmentedColormap
import random
import matplotlib.colors as mcolors
import matplotlib.patches as mpatches
from types import SimpleNamespace

# ==============================================================================
# Helper utilities
# ==============================================================================

def prepare_visualization_data(last_train_batch: dict,
                               last_train_outputs: dict,
                               num_vis_samples: int,
                               freq_min: float,
                               freq_max: float
                               ):
    """Prepare the *input_data*, *output* and *config* objects required by
    :func:`save_visualization` from the raw *last_train_batch* and
    *last_train_outputs* produced during training.

    This logic was previously embedded in the training loop; extracting it
    here avoids duplication and keeps all visualization-related code in one
    place.

    Parameters
    ----------
    last_train_batch : dict
        Mini-batch dict as produced by the dataloader.
    last_train_outputs : dict
        Model outputs corresponding to *last_train_batch*.
    num_vis_samples : int
        Number of samples to visualize.
    freq_min : float
        Minimum water frequency for sample selection.
    freq_max : float
        Maximum water frequency for sample selection.

    Returns
    -------
    tuple
        ``(input_data, output)`` where *input_data* and *output* can be fed directly to :func:`save_visualization`.
    """

    # Select indices for visualisation -------------------------------------------------
    B = last_train_batch['input_sequence'].size(0)

    # Compute mean water frequency if requested
    if 'occurrence' in last_train_batch:
        mean_freq = last_train_batch['occurrence'].mean(dim=(1, 2))
    else:
        mean_freq = None

    if mean_freq is not None:
        valid = torch.nonzero((mean_freq >= freq_min) & (mean_freq <= freq_max), as_tuple=True)[0]
        if len(valid):
            perm = torch.randperm(len(valid))[:min(num_vis_samples, len(valid))]
            selected_indices = valid[perm]
        else:
            # 如果没有符合条件的样本，使用所有样本
            selected_indices = torch.arange(min(num_vis_samples, B))
    else:
        selected_indices = torch.arange(min(num_vis_samples, B))

    # Convenience slicer --------------------------------------------------------------
    _sel = lambda x: (x[selected_indices] if isinstance(x, torch.Tensor) else x)

    # Determine centre frame index for sequence inputs
    try:
        center_idx = last_train_batch['center_frame_idx'][0].item()
    except (KeyError, IndexError):
        # 如果没有center_frame_idx，使用序列的中间帧
        center_idx = last_train_batch['input_sequence'].size(1) // 2

    # ------------------------------------------------------------------
    # Assemble *input_data* dict expected by save_visualization
    # ------------------------------------------------------------------
    input_data = {
        'input': _sel(last_train_batch['input_sequence'][:, center_idx]),
        'target': _sel(last_train_batch['ground_truth']),
        'missing_mask': _sel(last_train_batch['missing_mask']),
        'occurrence': _sel(last_train_batch.get('occurrence', torch.zeros_like(last_train_batch['ground_truth'])).unsqueeze(1)),
        # Geographic & temporal info (optional)
        'tile_lon': _sel(last_train_batch.get('tile_lon', None)),
        'tile_lat': _sel(last_train_batch.get('tile_lat', None)),
        'year': _sel(last_train_batch.get('year', None)),
        'month': _sel(last_train_batch.get('month', None)),
    }

    # ------------------------------------------------------------------
    # Output map corresponding to the selected samples
    # ------------------------------------------------------------------
    output = _sel(last_train_outputs['inpaint']['logits'])

    return input_data, output

# ==============================================================================
# Main visualization routine
# ==============================================================================

def save_visualization(batch_input, batch_output, epoch, vis_config, logger=None):
    # 设置默认配置值
    default_config = {
        'enabled': True,
        'num_vis_samples': 4,
        'freq_min': 0.2,
        'freq_max': 0.8,
        'vis_dir': Path('visualizations')
    }
    
    # 合并配置，使用默认值填充缺失的键
    if isinstance(vis_config, dict):
        for key, default_value in default_config.items():
            if key not in vis_config:
                vis_config[key] = default_value
    else:
        vis_config = default_config
    
    if not vis_config['enabled']:
        return
        
    try:
        num_vis_samples = vis_config['num_vis_samples']
        freq_min = vis_config['freq_min']
        freq_max = vis_config['freq_max']
        input_data, output = prepare_visualization_data(batch_input, batch_output, num_vis_samples, freq_min, freq_max)
        
        vis_dir = vis_config['vis_dir']
        vis_dir.mkdir(parents=True, exist_ok=True)
        num_samples = min(vis_config['num_vis_samples'], input_data['input'].size(0))
        sample_indices = random.sample(range(input_data['input'].size(0)), num_samples)
        
        # 修复单样本可视化问题
        if num_samples == 1:
            fig, axes = plt.subplots(1, 5, figsize=(20, 4))
            axes = axes.reshape(1, -1)  # 确保是二维数组
        else:
            fig, axes = plt.subplots(num_samples, 5, figsize=(20, 4*num_samples))
            
        fig.suptitle(f'Epoch {epoch+1} - Multiple Samples', fontsize=16)
        
        # 连续色彩映射
        colors_yellow_blue = [(1, 1, 0), (0, 0, 1)]
        colors_white_red = [(1, 1, 1), (1, 0, 0)]
        n_bins = 100
        yellow_blue_cmap = LinearSegmentedColormap.from_list("custom_yb", colors_yellow_blue, N=n_bins)
        white_red_cmap = LinearSegmentedColormap.from_list("custom_wr", colors_white_red, N=n_bins)
        
        # 创建水体频率分层的颜色映射
        freq_colors = {
            'background': '#FFFFFF',  # 白色背景（非水体）
            'low': '#A6CEE3',         # 浅蓝色 (0-0.2)
            'mid': '#1F78B4',         # 中蓝色 (0.2-0.4, 0.6-0.8)
            'high': '#08306B'         # 深蓝色 (0.4-0.6)
        }
        
        for idx, sample_idx in enumerate(sample_indices):
            input_vis = prepare_input_visualization(input_data['input'][sample_idx])
            pred = torch.sigmoid(output[sample_idx]).cpu().numpy()
            if pred.ndim == 3:
                pred = pred[1] if pred.shape[0] > 1 else pred[0]
            elif pred.ndim == 1:
                pred = pred.reshape(input_vis.shape[0], input_vis.shape[1])
            target = input_data['target'][sample_idx].cpu().numpy()
            mask = input_data['missing_mask'][sample_idx].cpu().numpy()
            water_freq = input_data['occurrence'][sample_idx].cpu().numpy()
            if water_freq.ndim == 3:
                water_freq = water_freq.squeeze(0)
                                
            # 获取地理和时间信息（如果可用）
            geo_temp_info = ""
            if all(k in input_data for k in ['tile_lon', 'tile_lat', 'year', 'month']):
                lon = input_data['tile_lon'][sample_idx].item() if isinstance(input_data['tile_lon'], torch.Tensor) else input_data['tile_lon'][sample_idx]
                lat = input_data['tile_lat'][sample_idx].item() if isinstance(input_data['tile_lat'], torch.Tensor) else input_data['tile_lat'][sample_idx]
                year = input_data['year'][sample_idx].item() if isinstance(input_data['year'], torch.Tensor) else input_data['year'][sample_idx]
                month = input_data['month'][sample_idx].item() if isinstance(input_data['month'], torch.Tensor) else input_data['month'][sample_idx]
                geo_temp_info = f"Lon: {lon:.2f}, Lat: {lat:.2f}\nYear: {year}, Month: {month}"
            
            # 输入图像
            input_rgb = np.ones((input_vis.shape[0], input_vis.shape[1], 3))
            water_mask = input_vis[:, :, 0] > 0.5
            missing_mask = input_vis[:, :, 2] > 0.5
            input_rgb[water_mask] = [0, 0, 1]
            input_rgb[missing_mask] = [0.7, 0.7, 0.7]
            axes[idx, 0].imshow(input_rgb)
            title = f'Sample {sample_idx} - Input'
            if geo_temp_info:
                axes[idx, 0].set_title(title + "\n" + geo_temp_info, fontsize=10)
            else:
                axes[idx, 0].set_title(title)
            axes[idx, 0].axis('off')
            
            # 预测结果
            im1 = axes[idx, 1].imshow(pred, cmap=yellow_blue_cmap, vmin=0, vmax=1)
            axes[idx, 1].set_title('Prediction')
            axes[idx, 1].axis('off')
            plt.colorbar(im1, ax=axes[idx, 1], fraction=0.046)
            
            # 真实标签
            im2 = axes[idx, 2].imshow(target, cmap=yellow_blue_cmap, vmin=0, vmax=1)
            axes[idx, 2].set_title('Ground Truth')
            axes[idx, 2].axis('off')
            plt.colorbar(im2, ax=axes[idx, 2], fraction=0.046)
            
            # 误差图
            error = np.abs(pred - target)
            im3 = axes[idx, 3].imshow(error, cmap=white_red_cmap, vmin=0, vmax=1)
            axes[idx, 3].set_title('Error')
            axes[idx, 3].axis('off')
            plt.colorbar(im3, ax=axes[idx, 3], fraction=0.046)
                                    
            # 水体频率分层设色图
            water_freq_layered = np.zeros((water_freq.shape[0], water_freq.shape[1], 3))
            
            # 创建分层颜色掩码
            mask_0_1 = water_freq > 0  # 0-1范围
            mask_0_2_0_8 = (water_freq >= 0.2) & (water_freq <= 0.8)  # 0.2-0.8范围
            mask_0_4_0_6 = (water_freq >= 0.4) & (water_freq <= 0.6)  # 0.4-0.6范围
            
            # 转换十六进制颜色为RGB
            rgb_bg = mcolors.hex2color(freq_colors['background'])
            rgb_low = mcolors.hex2color(freq_colors['low'])
            rgb_mid = mcolors.hex2color(freq_colors['mid'])
            rgb_high = mcolors.hex2color(freq_colors['high'])
            
            # 设置默认背景色
            water_freq_layered[:] = rgb_bg
            
            # 按频率范围填充颜色（顺序很重要，后面的会覆盖前面的）
            water_freq_layered[mask_0_1] = rgb_low
            water_freq_layered[mask_0_2_0_8] = rgb_mid
            water_freq_layered[mask_0_4_0_6] = rgb_high
            
            axes[idx, 4].imshow(water_freq_layered)
            axes[idx, 4].set_title('Frequency Layers')
            axes[idx, 4].axis('off')
            
            # 添加图例
            legend_elements = [
                mpatches.Patch(color=freq_colors['low'], label='0.0-0.2, 0.8-1.0'),
                mpatches.Patch(color=freq_colors['mid'], label='0.2-0.8'),
                mpatches.Patch(color=freq_colors['high'], label='0.4-0.6')
            ]
            axes[idx, 4].legend(handles=legend_elements, loc='lower right', fontsize='small')
            
        filename = f'predictions_epoch_{epoch+1}.png'
        plt.tight_layout()
        plt.savefig(vis_dir / filename, dpi=300, bbox_inches='tight')
        if logger:
            logger.info(f"Visualization saved: {vis_dir / filename}")
        plt.close()
    except Exception as e:
        if logger:
            logger.warning(f"Failed to save visualization: {e}")
        else:
            print(f"Warning: Failed to save visualization: {e}")
        plt.close('all')

def prepare_input_visualization(input_tensor):
    vis = torch.zeros((3, *input_tensor.shape[1:]), dtype=torch.float32)
    vis[0] = input_tensor[0]
    vis[1] = 1 - input_tensor[0]
    vis[2] = input_tensor[1]
    return vis.permute(1, 2, 0).cpu().numpy() 


def test_visualization():
    """测试可视化功能是否正常工作"""
    import torch
    
    # 创建模拟数据
    batch_size = 4
    img_size = 64  # 使用较小的尺寸进行测试
    
    # 模拟训练批次数据
    mock_batch = {
        'input_sequence': torch.randn(batch_size, 5, 2, img_size, img_size),  # [B, T, C, H, W]
        'ground_truth': torch.rand(batch_size, img_size, img_size),
        'missing_mask': torch.rand(batch_size, img_size, img_size) > 0.5,
        'occurrence': torch.rand(batch_size, img_size, img_size),
        'center_frame_idx': torch.tensor([2]),  # 中间帧
        'tile_lon': torch.rand(batch_size) * 360 - 180,
        'tile_lat': torch.rand(batch_size) * 180 - 90,
        'year': torch.randint(1984, 2025, (batch_size,)),
        'month': torch.randint(1, 13, (batch_size,))
    }
    
    # 模拟模型输出
    mock_outputs = {
        'inpaint': {
            'logits': torch.randn(batch_size, 1, img_size, img_size),
        }
    }
    
    # 测试配置
    test_config = {
        'enabled': True,
        'num_vis_samples': 2,
        'freq_min': 0.0,
        'freq_max': 1.0,
        'vis_dir': Path('test_visualizations')
    }
    
    try:
        # 测试数据准备
        input_data, output = prepare_visualization_data(
            mock_batch, mock_outputs, 2, 0.0, 1.0
        )
        print("✓ Data preparation successful")
        
        # 测试可视化保存
        save_visualization(mock_batch, mock_outputs, 0, test_config)
        print("✓ Visualization saved successfully")
        
        # 检查文件是否创建
        vis_file = test_config['vis_dir'] / 'predictions_epoch_1.png'
        if vis_file.exists():
            print(f"✓ Visualization file created: {vis_file}")
        else:
            print(f"✗ Visualization file not found: {vis_file}")
            
    except Exception as e:
        print(f"✗ Visualization test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_visualization() 