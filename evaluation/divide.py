#!/usr/bin/env python3
"""
样本数据划分脚本
按照四维bins网格划分样本数据，并生成训练和验证索引
"""

import json
import numpy as np
import yaml
from pathlib import Path
from collections import defaultdict
import random
from typing import Dict, List, Tuple, Any

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def load_samples(samples_path: str) -> List[Dict[str, Any]]:
    """加载样本数据"""
    with open(samples_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 只提取 no_missing_samples
    all_samples = []

    if 'no_missing_samples' in data:
        for file_samples in data['no_missing_samples'].values():
            if isinstance(file_samples, list):
                all_samples.extend(file_samples)

    return all_samples

def get_bin_index(value: float, bins: List[float]) -> int:
    """获取值在bins中的索引"""
    for i in range(len(bins) - 1):
        if bins[i] <= value < bins[i + 1]:
            return i
    # 处理边界情况（最大值）
    if value == bins[-1]:
        return len(bins) - 2
    return -1  # 超出范围

def create_2d_bins(samples: List[Dict[str, Any]], config: Dict[str, Any]) -> Dict[Tuple[int, int], List[int]]:
    """创建二维bins网格并分配样本"""
    feature_bins = config['sampling']['feature_bins']

    water_proportion_bins = feature_bins['water_proportion_bins']
    mean_water_frequency_bins = feature_bins['mean_water_frequency_bins']

    # 创建二维网格
    bins_grid = defaultdict(list)

    print(f"开始处理 {len(samples)} 个样本...")

    for idx, sample in enumerate(samples):
        # 提取两个变量
        water_proportion = sample['water_proportion']
        mean_water_frequency = sample['mean_water_frequency']

        # 获取每个变量在对应bins中的索引
        wp_idx = get_bin_index(water_proportion, water_proportion_bins)
        mwf_idx = get_bin_index(mean_water_frequency, mean_water_frequency_bins)

        # 检查是否所有索引都有效
        if wp_idx >= 0 and mwf_idx >= 0:
            bin_key = (wp_idx, mwf_idx)
            bins_grid[bin_key].append(idx)

    print(f"样本分配到 {len(bins_grid)} 个网格中")

    return bins_grid

def find_minimum_samples(bins_grid: Dict[Tuple[int, int], List[int]]) -> int:
    """找出网格中最小的样本数量"""
    if not bins_grid:
        return 0

    min_samples = min(len(samples) for samples in bins_grid.values())
    print(f"网格中最小样本数量: {min_samples}")

    # 打印每个网格的样本数量统计
    sample_counts = [len(samples) for samples in bins_grid.values()]
    print(f"样本数量统计: 最小={min(sample_counts)}, 最大={max(sample_counts)}, 平均={np.mean(sample_counts):.1f}")

    return min_samples

def extract_balanced_samples(bins_grid: Dict[Tuple[int, int], List[int]],
                           min_samples: int) -> List[int]:
    """从每个网格中提取相同数量的样本"""
    balanced_indices = []

    for sample_indices in bins_grid.values():
        if len(sample_indices) >= min_samples:
            # 随机选择min_samples个样本
            selected = random.sample(sample_indices, min_samples)
            balanced_indices.extend(selected)
        else:
            # 如果样本不足，使用所有样本
            balanced_indices.extend(sample_indices)

    print(f"平衡后总样本数量: {len(balanced_indices)}")
    return balanced_indices

def split_train_validation(balanced_indices: List[int],
                         samples: List[Dict[str, Any]],
                         target_train: int = 100000,
                         target_val: int = 10000) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """划分训练和验证样本"""
    total_samples = len(balanced_indices)

    # 随机打乱索引
    shuffled_indices = balanced_indices.copy()
    random.shuffle(shuffled_indices)

    if total_samples >= target_train + target_val:
        # 样本充足，使用目标数量
        train_indices = shuffled_indices[:target_train]
        val_indices = shuffled_indices[target_train:target_train + target_val]
        print(f"样本充足: 训练样本 {len(train_indices)}, 验证样本 {len(val_indices)}")
    else:
        # 样本不足，使用90%/10%比例
        train_size = int(total_samples * 0.9)
        train_indices = shuffled_indices[:train_size]
        val_indices = shuffled_indices[train_size:]
        print(f"样本不足: 训练样本 {len(train_indices)} (90%), 验证样本 {len(val_indices)} (10%)")

    # 根据索引提取实际样本数据
    train_samples = [samples[idx] for idx in train_indices]
    val_samples = [samples[idx] for idx in val_indices]

    return train_samples, val_samples

def save_samples(train_samples: List[Dict[str, Any]], val_samples: List[Dict[str, Any]], output_dir: str):
    """保存训练和验证样本，格式与val_indices.json一致"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 为训练样本添加is_no_missing字段
    train_data = []
    for sample in train_samples:
        sample_copy = sample.copy()
        sample_copy["is_no_missing"] = True
        train_data.append(sample_copy)

    # 为验证样本添加is_no_missing字段
    val_data = []
    for sample in val_samples:
        sample_copy = sample.copy()
        sample_copy["is_no_missing"] = True
        val_data.append(sample_copy)

    # 保存训练样本
    train_path = output_path / "train_indices.json"
    with open(train_path, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, indent=2)

    # 保存验证样本
    val_path = output_path / "validation_indices.json"
    with open(val_path, 'w', encoding='utf-8') as f:
        json.dump(val_data, f, indent=2)

    print(f"样本已保存到: {output_path}")
    print(f"训练样本: {train_path}")
    print(f"验证样本: {val_path}")

    # 打印统计信息
    print(f"训练样本数量: {len(train_data)}")
    print(f"验证样本数量: {len(val_data)}")

def main():
    """主函数"""
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)

    # 文件路径
    samples_path = "/mnt/storage/xiaozhen/Water/Sample/Sample4/samples.json"
    config_path = "/home/<USER>/Water/configs/config_v20.yaml"
    output_dir = "/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/divide/"

    print("开始样本数据划分...")

    # 加载配置和样本数据
    print("加载配置文件...")
    config = load_config(config_path)

    print("加载样本数据...")
    samples = load_samples(samples_path)
    print(f"总样本数量: {len(samples)}")

    # 创建二维bins网格
    print("创建二维bins网格...")
    bins_grid = create_2d_bins(samples, config)

    # 找出最小样本数量
    min_samples = find_minimum_samples(bins_grid)

    if min_samples == 0:
        print("错误: 没有有效的样本分配到网格中")
        return

    # 提取平衡样本
    print("提取平衡样本...")
    balanced_indices = extract_balanced_samples(bins_grid, min_samples)

    # 划分训练和验证集
    print("划分训练和验证集...")
    train_samples, val_samples = split_train_validation(balanced_indices, samples)

    # 保存样本
    print("保存样本文件...")
    save_samples(train_samples, val_samples, output_dir)

    print("样本数据划分完成!")

if __name__ == "__main__":
    main()