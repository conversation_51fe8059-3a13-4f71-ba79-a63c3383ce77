import torch
import numpy as np
from torch import autocast
from typing import Dict, Union
from evaluation.visualization import save_visualization

class MetricsCalculator:
    """Handles metrics calculation with numerical stability"""
    @staticmethod
    def calculate_metrics(pred: torch.Tensor, target: torch.Tensor, valid_mask: torch.Tensor, water_frequency: torch.Tensor) -> Dict[str, float]:
        import logging
        logger = logging.getLogger(__name__)

        # Check if model outputs are all zeros or empty
        if pred.numel() == 0:
            logger.warning("Model output is empty - no predictions generated")
            
        water_freq_sq = water_frequency.squeeze(1)

        water_freq_mask_0_1 = torch.logical_and(water_freq_sq >= 0.0, water_freq_sq <= 1.0)
        water_freq_mask_0_2_0_8 = torch.logical_and(water_freq_sq >= 0.2, water_freq_sq <= 0.8)
        water_freq_mask_0_4_0_6 = torch.logical_and(water_freq_sq >= 0.4, water_freq_sq <= 0.6)

        metrics_0_1 = MetricsCalculator._calculate_single_range_metrics(pred, target, valid_mask, water_freq_mask_0_1)
        metrics_0_2_0_8 = MetricsCalculator._calculate_single_range_metrics(pred, target, valid_mask, water_freq_mask_0_2_0_8)
        metrics_0_4_0_6 = MetricsCalculator._calculate_single_range_metrics(pred, target, valid_mask, water_freq_mask_0_4_0_6)

        return {
            'accuracy_0_1': metrics_0_1['accuracy'],
            'precision_0_1': metrics_0_1['precision'],
            'recall_0_1': metrics_0_1['recall'],
            'f1_score_0_1': metrics_0_1['f1_score'],
            'iou_0_1': metrics_0_1['iou'],
            'accuracy_0_2_0_8': metrics_0_2_0_8['accuracy'],
            'precision_0_2_0_8': metrics_0_2_0_8['precision'],
            'recall_0_2_0_8': metrics_0_2_0_8['recall'],
            'f1_score_0_2_0_8': metrics_0_2_0_8['f1_score'],
            'iou_0_2_0_8': metrics_0_2_0_8['iou'],
            'accuracy_0_4_0_6': metrics_0_4_0_6['accuracy'],
            'precision_0_4_0_6': metrics_0_4_0_6['precision'],
            'recall_0_4_0_6': metrics_0_4_0_6['recall'],
            'f1_score_0_4_0_6': metrics_0_4_0_6['f1_score'],
            'iou_0_4_0_6': metrics_0_4_0_6['iou'],
        }
    @staticmethod
    def _calculate_single_range_metrics(pred: torch.Tensor, target: torch.Tensor, valid_mask: torch.Tensor, water_freq_mask: torch.Tensor) -> Dict[str, float]:
        combined_mask =  torch.logical_and(valid_mask, water_freq_mask)
        pred_masked = pred[combined_mask]
        target_masked = target[combined_mask]

        # Check if there are no valid pixels
        if combined_mask.sum().item() == 0:
            # logger.warning("No valid pixels found in combined mask - all metrics will be NaN")
            return {'accuracy': float('nan'), 'precision': float('nan'), 'recall': float('nan'), 'f1_score': float('nan'), 'iou': float('nan')}

        # 数值稳定性保护：确保输入张量没有NaN或inf
        pred_masked = torch.nan_to_num(pred_masked, nan=False)
        target_masked = torch.nan_to_num(target_masked, nan=False)

        correct = (pred_masked == target_masked).sum().item()
        total = len(pred_masked)
        accuracy = correct / total if total > 0 else np.nan

        tp = (pred_masked & target_masked).sum().item()
        fp = (pred_masked & ~target_masked).sum().item()
        fn = (~pred_masked & target_masked).sum().item()

        # 改进的F1计算：处理边界情况
        if (tp + fp) > 0:
            precision = tp / (tp + fp)
        else:
            precision = np.nan  # 当没有预测正例时，precision为0而不是nan

        if (tp + fn) > 0:
            recall = tp / (tp + fn)
        else:
            recall = np.nan  # 当没有真实正例时，recall为0而不是nan

        # 改进的F1计算：避免除零错误
        if precision + recall > 0:
            f1_score = 2 * precision * recall / (precision + recall)
        else:
            f1_score = np.nan  # 当precision和recall都为0时，F1为0而不是nan

        union = tp + fp + fn
        iou = tp / union if union > 0 else np.nan  # 当union为0时，IoU为0而不是nan

        return {'accuracy': accuracy, 'precision': precision, 'recall': recall, 'f1_score': f1_score, 'iou': iou}
